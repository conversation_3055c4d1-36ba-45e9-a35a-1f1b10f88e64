#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的字段覆盖测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    print("开始简单字段覆盖测试...")
    
    try:
        # 测试字段覆盖函数
        from core.crawler import _apply_field_selectors, _get_field_selectors, _field_selector_overrides
        
        print("✅ 函数导入成功")
        
        # 测试配置
        test_configs = {
            'title': {
                'selectors': ['.custom-title', '.my-title']
            },
            'content': {
                'selectors': ['.custom-content', '.my-content']
            }
        }
        
        print(f"测试配置: {list(test_configs.keys())}")
        
        # 应用配置
        _apply_field_selectors(test_configs)
        
        print(f"覆盖字段: {list(_field_selector_overrides.keys())}")
        
        # 测试获取
        title_selectors = _get_field_selectors('title', ['default-title'])
        print(f"title选择器: {title_selectors}")
        
        content_selectors = _get_field_selectors('content', ['default-content'])
        print(f"content选择器: {content_selectors}")
        
        # 测试未覆盖的字段
        other_selectors = _get_field_selectors('other', ['default-other'])
        print(f"other选择器: {other_selectors}")
        
        # 验证结果
        title_correct = title_selectors == ['.custom-title', '.my-title']
        content_correct = content_selectors == ['.custom-content', '.my-content']
        other_correct = other_selectors == ['default-other']
        
        print(f"title覆盖: {'✅' if title_correct else '❌'}")
        print(f"content覆盖: {'✅' if content_correct else '❌'}")
        print(f"other默认: {'✅' if other_correct else '❌'}")
        
        if title_correct and content_correct and other_correct:
            print("🎉 字段覆盖功能正常！")
        else:
            print("❌ 字段覆盖功能有问题")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
