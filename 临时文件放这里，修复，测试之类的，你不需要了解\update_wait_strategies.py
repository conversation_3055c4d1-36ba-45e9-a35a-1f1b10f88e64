#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新项目中的等待策略
将所有使用旧等待机制的地方更新为使用新的健壮等待策略
"""

import os
import re
import sys
from typing import List, Dict, Tuple

def find_goto_patterns(file_path: str) -> List[Tuple[int, str]]:
    """查找文件中的 page.goto 模式"""
    patterns = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for i, line in enumerate(lines, 1):
            # 查找 page.goto 调用
            if 'page.goto' in line and ('timeout' in line or 'wait_until' in line):
                patterns.append((i, line.strip()))
    
    except Exception as e:
        print(f"读取文件失败 {file_path}: {e}")
    
    return patterns

def scan_project_files() -> Dict[str, List[Tuple[int, str]]]:
    """扫描项目文件，查找需要更新的等待策略"""
    
    # 需要扫描的文件模式
    target_files = []
    
    # 扫描核心模块
    for root, dirs, files in os.walk('.'):
        # 跳过不需要的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
        
        for file in files:
            if file.endswith('.py') and not file.startswith('test_'):
                file_path = os.path.join(root, file)
                target_files.append(file_path)
    
    results = {}
    
    for file_path in target_files:
        patterns = find_goto_patterns(file_path)
        if patterns:
            results[file_path] = patterns
    
    return results

def generate_update_suggestions(scan_results: Dict[str, List[Tuple[int, str]]]) -> List[str]:
    """生成更新建议"""
    suggestions = []
    
    suggestions.append("# 等待策略更新建议")
    suggestions.append("# " + "=" * 50)
    suggestions.append("")
    
    if not scan_results:
        suggestions.append("✅ 未发现需要更新的等待策略模式")
        return suggestions
    
    for file_path, patterns in scan_results.items():
        suggestions.append(f"## 文件: {file_path}")
        suggestions.append("")
        
        for line_num, line_content in patterns:
            suggestions.append(f"**行 {line_num}:**")
            suggestions.append(f"```python")
            suggestions.append(f"# 当前代码:")
            suggestions.append(f"{line_content}")
            suggestions.append(f"")
            suggestions.append(f"# 建议更新为:")
            
            # 生成更新建议
            if 'mp.weixin.qq.com' in line_content:
                suggestions.append(f"# 微信公众号使用健壮等待策略")
                suggestions.append(f"success = await robust_goto(page, url)")
            else:
                suggestions.append(f"# 使用健壮等待策略")
                suggestions.append(f"success = await robust_goto(page, url)")
            
            suggestions.append(f"if not success:")
            suggestions.append(f"    # 处理访问失败的情况")
            suggestions.append(f"    logger.error(f'页面访问失败: {{url}}')")
            suggestions.append(f"    return False")
            suggestions.append(f"```")
            suggestions.append("")
        
        suggestions.append("")
    
    return suggestions

def create_migration_guide():
    """创建迁移指南"""
    guide = [
        "# 健壮等待策略迁移指南",
        "",
        "## 概述",
        "本指南帮助您将项目中的等待策略更新为新的健壮等待机制。",
        "",
        "## 新的健壮等待策略特性",
        "- 🔄 渐进式等待: networkidle -> domcontentloaded -> load -> commit",
        "- 🎯 网站特定配置: 针对不同网站优化参数",
        "- 📊 成功率统计: 记录各策略的成功率",
        "- 🛡️ 错误恢复: 自动尝试多种策略",
        "",
        "## 使用方法",
        "",
        "### 1. 导入健壮等待策略",
        "```python",
        "from utils.robust_wait_strategy import robust_goto",
        "```",
        "",
        "### 2. 替换原有的 page.goto 调用",
        "```python",
        "# 原有方式",
        "await page.goto(url, timeout=90000, wait_until='networkidle')",
        "await page.wait_for_load_state('networkidle', timeout=45000)",
        "",
        "# 新方式",
        "success = await robust_goto(page, url)",
        "if not success:",
        "    # 处理失败情况",
        "    return False",
        "```",
        "",
        "### 3. 自定义配置",
        "```python",
        "# 使用自定义配置",
        "success = await robust_goto(",
        "    page, ",
        "    url,",
        "    timeout=60000,",
        "    preferred_strategy='domcontentloaded',",
        "    extra_wait=3.0",
        ")",
        "```",
        "",
        "## 网站特定配置",
        "",
        "健壮等待策略会根据URL自动选择最适合的配置：",
        "",
        "- **微信公众号** (mp.weixin.qq.com): 使用 domcontentloaded + 5秒等待",
        "- **政府网站** (*.gov.cn): 使用 networkidle + 2秒等待",
        "- **默认配置**: 使用 networkidle + 2秒等待",
        "",
        "## 错误处理",
        "",
        "```python",
        "success = await robust_goto(page, url)",
        "if not success:",
        "    logger.error(f'所有等待策略都失败: {url}')",
        "    # 记录失败URL或采取其他措施",
        "    await save_failed_url(url, '页面访问失败')",
        "    return False",
        "```",
        "",
        "## 成功率监控",
        "",
        "```python",
        "from utils.robust_wait_strategy import get_success_stats",
        "",
        "# 获取成功率统计",
        "stats = get_success_stats()",
        "for domain, strategies in stats.items():",
        "    print(f'{domain}: {strategies}')",
        "```",
        "",
        "## 注意事项",
        "",
        "1. **向后兼容**: 如果健壮等待策略不可用，会自动回退到原有方式",
        "2. **性能影响**: 新策略可能会增加页面加载时间，但提高成功率",
        "3. **日志记录**: 建议启用详细日志以监控策略效果",
        "4. **测试验证**: 更新后请运行测试确保功能正常",
        "",
        "## 测试",
        "",
        "运行测试脚本验证新策略:",
        "```bash",
        "python test_robust_wait_strategy.py",
        "```"
    ]
    
    return guide

def main():
    """主函数"""
    print("🔍 扫描项目中的等待策略...")
    
    # 扫描项目文件
    scan_results = scan_project_files()
    
    print(f"📊 扫描完成，发现 {len(scan_results)} 个文件包含等待策略")
    
    # 生成更新建议
    suggestions = generate_update_suggestions(scan_results)
    
    # 保存更新建议
    with open('wait_strategy_update_suggestions.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(suggestions))
    
    print("📝 更新建议已保存到: wait_strategy_update_suggestions.md")
    
    # 创建迁移指南
    guide = create_migration_guide()
    
    with open('robust_wait_strategy_migration_guide.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(guide))
    
    print("📖 迁移指南已保存到: robust_wait_strategy_migration_guide.md")
    
    # 显示扫描结果摘要
    if scan_results:
        print("\n📋 发现的等待策略模式:")
        for file_path, patterns in scan_results.items():
            print(f"  📄 {file_path}: {len(patterns)} 个模式")
    
    print("\n✅ 扫描和建议生成完成！")
    print("\n📌 下一步:")
    print("1. 查看 wait_strategy_update_suggestions.md 了解需要更新的地方")
    print("2. 参考 robust_wait_strategy_migration_guide.md 进行迁移")
    print("3. 运行 test_robust_wait_strategy.py 测试新策略")

if __name__ == "__main__":
    main()
