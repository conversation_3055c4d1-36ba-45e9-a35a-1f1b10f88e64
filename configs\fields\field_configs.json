{"default_fields": {"dateget": {"name": "发布日期", "type": "text", "selectors": [".date", ".publish-date", ".article-date", "time", ".time"], "required": false, "default": "", "extractor": "date_extractor", "description": "文章发布日期"}, "source": {"name": "来源", "type": "text", "selectors": [".source", ".author", ".publisher", ".from"], "required": false, "default": "本站", "extractor": "text_extractor", "description": "文章来源或作者"}, "title": {"name": "标题", "type": "text", "selectors": ["h1", ".title", ".article-title", "h2", ".news-title"], "required": true, "default": "", "extractor": "text_extractor", "description": "文章标题"}, "articlelink": {"name": "文章链接", "type": "url", "selectors": [], "required": true, "default": "", "extractor": "url_extractor", "description": "文章URL链接"}, "content": {"name": "内容", "type": "html", "selectors": [".content", ".article-content", ".main-text"], "required": true, "default": "", "extractor": "content_extractor", "description": "文章正文内容"}, "classid": {"name": "分类ID", "type": "text", "selectors": [], "required": false, "default": "", "extractor": "static_extractor", "description": "文章分类标识"}, "city": {"name": "城市", "type": "text", "selectors": [], "required": false, "default": "上海", "extractor": "static_extractor", "description": "文章所属城市"}, "getdate": {"name": "采集时间", "type": "datetime", "selectors": [], "required": false, "default": "", "extractor": "timestamp_extractor", "description": "数据采集时间"}}, "extended_fields": {"likes": {"name": "点赞数", "type": "number", "selectors": [".like-count", ".praise-num", "[data-likes]", ".zan-count", ".thumbs-up", ".good-count"], "required": false, "default": 0, "extractor": "number_extractor", "description": "文章点赞数量"}, "views": {"name": "阅读量", "type": "number", "selectors": [".view-count", ".read-num", "[data-views]", ".pv-count", ".visit-count", ".browse-count"], "required": false, "default": 0, "extractor": "number_extractor", "description": "文章阅读量"}, "price": {"name": "价格", "type": "currency", "selectors": [".price", ".cost", "[data-price]", ".money", ".amount", ".fee"], "required": false, "default": 0.0, "extractor": "currency_extractor", "description": "商品或服务价格"}, "sales": {"name": "成交量", "type": "number", "selectors": [".sales-count", ".sold-num", "[data-sales]", ".deal-count", ".transaction-count", ".order-count"], "required": false, "default": 0, "extractor": "number_extractor", "description": "商品成交数量"}, "comments": {"name": "评论数", "type": "number", "selectors": [".comment-count", ".reply-num", "[data-comments]", ".discuss-count", ".feedback-count", ".review-count"], "required": false, "default": 0, "extractor": "number_extractor", "description": "文章评论数量"}, "shares": {"name": "分享数", "type": "number", "selectors": [".share-count", ".forward-num", "[data-shares]", ".repost-count", ".spread-count"], "required": false, "default": 0, "extractor": "number_extractor", "description": "文章分享次数"}, "tags": {"name": "标签", "type": "list", "selectors": [".tags", ".keywords", ".labels", ".tag-list", ".category-tags"], "required": false, "default": [], "extractor": "list_extractor", "description": "文章标签列表"}, "rating": {"name": "评分", "type": "float", "selectors": [".rating", ".score", "[data-rating]", ".star-rating", ".grade"], "required": false, "default": 0.0, "extractor": "float_extractor", "description": "文章或商品评分"}, "category": {"name": "分类", "type": "text", "selectors": [".category", ".section", ".type", ".classification", ".genre"], "required": false, "default": "", "extractor": "text_extractor", "description": "文章分类"}, "author_avatar": {"name": "作者头像", "type": "url", "selectors": [".author-avatar img", ".user-avatar img", ".profile-pic img"], "required": false, "default": "", "extractor": "image_url_extractor", "description": "作者头像URL"}, "publish_platform": {"name": "发布平台", "type": "text", "selectors": [".platform", ".source-site", ".from-platform"], "required": false, "default": "", "extractor": "text_extractor", "description": "文章发布平台"}, "word_count": {"name": "字数", "type": "number", "selectors": [".word-count", ".text-length", "[data-words]"], "required": false, "default": 0, "extractor": "number_extractor", "description": "文章字数统计"}, "reading_time": {"name": "阅读时长", "type": "text", "selectors": [".reading-time", ".read-duration", ".time-to-read"], "required": false, "default": "", "extractor": "text_extractor", "description": "预估阅读时长"}, "update_time": {"name": "更新时间", "type": "text", "selectors": [".update-time", ".modified-date", ".last-updated"], "required": false, "default": "", "extractor": "date_extractor", "description": "文章最后更新时间"}}, "field_presets": {"basic": ["dateget", "source", "title", "articlelink", "content", "classid", "city", "getdate"], "social_media": ["dateget", "source", "title", "articlelink", "content", "likes", "views", "comments", "shares", "getdate"], "ecommerce": ["title", "articlelink", "content", "price", "sales", "rating", "category", "getdate"], "news": ["dateget", "source", "title", "articlelink", "content", "category", "tags", "views", "getdate"], "blog": ["dateget", "source", "title", "articlelink", "content", "tags", "word_count", "reading_time", "getdate"], "comprehensive": ["dateget", "source", "title", "articlelink", "content", "classid", "city", "likes", "views", "comments", "shares", "price", "sales", "rating", "tags", "category", "getdate"]}}