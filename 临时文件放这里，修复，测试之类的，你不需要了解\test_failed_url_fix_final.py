#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试失败URL处理修复
"""

def test_failed_url_processor():
    """测试失败URL处理器"""
    print("🔧 测试失败URL处理器...")
    
    try:
        from failed_url_processor import process_failed_csv
        print("✅ 成功导入 process_failed_csv")
        
        # 测试回调函数
        def test_log_callback(message):
            print(f"[LOG] {message}")
        
        def test_progress_callback(current, total):
            print(f"[PROGRESS] {current}/{total}")
        
        def test_stop_flag():
            return False  # 不停止
        
        # 检查测试文件
        test_file = "articles/上海人大_代表风采_failed.xlsx"
        import os
        if not os.path.exists(test_file):
            print(f"❌ 测试文件不存在: {test_file}")
            return False
        
        print(f"📁 测试文件存在: {test_file}")
        
        # 测试参数
        result = process_failed_csv(
            failed_csv_path=test_file,
            save_dir="articles",
            export_filename="测试重试结果",
            file_format="CSV",
            classid="",
            retry=1,  # 减少重试次数以便快速测试
            interval=0.5,
            max_workers=2,
            progress_callback=test_progress_callback,
            log_callback=test_log_callback,
            stop_flag=test_stop_flag
        )
        
        print(f"✅ 处理完成，结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_module_manager():
    """测试模组管理器"""
    print("\n🔧 测试模组管理器...")
    
    try:
        from module_manager import get_config_for_url, match_module_for_url
        print("✅ 成功导入模组管理器")
        
        # 测试微信公众号URL匹配
        test_url = "https://mp.weixin.qq.com/s/test123"
        module_name = match_module_for_url(test_url)
        print(f"📋 URL匹配结果: {test_url} -> {module_name}")
        
        config = get_config_for_url(test_url)
        if config:
            print(f"📝 获取到配置: {list(config.keys())}")
            if 'title_selectors' in config:
                print(f"📝 标题选择器: {config['title_selectors']}")
            if 'content_selectors' in config:
                print(f"📝 内容选择器: {config['content_selectors']}")
        else:
            print("⚠️ 未获取到配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 模组管理器测试失败: {e}")
        return False

def test_crawler_integration():
    """测试爬虫集成"""
    print("\n🔧 测试爬虫集成...")
    
    try:
        from crawler import save_article
        print("✅ 成功导入 save_article")
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(save_article)
        params = list(sig.parameters.keys())
        print(f"📋 save_article 参数: {params}")
        
        # 检查是否有 use_module_config 参数
        if 'use_module_config' in params:
            print("✅ 支持模组配置参数")
        else:
            print("⚠️ 缺少模组配置参数")
        
        return True
        
    except Exception as e:
        print(f"❌ 爬虫集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("失败URL处理修复测试")
    print("=" * 50)
    
    tests = [
        ("模组管理器", test_module_manager),
        ("爬虫集成", test_crawler_integration),
        ("失败URL处理器", test_failed_url_processor),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！失败URL处理功能应该正常工作。")
        print("\n📋 修复内容:")
        print("✅ 恢复使用原有的 failed_url_processor.py")
        print("✅ 添加GUI回调函数支持")
        print("✅ 支持Excel和CSV文件格式")
        print("✅ 集成模组配置系统")
        print("✅ 支持进度回调和停止控制")
        
        print("\n🎯 现在可以在GUI中测试:")
        print("1. 选择失败文件: articles/上海人大_代表风采_failed.xlsx")
        print("2. 设置保存目录和文件名")
        print("3. 点击'处理失败URL'")
        print("4. 观察详细的处理日志")
        print("5. 微信公众号URL应该能正确提取内容")
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件")
        
        # 提供调试建议
        print("\n🔧 调试建议:")
        print("1. 确保 failed_url_processor.py 存在且可导入")
        print("2. 确保 module_manager.py 存在且配置正确")
        print("3. 确保 crawler.py 支持模组配置")
        print("4. 检查模组配置文件 module_configs.json")

if __name__ == "__main__":
    main()
