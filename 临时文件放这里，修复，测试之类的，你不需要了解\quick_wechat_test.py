#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试微信公众号选择器
"""

import asyncio
from playwright.async_api import async_playwright

async def test_wechat_selectors():
    """测试微信公众号选择器"""
    url = "https://mp.weixin.qq.com/s/hZmZVjxiQGmpS7z3Tjtg5g"
    
    print(f"🔍 测试URL: {url}")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        # 添加反检测脚本
        await context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
        
        page = await context.new_page()
        
        try:
            print("📱 访问页面...")
            await page.goto(url, timeout=60000)
            await page.wait_for_load_state('networkidle', timeout=30000)
            await asyncio.sleep(5)
            
            print("📝 页面标题:", await page.title())
            print("🔗 当前URL:", page.url)
            
            # 测试内容选择器
            content_selectors = [
                "#js_content",
                ".rich_media_content", 
                "#img-content",
                ".rich_media_area_primary",
                "[data-role='outer']",
                ".rich_media_wrp"
            ]
            
            print("\n🔍 测试内容选择器:")
            for selector in content_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        text = await element.inner_text()
                        print(f"  ✅ {selector}: {len(text)} 字符")
                        if len(text) > 100:
                            print(f"     预览: {text[:100]}...")
                    else:
                        print(f"  ❌ {selector}: 未找到")
                except Exception as e:
                    print(f"  ❌ {selector}: 错误 - {e}")
            
            # 测试标题选择器
            print("\n🔍 测试标题选择器:")
            title_selectors = [
                "#activity-name",
                ".rich_media_title",
                "h1.rich_media_title",
                ".rich_media_area_primary h1",
                "[data-role='title']"
            ]
            
            for selector in title_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        text = await element.inner_text()
                        print(f"  ✅ {selector}: {text}")
                    else:
                        print(f"  ❌ {selector}: 未找到")
                except Exception as e:
                    print(f"  ❌ {selector}: 错误 - {e}")
            
            # 检查页面是否有特殊提示
            page_content = await page.content()
            if "验证" in page_content or "robot" in page_content.lower():
                print("\n⚠️ 页面可能需要验证")
            
            # 截图
            await page.screenshot(path="wechat_test.png")
            print("\n📸 页面截图已保存: wechat_test.png")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_wechat_selectors())
