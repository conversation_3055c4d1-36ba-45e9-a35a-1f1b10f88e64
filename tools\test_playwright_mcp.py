#!/usr/bin/env python3
"""
使用Playwright MCP服务测试翻页功能
直接测试而不依赖PaginationHandler的复杂配置
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from playwright.async_api import async_playwright

async def test_with_playwright_mcp():
    """使用Playwright MCP服务直接测试翻页"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("🚀 使用Playwright MCP服务测试翻页功能")
    print(f"📍 URL: {url}")
    
    async with async_playwright() as p:
        # 测试简化的浏览器配置（移除可能影响JavaScript的设置）
        browser = await p.chromium.launch(
            headless=False,
            slow_mo=1000,
            args=[
                "--no-sandbox",
                "--disable-setuid-sandbox",
                # 移除可能影响JavaScript的参数
                # "--disable-blink-features=AutomationControlled",
                # "--exclude-switches=enable-automation",
            ]
        )

        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            locale='zh-CN',
            java_script_enabled=True,
            # 移除可能影响JavaScript的设置
            # bypass_csp=True,
            ignore_https_errors=True
        )

        # 不注入反检测脚本
        page = await context.new_page()
        
        try:
            print("🌐 访问页面...")
            await page.goto(url, wait_until='networkidle')
            
            # 等待页面完全加载
            print("⏳ 等待页面加载...")
            await page.wait_for_timeout(5000)
            
            # 检查页面标题
            title = await page.title()
            print(f"📄 页面标题: {title}")
            
            # 1. 直接检查翻页按钮
            print("\n=== 🔍 检查翻页按钮 ===")
            
            # 使用与成功测试脚本相同的选择器
            next_selectors = [
                'a:has-text("下一页")',
                'a:has-text("下页")',
                'a:has-text("下一頁")',
                'a.next',
                'a.next-page',
                '.pagination .next',
                'a[title="下一页"]',
                'button:has-text("下一页")'
            ]
            
            found_button = None
            for selector in next_selectors:
                try:
                    btn = await page.query_selector(selector)
                    if btn:
                        is_disabled = await btn.evaluate('(el) => el.hasAttribute("disabled") || el.classList.contains("disabled")')
                        is_visible = await btn.is_visible()
                        btn_text = await btn.text_content()
                        btn_href = await btn.get_attribute('href')
                        
                        print(f"✅ 找到按钮: {selector}")
                        print(f"   文本: '{btn_text}'")
                        print(f"   href: '{btn_href}'")
                        print(f"   可见: {is_visible}")
                        print(f"   禁用: {is_disabled}")
                        
                        if not is_disabled and is_visible:
                            found_button = btn
                            print(f"🎯 选择此按钮进行翻页测试")
                            break
                    else:
                        print(f"❌ 未找到: {selector}")
                except Exception as e:
                    print(f"❌ 检查 {selector} 时出错: {e}")
            
            if not found_button:
                print("❌ 未找到任何可用的翻页按钮")
                
                # 尝试查找所有包含"下一页"的元素
                print("\n=== 🔍 查找所有包含'下一页'的元素 ===")
                all_elements = await page.query_selector_all('*')
                found_any = False
                for elem in all_elements:
                    try:
                        text = await elem.text_content()
                        if text and '下一页' in text:
                            tag_name = await elem.evaluate('el => el.tagName')
                            class_name = await elem.get_attribute('class')
                            id_attr = await elem.get_attribute('id')
                            print(f"找到包含'下一页'的元素: {tag_name}")
                            print(f"   文本: '{text.strip()}'")
                            print(f"   class: '{class_name}'")
                            print(f"   id: '{id_attr}'")
                            found_any = True
                            break
                    except:
                        continue
                
                if not found_any:
                    print("❌ 页面中没有任何包含'下一页'的元素")
                
                return False
            
            # 2. 测试翻页功能
            print(f"\n=== 🧪 测试翻页功能 ===")
            
            # 记录初始状态
            initial_url = page.url
            print(f"初始URL: {initial_url}")
            
            # 获取初始文章列表
            initial_articles = await page.query_selector_all('ul li a')
            print(f"初始文章数量: {len(initial_articles)}")
            
            # 点击下一页
            print("🖱️ 点击下一页按钮...")
            await found_button.click()
            
            # 等待页面变化
            await page.wait_for_load_state('networkidle')
            await page.wait_for_timeout(2000)
            
            # 检查变化
            new_url = page.url
            print(f"翻页后URL: {new_url}")
            
            if new_url != initial_url:
                print("✅ URL已变化，翻页成功！")
                
                # 获取新页面的文章列表
                new_articles = await page.query_selector_all('ul li a')
                print(f"新页面文章数量: {len(new_articles)}")
                
                # 比较文章内容
                if len(new_articles) > 0:
                    first_new_article = await new_articles[0].text_content()
                    print(f"新页面第一篇文章: {first_new_article[:50]}...")
                
                return True
            else:
                print("⚠️ URL未变化，可能是AJAX翻页或翻页失败")
                
                # 检查内容是否变化
                new_articles = await page.query_selector_all('ul li a')
                if len(new_articles) != len(initial_articles):
                    print("✅ 文章数量发生变化，可能是AJAX翻页成功")
                    return True
                else:
                    print("❌ 内容未发生变化，翻页失败")
                    return False
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            await page.screenshot(path='playwright_mcp_error.png')
            return False
        finally:
            # 截图保存
            await page.screenshot(path='playwright_mcp_test.png', full_page=True)
            print("📸 测试截图已保存: playwright_mcp_test.png")
            
            # 关闭浏览器
            await context.close()
            await browser.close()

async def test_pagination_handler_vs_direct():
    """对比PaginationHandler和直接Playwright的差异"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("\n" + "="*80)
    print("🔄 对比PaginationHandler vs 直接Playwright")
    print("="*80)
    
    # 1. 直接Playwright测试
    print("\n1️⃣ 直接Playwright测试:")
    direct_success = await test_with_playwright_mcp()
    
    # 2. PaginationHandler测试
    print("\n2️⃣ PaginationHandler测试:")
    
    from core.PaginationHandler import PaginationHandler
    from core.crawler import launch_browser
    
    async with async_playwright() as p:
        try:
            browser, context, page = await launch_browser(p, headless=False)
            handler = PaginationHandler(page)
            
            await page.goto(url, wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(5000)
            
            extract_config = {
                'list_container_selector': 'body',
                'article_item_selector': 'ul li a',
                'title_selector': 'a',
                'save_dir': "对比测试",
                'page_title': "PaginationHandler测试",
                'classid': 'handler_test',
                'base_url': 'https://www.tjszx.gov.cn',
                'url_mode': 'relative'
            }
            
            pages_processed = await handler._simple_click_pagination(
                next_button_selector='a:has-text("下一页")',
                max_pages=2,
                wait_after_click=2000,
                extract_articles_config=extract_config
            )
            
            handler_success = pages_processed > 1
            articles = handler.get_all_articles()
            
            print(f"PaginationHandler结果:")
            print(f"   处理页数: {pages_processed}")
            print(f"   收集文章: {len(articles)} 篇")
            print(f"   成功翻页: {handler_success}")
            
            await context.close()
            await browser.close()
            
        except Exception as e:
            print(f"❌ PaginationHandler测试失败: {e}")
            handler_success = False
    
    # 3. 结果对比
    print(f"\n📊 对比结果:")
    print(f"   直接Playwright: {'✅ 成功' if direct_success else '❌ 失败'}")
    print(f"   PaginationHandler: {'✅ 成功' if handler_success else '❌ 失败'}")
    
    if direct_success and not handler_success:
        print(f"\n🔍 分析: 直接Playwright成功但PaginationHandler失败")
        print(f"   可能原因:")
        print(f"   1. PaginationHandler的配置或逻辑有问题")
        print(f"   2. 文章提取配置影响了翻页检测")
        print(f"   3. 等待时机或顺序不同")
    elif not direct_success and not handler_success:
        print(f"\n🔍 分析: 两种方法都失败")
        print(f"   可能原因:")
        print(f"   1. 网站翻页机制发生变化")
        print(f"   2. 网络或访问问题")
        print(f"   3. 浏览器环境问题")
    elif direct_success and handler_success:
        print(f"\n🎉 两种方法都成功！问题可能在GUI的其他配置")

if __name__ == "__main__":
    print("Playwright MCP服务翻页测试")
    print("=" * 50)
    
    choice = input("选择测试:\n1. 仅直接Playwright测试\n2. 对比测试\n请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        result = asyncio.run(test_with_playwright_mcp())
        print(f"\n🎯 测试完成！结果: {'✅ 成功' if result else '❌ 失败'}")
    else:
        asyncio.run(test_pagination_handler_vs_direct())
