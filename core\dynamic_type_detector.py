#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能动态类型检测与处理模块

=== 策略规则注释标准 ===

每个策略处理器必须遵循以下注释规范：

1. 【策略标识】: 唯一的策略名称，格式：STRATEGY_[TYPE]_[NAME]
2. 【适用场景】: 详细描述该策略适用的网站类型和特征
3. 【检测条件】: 明确的检测条件，包括URL模式、页面特征、技术栈等
4. 【处理逻辑】: 详细的处理步骤和核心算法
5. 【优先级】: 策略执行优先级（1-10，数字越小优先级越高）
6. 【依赖项】: 所需的外部依赖和工具
7. 【成功标准】: 判断策略执行成功的标准
8. 【失败处理】: 策略失败时的回退机制
9. 【维护说明】: 维护和扩展该策略的注意事项
10. 【测试用例】: 典型的测试URL和预期结果

=== 策略扩展指南 ===

添加新策略时，请遵循以下步骤：
1. 在 DynamicTypeDetector 类中添加检测方法：detect_[type]_website()
2. 在 DynamicTypeProcessor 类中添加处理方法：process_[type]_pagination()
3. 更新 STRATEGY_REGISTRY 注册表
4. 添加完整的策略规则注释
5. 编写单元测试
6. 更新文档和示例

=== 当前支持的策略类型 ===

- JSP_WEBSITE: JSP网站翻页处理
- IFRAME_PAGINATION: iframe内翻页处理
- AJAX_PAGINATION: AJAX动态加载翻页
- INFINITE_SCROLL: 无限滚动翻页
- REACT_SPA: React单页应用翻页
- VUE_SPA: Vue单页应用翻页
- ANGULAR_SPA: Angular单页应用翻页

"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable, Tuple
from playwright.async_api import Page
from urllib.parse import urlparse
import re
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class StrategyPriority(Enum):
    """策略优先级枚举"""
    HIGHEST = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4
    LOWEST = 5

@dataclass
class StrategyInfo:
    """策略信息数据类"""
    name: str
    priority: StrategyPriority
    detector: Callable
    processor: Callable
    description: str
    applicable_domains: List[str]
    test_urls: List[str]

class DynamicTypeDetector:
    """
    动态类型检测器
    
    负责检测网站的动态类型和技术栈，为后续处理提供决策依据
    """
    
    def __init__(self):
        self.detection_cache = {}  # 检测结果缓存
    
    async def detect_website_type(self, page: Page, url: str) -> Dict[str, Any]:
        """
        智能检测网站类型
        
        Args:
            page: Playwright页面对象
            url: 网站URL
            
        Returns:
            dict: 检测结果，包含网站类型、特征、推荐策略等
        """
        # 检查缓存
        cache_key = f"{urlparse(url).netloc}_{urlparse(url).path}"
        if cache_key in self.detection_cache:
            logger.debug(f"使用缓存的检测结果: {cache_key}")
            return self.detection_cache[cache_key]
        
        detection_result = {
            'url': url,
            'types': [],
            'features': {},
            'recommended_strategies': [],
            'confidence': 0.0
        }
        
        # 执行各种检测
        await self._detect_jsp_website(page, url, detection_result)
        await self._detect_iframe_structure(page, url, detection_result)
        await self._detect_ajax_pagination(page, url, detection_result)
        await self._detect_spa_framework(page, url, detection_result)
        await self._detect_infinite_scroll(page, url, detection_result)
        
        # 计算总体置信度
        detection_result['confidence'] = self._calculate_confidence(detection_result)
        
        # 缓存结果
        self.detection_cache[cache_key] = detection_result
        
        logger.info(f"网站类型检测完成: {url}")
        logger.info(f"检测到的类型: {detection_result['types']}")
        logger.info(f"推荐策略: {detection_result['recommended_strategies']}")
        
        return detection_result
    
    async def _detect_jsp_website(self, page: Page, url: str, result: Dict[str, Any]):
        """
        【策略标识】: STRATEGY_JSP_WEBSITE
        【适用场景】: JSP技术栈的政府网站、企业网站，通常使用传统的服务器端渲染
        【检测条件】: 
            - URL包含.jsp扩展名
            - 页面包含JSP特有的JavaScript函数（如goto, nextPage）
            - 使用iframe结构
            - 表单提交方式为POST
        【处理逻辑】: 
            1. 检测iframe结构
            2. 查找JavaScript翻页函数
            3. 使用goto()函数进行翻页
            4. 处理表单提交
        【优先级】: 2 (HIGH)
        【依赖项】: Playwright, JSP处理器模块
        【成功标准】: 成功调用goto()函数并检测到页面变化
        【失败处理】: 回退到传统点击翻页
        【维护说明】: JSP网站结构相对稳定，主要关注JavaScript函数名变化
        【测试用例】: 
            - http://www.hfszx.org.cn/hfzx/web/list.jsp (合肥政协)
            - 预期结果: 检测到JSP类型，推荐JSP_WEBSITE策略
        """
        try:
            confidence = 0.0
            features = {}
            
            # 检测URL特征
            if '.jsp' in url.lower():
                confidence += 0.3
                features['jsp_url'] = True
            
            # 检测iframe结构
            iframes = await page.query_selector_all('iframe')
            if iframes:
                confidence += 0.2
                features['has_iframe'] = True
                features['iframe_count'] = len(iframes)
            
            # 检测JavaScript函数
            js_functions = await page.evaluate("""
                () => {
                    const functions = [];
                    if (typeof goto === 'function') functions.push('goto');
                    if (typeof nextPage === 'function') functions.push('nextPage');
                    if (typeof goPage === 'function') functions.push('goPage');
                    return functions;
                }
            """)
            
            if js_functions:
                confidence += 0.4
                features['js_functions'] = js_functions
            
            # 检测表单结构
            forms = await page.query_selector_all('form')
            if forms:
                confidence += 0.1
                features['has_forms'] = True
            
            if confidence > 0.5:
                result['types'].append('JSP_WEBSITE')
                result['features']['jsp'] = features
                result['recommended_strategies'].append({
                    'name': 'JSP_WEBSITE',
                    'confidence': confidence,
                    'priority': StrategyPriority.HIGH.value
                })
                
        except Exception as e:
            logger.debug(f"JSP网站检测失败: {e}")
    
    async def _detect_iframe_structure(self, page: Page, url: str, result: Dict[str, Any]):
        """
        【策略标识】: STRATEGY_IFRAME_PAGINATION
        【适用场景】: 使用iframe嵌套结构的网站，翻页元素在iframe内部
        【检测条件】:
            - 页面包含iframe元素
            - iframe中包含翻页相关元素
            - 主页面中没有翻页元素
        【处理逻辑】:
            1. 遍历所有iframe
            2. 在iframe中查找翻页元素
            3. 使用iframe上下文进行翻页操作
        【优先级】: 3 (MEDIUM)
        【依赖项】: Playwright iframe API
        【成功标准】: 在iframe中找到可用的翻页元素
        【失败处理】: 回退到主页面翻页检测
        【维护说明】: 注意iframe加载时间和跨域限制
        【测试用例】:
            - 包含iframe的政府网站
            - 预期结果: 检测到IFRAME_PAGINATION类型
        """
        try:
            confidence = 0.0
            features = {}
            
            # 检测iframe
            iframes = await page.query_selector_all('iframe')
            if not iframes:
                return
            
            features['iframe_count'] = len(iframes)
            confidence += 0.2
            
            # 检查iframe中的翻页元素
            iframe_pagination_count = 0
            for iframe in iframes:
                try:
                    frame = await iframe.content_frame()
                    if frame:
                        await frame.wait_for_load_state('networkidle', timeout=5000)
                        pagination_elements = await frame.query_selector_all(
                            'a:has-text("下一页"), a:has-text("下页"), a[href*="javascript:"]'
                        )
                        if pagination_elements:
                            iframe_pagination_count += 1
                            confidence += 0.3
                except:
                    continue
            
            features['iframe_with_pagination'] = iframe_pagination_count
            
            if confidence > 0.3:
                result['types'].append('IFRAME_PAGINATION')
                result['features']['iframe'] = features
                result['recommended_strategies'].append({
                    'name': 'IFRAME_PAGINATION',
                    'confidence': confidence,
                    'priority': StrategyPriority.MEDIUM.value
                })
                
        except Exception as e:
            logger.debug(f"iframe结构检测失败: {e}")
    
    async def _detect_ajax_pagination(self, page: Page, url: str, result: Dict[str, Any]):
        """
        【策略标识】: STRATEGY_AJAX_PAGINATION
        【适用场景】: 使用AJAX技术进行动态加载的现代网站
        【检测条件】:
            - 页面包含AJAX相关的JavaScript库
            - 翻页按钮绑定了AJAX事件
            - 页面内容通过异步请求更新
        【处理逻辑】:
            1. 监听网络请求
            2. 点击翻页按钮
            3. 等待AJAX请求完成
            4. 提取新加载的内容
        【优先级】: 3 (MEDIUM)
        【依赖项】: Playwright网络监听API
        【成功标准】: 检测到AJAX请求并成功获取新内容
        【失败处理】: 回退到传统翻页方式
        【维护说明】: 需要关注AJAX请求的URL模式变化
        【测试用例】:
            - 现代企业网站、新闻网站
            - 预期结果: 检测到AJAX_PAGINATION类型
        """
        try:
            confidence = 0.0
            features = {}
            
            # 检测AJAX相关的JavaScript库
            ajax_libraries = await page.evaluate("""
                () => {
                    const libraries = [];
                    if (typeof jQuery !== 'undefined') libraries.push('jQuery');
                    if (typeof axios !== 'undefined') libraries.push('axios');
                    if (typeof fetch !== 'undefined') libraries.push('fetch');
                    if (typeof XMLHttpRequest !== 'undefined') libraries.push('XMLHttpRequest');
                    return libraries;
                }
            """)
            
            if ajax_libraries:
                confidence += 0.3
                features['ajax_libraries'] = ajax_libraries
            
            # 检测异步加载的内容容器
            async_containers = await page.query_selector_all(
                '[data-ajax], [data-load], .ajax-content, .dynamic-content'
            )
            if async_containers:
                confidence += 0.2
                features['async_containers'] = len(async_containers)
            
            # 检测翻页按钮的事件绑定
            pagination_buttons = await page.query_selector_all(
                '.pagination a, .page-link, [data-page]'
            )
            if pagination_buttons:
                confidence += 0.2
                features['pagination_buttons'] = len(pagination_buttons)
            
            if confidence > 0.4:
                result['types'].append('AJAX_PAGINATION')
                result['features']['ajax'] = features
                result['recommended_strategies'].append({
                    'name': 'AJAX_PAGINATION',
                    'confidence': confidence,
                    'priority': StrategyPriority.MEDIUM.value
                })
                
        except Exception as e:
            logger.debug(f"AJAX翻页检测失败: {e}")
    
    async def _detect_spa_framework(self, page: Page, url: str, result: Dict[str, Any]):
        """
        【策略标识】: STRATEGY_SPA_FRAMEWORK
        【适用场景】: React、Vue、Angular等单页应用框架
        【检测条件】:
            - 页面包含SPA框架的特征代码
            - 使用前端路由
            - 动态渲染内容
        【处理逻辑】:
            1. 检测框架类型
            2. 监听路由变化
            3. 等待组件渲染完成
            4. 提取动态内容
        【优先级】: 4 (LOW)
        【依赖项】: 框架特定的检测逻辑
        【成功标准】: 成功检测到框架并处理路由变化
        【失败处理】: 回退到通用动态内容处理
        【维护说明】: 需要跟进各框架版本更新
        【测试用例】:
            - 现代Web应用
            - 预期结果: 检测到对应的SPA框架类型
        """
        try:
            confidence = 0.0
            features = {}
            
            # 检测前端框架
            frameworks = await page.evaluate("""
                () => {
                    const detected = [];
                    if (typeof React !== 'undefined') detected.push('React');
                    if (typeof Vue !== 'undefined') detected.push('Vue');
                    if (typeof angular !== 'undefined') detected.push('Angular');
                    if (document.querySelector('[data-reactroot]')) detected.push('React');
                    if (document.querySelector('[data-v-]')) detected.push('Vue');
                    if (document.querySelector('ng-app, [ng-app]')) detected.push('Angular');
                    return detected;
                }
            """)
            
            if frameworks:
                confidence += 0.4
                features['frameworks'] = frameworks
            
            # 检测前端路由
            has_router = await page.evaluate("""
                () => {
                    return !!(window.history && window.history.pushState);
                }
            """)
            
            if has_router:
                confidence += 0.2
                features['has_router'] = True
            
            if confidence > 0.3:
                for framework in frameworks:
                    result['types'].append(f'{framework.upper()}_SPA')
                    result['recommended_strategies'].append({
                        'name': f'{framework.upper()}_SPA',
                        'confidence': confidence,
                        'priority': StrategyPriority.LOW.value
                    })
                
                result['features']['spa'] = features
                
        except Exception as e:
            logger.debug(f"SPA框架检测失败: {e}")
    
    async def _detect_infinite_scroll(self, page: Page, url: str, result: Dict[str, Any]):
        """
        【策略标识】: STRATEGY_INFINITE_SCROLL
        【适用场景】: 使用无限滚动加载的现代网站，如社交媒体、新闻聚合网站
        【检测条件】:
            - 页面没有传统的翻页按钮
            - 滚动到底部时触发新内容加载
            - 使用Intersection Observer或scroll事件
        【处理逻辑】:
            1. 模拟滚动到页面底部
            2. 监听新内容加载
            3. 重复滚动直到没有新内容
        【优先级】: 4 (LOW)
        【依赖项】: Playwright滚动API
        【成功标准】: 成功触发滚动加载并获取新内容
        【失败处理】: 提示用户该网站不支持传统翻页
        【维护说明】: 需要调整滚动策略和等待时间
        【测试用例】:
            - 社交媒体网站、新闻聚合网站
            - 预期结果: 检测到INFINITE_SCROLL类型
        """
        try:
            confidence = 0.0
            features = {}
            
            # 检测是否缺少传统翻页元素
            traditional_pagination = await page.query_selector_all(
                '.pagination, .page-nav, a:has-text("下一页"), a:has-text("下页")'
            )
            
            if not traditional_pagination:
                confidence += 0.2
                features['no_traditional_pagination'] = True
            
            # 检测滚动加载相关的元素
            scroll_indicators = await page.query_selector_all(
                '.loading, .load-more, [data-infinite], .infinite-scroll'
            )
            
            if scroll_indicators:
                confidence += 0.3
                features['scroll_indicators'] = len(scroll_indicators)
            
            # 检测Intersection Observer
            has_intersection_observer = await page.evaluate("""
                () => {
                    return typeof IntersectionObserver !== 'undefined';
                }
            """)
            
            if has_intersection_observer:
                confidence += 0.2
                features['has_intersection_observer'] = True
            
            if confidence > 0.4:
                result['types'].append('INFINITE_SCROLL')
                result['features']['infinite_scroll'] = features
                result['recommended_strategies'].append({
                    'name': 'INFINITE_SCROLL',
                    'confidence': confidence,
                    'priority': StrategyPriority.LOW.value
                })
                
        except Exception as e:
            logger.debug(f"无限滚动检测失败: {e}")
    
    def _calculate_confidence(self, result: Dict[str, Any]) -> float:
        """计算总体置信度"""
        if not result['recommended_strategies']:
            return 0.0
        
        # 使用最高置信度作为总体置信度
        max_confidence = max(
            strategy['confidence'] for strategy in result['recommended_strategies']
        )
        
        return min(max_confidence, 1.0)


class DynamicTypeProcessor:
    """
    动态类型处理器

    根据检测结果选择合适的处理策略，执行具体的翻页和内容提取操作
    """

    def __init__(self):
        self.strategy_registry = self._build_strategy_registry()
        self.processing_cache = {}

    def _build_strategy_registry(self) -> Dict[str, StrategyInfo]:
        """构建策略注册表"""
        return {
            'JSP_WEBSITE': StrategyInfo(
                name='JSP_WEBSITE',
                priority=StrategyPriority.HIGH,
                detector=self._detect_jsp,
                processor=self._process_jsp_pagination,
                description='JSP网站翻页处理策略',
                applicable_domains=['政府网站', '企业网站', '传统Web应用'],
                test_urls=['http://www.hfszx.org.cn/hfzx/web/list.jsp']
            ),
            'IFRAME_PAGINATION': StrategyInfo(
                name='IFRAME_PAGINATION',
                priority=StrategyPriority.MEDIUM,
                detector=self._detect_iframe,
                processor=self._process_iframe_pagination,
                description='iframe内翻页处理策略',
                applicable_domains=['嵌套结构网站', '框架网站'],
                test_urls=[]
            ),
            'AJAX_PAGINATION': StrategyInfo(
                name='AJAX_PAGINATION',
                priority=StrategyPriority.MEDIUM,
                detector=self._detect_ajax,
                processor=self._process_ajax_pagination,
                description='AJAX动态翻页处理策略',
                applicable_domains=['现代Web应用', '新闻网站', '电商网站'],
                test_urls=[]
            ),
            'INFINITE_SCROLL': StrategyInfo(
                name='INFINITE_SCROLL',
                priority=StrategyPriority.LOW,
                detector=self._detect_infinite_scroll,
                processor=self._process_infinite_scroll,
                description='无限滚动处理策略',
                applicable_domains=['社交媒体', '新闻聚合', '图片网站'],
                test_urls=[]
            ),
            'REACT_SPA': StrategyInfo(
                name='REACT_SPA',
                priority=StrategyPriority.LOW,
                detector=self._detect_react,
                processor=self._process_spa_pagination,
                description='React单页应用处理策略',
                applicable_domains=['React应用', '现代Web应用'],
                test_urls=[]
            ),
            'VUE_SPA': StrategyInfo(
                name='VUE_SPA',
                priority=StrategyPriority.LOW,
                detector=self._detect_vue,
                processor=self._process_spa_pagination,
                description='Vue单页应用处理策略',
                applicable_domains=['Vue应用', '现代Web应用'],
                test_urls=[]
            )
        }

    async def process_dynamic_pagination(
        self,
        page: Page,
        url: str,
        detection_result: Dict[str, Any],
        max_pages: int = 5,
        **kwargs
    ) -> Dict[str, Any]:
        """
        根据检测结果处理动态翻页

        Args:
            page: Playwright页面对象
            url: 网站URL
            detection_result: 类型检测结果
            max_pages: 最大页数
            **kwargs: 其他参数

        Returns:
            dict: 处理结果
        """
        if not detection_result['recommended_strategies']:
            logger.warning("未检测到合适的处理策略")
            return {'success': False, 'error': '未检测到合适的处理策略'}

        # 按优先级排序策略
        strategies = sorted(
            detection_result['recommended_strategies'],
            key=lambda x: x['priority']
        )

        # 尝试执行策略
        for strategy in strategies:
            strategy_name = strategy['name']
            if strategy_name not in self.strategy_registry:
                logger.warning(f"未找到策略处理器: {strategy_name}")
                continue

            try:
                logger.info(f"尝试执行策略: {strategy_name}")
                strategy_info = self.strategy_registry[strategy_name]

                result = await strategy_info.processor(
                    page, url, detection_result, max_pages, **kwargs
                )

                if result.get('success', False):
                    logger.info(f"策略执行成功: {strategy_name}")
                    result['used_strategy'] = strategy_name
                    return result
                else:
                    logger.warning(f"策略执行失败: {strategy_name}, 错误: {result.get('error', '未知错误')}")

            except Exception as e:
                logger.error(f"策略执行异常: {strategy_name}, 错误: {e}")
                continue

        return {'success': False, 'error': '所有策略都执行失败'}

    async def _process_jsp_pagination(
        self,
        page: Page,
        url: str,
        detection_result: Dict[str, Any],
        max_pages: int,
        **kwargs
    ) -> Dict[str, Any]:
        """
        【策略标识】: PROCESSOR_JSP_WEBSITE
        【处理逻辑】: 使用JSP处理器进行翻页和内容提取
        【成功标准】: 成功收集到文章链接
        【失败处理】: 返回错误信息，由上层决定回退策略
        """
        try:
            # 导入JSP处理器
            from modules.jsp_website_handler import jsp_handler

            logger.info("使用JSP处理器进行翻页")

            # 使用JSP处理器处理翻页
            article_links = await jsp_handler.handle_pagination(
                page=page,
                current_page=1,
                max_pages=max_pages
            )

            if article_links:
                return {
                    'success': True,
                    'articles': article_links,
                    'pages_processed': max_pages,
                    'strategy': 'JSP_WEBSITE'
                }
            else:
                return {
                    'success': False,
                    'error': 'JSP处理器未收集到文章'
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'JSP处理器执行失败: {e}'
            }

    async def _process_iframe_pagination(
        self,
        page: Page,
        url: str,
        detection_result: Dict[str, Any],
        max_pages: int,
        **kwargs
    ) -> Dict[str, Any]:
        """
        【策略标识】: PROCESSOR_IFRAME_PAGINATION
        【处理逻辑】: 在iframe上下文中进行翻页操作
        【成功标准】: 成功在iframe中执行翻页
        【失败处理】: 回退到主页面翻页
        """
        try:
            from core.pagination_utils import PaginationUtils

            logger.info("使用iframe翻页处理")

            articles = []
            current_page = 1

            while current_page <= max_pages:
                # 使用统一翻页工具处理iframe翻页
                success = await PaginationUtils.smart_pagination(
                    page=page,
                    current_page=current_page,
                    include_jsp_selectors=True,
                    wait_after_click=2000
                )

                if not success:
                    break

                # 这里应该添加文章提取逻辑
                # 暂时返回基本结果
                current_page += 1

            return {
                'success': True,
                'articles': articles,
                'pages_processed': current_page - 1,
                'strategy': 'IFRAME_PAGINATION'
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'iframe翻页处理失败: {e}'
            }

    async def _process_ajax_pagination(
        self,
        page: Page,
        url: str,
        detection_result: Dict[str, Any],
        max_pages: int,
        **kwargs
    ) -> Dict[str, Any]:
        """
        【策略标识】: PROCESSOR_AJAX_PAGINATION
        【处理逻辑】: 监听AJAX请求，处理异步加载的内容
        【成功标准】: 成功捕获AJAX请求并提取新内容
        【失败处理】: 回退到传统翻页方式
        """
        try:
            logger.info("使用AJAX翻页处理")

            articles = []
            current_page = 1

            # 设置网络监听
            responses = []

            def handle_response(response):
                if 'json' in response.headers.get('content-type', '').lower():
                    responses.append(response)

            page.on('response', handle_response)

            while current_page <= max_pages:
                # 查找并点击翻页按钮
                pagination_buttons = await page.query_selector_all(
                    '.pagination a, .page-link, [data-page]'
                )

                if not pagination_buttons:
                    break

                # 点击下一页按钮
                try:
                    await pagination_buttons[0].click()
                    await page.wait_for_timeout(2000)  # 等待AJAX请求完成
                    current_page += 1
                except:
                    break

            return {
                'success': True,
                'articles': articles,
                'pages_processed': current_page - 1,
                'strategy': 'AJAX_PAGINATION',
                'ajax_responses': len(responses)
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'AJAX翻页处理失败: {e}'
            }

    async def _process_infinite_scroll(
        self,
        page: Page,
        url: str,
        detection_result: Dict[str, Any],
        max_pages: int,
        **kwargs
    ) -> Dict[str, Any]:
        """
        【策略标识】: PROCESSOR_INFINITE_SCROLL
        【处理逻辑】: 模拟滚动操作，触发无限加载
        【成功标准】: 成功触发滚动加载并获取新内容
        【失败处理】: 提示用户该网站使用无限滚动
        """
        try:
            logger.info("使用无限滚动处理")

            articles = []
            scroll_count = 0
            max_scrolls = max_pages * 3  # 每页滚动3次

            while scroll_count < max_scrolls:
                # 滚动到页面底部
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await page.wait_for_timeout(2000)  # 等待内容加载

                # 检查是否有新内容加载
                # 这里应该添加具体的内容检测逻辑

                scroll_count += 1

            return {
                'success': True,
                'articles': articles,
                'scrolls_performed': scroll_count,
                'strategy': 'INFINITE_SCROLL'
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'无限滚动处理失败: {e}'
            }

    async def _process_spa_pagination(
        self,
        page: Page,
        url: str,
        detection_result: Dict[str, Any],
        max_pages: int,
        **kwargs
    ) -> Dict[str, Any]:
        """
        【策略标识】: PROCESSOR_SPA_PAGINATION
        【处理逻辑】: 处理单页应用的路由变化和组件渲染
        【成功标准】: 成功监听路由变化并提取内容
        【失败处理】: 回退到通用处理方式
        """
        try:
            logger.info("使用SPA翻页处理")

            articles = []
            current_page = 1

            while current_page <= max_pages:
                # 等待组件渲染完成
                await page.wait_for_timeout(1000)

                # 查找翻页按钮
                next_buttons = await page.query_selector_all(
                    'a[href*="page"], button[data-page], .pagination a'
                )

                if not next_buttons:
                    break

                # 点击下一页
                try:
                    await next_buttons[0].click()
                    await page.wait_for_load_state('networkidle')
                    current_page += 1
                except:
                    break

            return {
                'success': True,
                'articles': articles,
                'pages_processed': current_page - 1,
                'strategy': 'SPA_PAGINATION'
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'SPA翻页处理失败: {e}'
            }

    # 检测方法（用于策略注册表）
    async def _detect_jsp(self, page: Page, url: str) -> bool:
        return '.jsp' in url.lower()

    async def _detect_iframe(self, page: Page, url: str) -> bool:
        iframes = await page.query_selector_all('iframe')
        return len(iframes) > 0

    async def _detect_ajax(self, page: Page, url: str) -> bool:
        has_ajax = await page.evaluate("() => typeof jQuery !== 'undefined' || typeof axios !== 'undefined'")
        return has_ajax

    async def _detect_infinite_scroll(self, page: Page, url: str) -> bool:
        scroll_elements = await page.query_selector_all('.infinite-scroll, [data-infinite]')
        return len(scroll_elements) > 0

    async def _detect_react(self, page: Page, url: str) -> bool:
        has_react = await page.evaluate("() => typeof React !== 'undefined' || document.querySelector('[data-reactroot]')")
        return has_react

    async def _detect_vue(self, page: Page, url: str) -> bool:
        has_vue = await page.evaluate("() => typeof Vue !== 'undefined' || document.querySelector('[data-v-]')")
        return has_vue


class SmartDynamicHandler:
    """
    智能动态处理器

    整合检测器和处理器，提供统一的智能处理接口
    """

    def __init__(self):
        self.detector = DynamicTypeDetector()
        self.processor = DynamicTypeProcessor()

    async def smart_handle_pagination(
        self,
        page: Page,
        url: str,
        max_pages: int = 5,
        **kwargs
    ) -> Dict[str, Any]:
        """
        智能处理翻页

        Args:
            page: Playwright页面对象
            url: 网站URL
            max_pages: 最大页数
            **kwargs: 其他参数

        Returns:
            dict: 处理结果
        """
        try:
            # 1. 检测网站类型
            logger.info(f"开始智能检测网站类型: {url}")
            detection_result = await self.detector.detect_website_type(page, url)

            if not detection_result['recommended_strategies']:
                logger.warning("未检测到合适的处理策略，使用默认处理")
                return {
                    'success': False,
                    'error': '未检测到合适的处理策略',
                    'detection_result': detection_result
                }

            # 2. 执行处理策略
            logger.info("开始执行动态处理策略")
            processing_result = await self.processor.process_dynamic_pagination(
                page, url, detection_result, max_pages, **kwargs
            )

            # 3. 合并结果
            final_result = {
                **processing_result,
                'detection_result': detection_result,
                'url': url,
                'max_pages': max_pages
            }

            return final_result

        except Exception as e:
            logger.error(f"智能动态处理失败: {e}")
            return {
                'success': False,
                'error': f'智能动态处理失败: {e}',
                'url': url
            }
