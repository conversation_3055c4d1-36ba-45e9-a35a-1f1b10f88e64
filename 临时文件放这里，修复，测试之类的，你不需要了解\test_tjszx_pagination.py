#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试天津市政协网站的翻页功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from core.crawler import launch_browser
from core.PaginationHandler import PaginationHandler

async def analyze_tjszx_pagination():
    """分析天津市政协网站的翻页结构"""
    print("🔍 分析天津市政协网站翻页结构")
    print("=" * 60)
    
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            print(f"📋 访问网站: {url}")
            await page.goto(url, timeout=30000)
            await page.wait_for_load_state('networkidle', timeout=15000)
            
            print("✅ 页面加载完成")
            
            # 分析页面结构
            print("\n🔍 分析页面结构...")
            
            # 1. 查找所有可能的翻页相关元素
            pagination_selectors = [
                ".pagination",
                ".page",
                ".next",
                "a.next",
                "[title*='下一页']",
                "[title*='下页']", 
                "a[href*='page']",
                "a[onclick*='page']",
                "a[onclick*='Page']",
                ".page-nav",
                ".page-list",
                ".pagelist",
                ".pager",
                "a[href*='index']",
                "script[type='text/javascript']"
            ]
            
            found_elements = {}
            for selector in pagination_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        found_elements[selector] = len(elements)
                        print(f"  ✅ 找到 {selector}: {len(elements)} 个元素")
                        
                        # 获取前几个元素的详细信息
                        for i, element in enumerate(elements[:3]):
                            try:
                                text = await element.text_content()
                                href = await element.get_attribute('href')
                                onclick = await element.get_attribute('onclick')
                                title = await element.get_attribute('title')
                                class_name = await element.get_attribute('class')
                                
                                print(f"    [{i+1}] 文本: '{text}' | href: '{href}' | onclick: '{onclick}' | title: '{title}' | class: '{class_name}'")
                            except Exception as e:
                                print(f"    [{i+1}] 获取元素信息失败: {e}")
                except Exception:
                    pass
            
            # 2. 检查页面中的JavaScript代码
            print("\n🔍 分析页面JavaScript...")
            try:
                # 获取页面HTML内容
                html_content = await page.content()
                
                # 查找翻页相关的JavaScript模式
                js_patterns = [
                    "function.*page",
                    "onclick.*page",
                    "location.*page",
                    "window.location",
                    "href.*page",
                    "goPage",
                    "nextPage",
                    "prevPage"
                ]
                
                import re
                for pattern in js_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    if matches:
                        print(f"  ✅ 找到JS模式 '{pattern}': {len(matches)} 个匹配")
                        for match in matches[:3]:  # 只显示前3个
                            print(f"    - {match}")
                            
            except Exception as e:
                print(f"  ❌ JavaScript分析失败: {e}")
            
            # 3. 尝试查找具体的翻页按钮
            print("\n🔍 查找具体的翻页按钮...")
            
            # 常见的翻页按钮文本
            pagination_texts = ["下一页", "下页", "next", "Next", "NEXT", ">", ">>", "更多"]
            
            for text in pagination_texts:
                try:
                    # 使用文本查找
                    elements = await page.query_selector_all(f"a:has-text('{text}')")
                    if elements:
                        print(f"  ✅ 找到包含文本 '{text}' 的链接: {len(elements)} 个")
                        for i, element in enumerate(elements):
                            href = await element.get_attribute('href')
                            onclick = await element.get_attribute('onclick')
                            print(f"    [{i+1}] href: '{href}' | onclick: '{onclick}'")
                except Exception:
                    pass
            
            # 4. 检查是否有分页数字
            print("\n🔍 查找分页数字...")
            try:
                # 查找数字链接
                number_links = await page.query_selector_all("a[href*='index']")
                if number_links:
                    print(f"  ✅ 找到包含'index'的链接: {len(number_links)} 个")
                    for i, link in enumerate(number_links[:5]):
                        href = await link.get_attribute('href')
                        text = await link.text_content()
                        print(f"    [{i+1}] 文本: '{text}' | href: '{href}'")
                        
                # 查找数字模式
                digit_links = await page.query_selector_all("a")
                digit_count = 0
                for link in digit_links:
                    text = await link.text_content()
                    if text and text.strip().isdigit():
                        digit_count += 1
                        if digit_count <= 5:  # 只显示前5个
                            href = await link.get_attribute('href')
                            print(f"    数字链接: '{text}' | href: '{href}'")
                            
                if digit_count > 0:
                    print(f"  ✅ 总共找到 {digit_count} 个数字链接")
                    
            except Exception as e:
                print(f"  ❌ 分页数字查找失败: {e}")
            
            # 5. 测试动态翻页功能
            print("\n🧪 测试动态翻页功能...")
            
            # 尝试不同的选择器
            test_selectors = [
                "a.next:not(.lose)",
                "a.next",
                ".next",
                "a[title*='下一页']",
                "a:has-text('下一页')",
                "a:has-text('>')",
                "a[href*='index_2']"
            ]
            
            handler = PaginationHandler(page)
            
            for selector in test_selectors:
                try:
                    print(f"  🔍 测试选择器: {selector}")
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"    ✅ 找到 {len(elements)} 个元素")
                        
                        # 尝试点击第一个元素
                        element = elements[0]
                        text = await element.text_content()
                        href = await element.get_attribute('href')
                        onclick = await element.get_attribute('onclick')
                        
                        print(f"    📋 元素信息: 文本='{text}' | href='{href}' | onclick='{onclick}'")
                        
                        # 检查是否可点击
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()
                        print(f"    📋 状态: 可见={is_visible} | 可用={is_enabled}")
                        
                    else:
                        print(f"    ❌ 未找到元素")
                        
                except Exception as e:
                    print(f"    ❌ 测试失败: {e}")
            
            print("\n📊 分析完成")
            
        except Exception as e:
            print(f"❌ 分析过程中出错: {e}")
            
        finally:
            await context.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(analyze_tjszx_pagination())
