#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能动态类型检测与处理模块
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from core.dynamic_type_detector import SmartDynamicHandler, DynamicTypeDetector, DynamicTypeProcessor

async def test_dynamic_type_detection():
    """测试动态类型检测"""
    print("🧪 测试动态类型检测...")
    
    test_urls = [
        {
            'url': 'http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001',
            'expected_types': ['JSP_WEBSITE', 'IFRAME_PAGINATION'],
            'description': '合肥政协JSP网站'
        }
    ]
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        detector = DynamicTypeDetector()
        
        for test_case in test_urls:
            try:
                print(f"\n📋 测试网站: {test_case['description']}")
                print(f"   URL: {test_case['url']}")
                
                await page.goto(test_case['url'], wait_until='networkidle')
                
                # 执行类型检测
                detection_result = await detector.detect_website_type(page, test_case['url'])
                
                print(f"   🔍 检测结果:")
                print(f"      检测到的类型: {detection_result['types']}")
                print(f"      置信度: {detection_result['confidence']:.2f}")
                print(f"      推荐策略: {[s['name'] for s in detection_result['recommended_strategies']]}")
                
                # 验证检测结果
                detected_types = set(detection_result['types'])
                expected_types = set(test_case['expected_types'])
                
                if detected_types.intersection(expected_types):
                    print(f"   ✅ 检测正确: 找到预期类型 {detected_types.intersection(expected_types)}")
                else:
                    print(f"   ⚠️ 检测结果与预期不符")
                    print(f"      预期: {expected_types}")
                    print(f"      实际: {detected_types}")
                
                # 显示详细特征
                print(f"   📊 检测特征:")
                for feature_type, features in detection_result['features'].items():
                    print(f"      {feature_type}: {features}")
                
            except Exception as e:
                print(f"   ❌ 检测失败: {e}")
                import traceback
                traceback.print_exc()
        
        await browser.close()

async def test_smart_dynamic_handler():
    """测试智能动态处理器"""
    print("\n🧪 测试智能动态处理器...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(test_url, wait_until='networkidle')
            print(f"✅ 成功访问测试页面")
            
            # 创建智能处理器
            smart_handler = SmartDynamicHandler()
            
            # 执行智能处理
            print(f"\n🚀 开始智能动态处理...")
            result = await smart_handler.smart_handle_pagination(
                page=page,
                url=test_url,
                max_pages=2
            )
            
            print(f"📊 处理结果:")
            print(f"   成功: {'✅' if result.get('success', False) else '❌'}")
            
            if result.get('success', False):
                print(f"   使用策略: {result.get('used_strategy', 'Unknown')}")
                print(f"   处理页数: {result.get('pages_processed', 0)}")
                print(f"   收集文章: {len(result.get('articles', []))} 篇")
                
                # 显示前几篇文章
                articles = result.get('articles', [])
                if articles:
                    print(f"   前3篇文章:")
                    for i, article in enumerate(articles[:3]):
                        if isinstance(article, dict):
                            title = article.get('text', 'Unknown')
                            url = article.get('href', 'Unknown')
                        else:
                            title = str(article)[:50]
                            url = 'Unknown'
                        print(f"      [{i+1}] {title}...")
                        print(f"          URL: {url}")
            else:
                print(f"   错误: {result.get('error', 'Unknown error')}")
            
            # 显示检测结果
            detection_result = result.get('detection_result', {})
            if detection_result:
                print(f"\n🔍 检测详情:")
                print(f"   检测类型: {detection_result.get('types', [])}")
                print(f"   置信度: {detection_result.get('confidence', 0):.2f}")
                
        except Exception as e:
            print(f"❌ 智能处理测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

async def test_strategy_registry():
    """测试策略注册表"""
    print("\n🧪 测试策略注册表...")
    
    processor = DynamicTypeProcessor()
    registry = processor.strategy_registry
    
    print(f"📋 已注册的策略:")
    for strategy_name, strategy_info in registry.items():
        print(f"   - {strategy_name}:")
        print(f"     描述: {strategy_info.description}")
        print(f"     优先级: {strategy_info.priority.name} ({strategy_info.priority.value})")
        print(f"     适用域名: {strategy_info.applicable_domains}")
        print(f"     测试URL: {strategy_info.test_urls}")
        print()

async def test_strategy_priority():
    """测试策略优先级排序"""
    print("\n🧪 测试策略优先级排序...")
    
    # 模拟检测结果
    mock_strategies = [
        {'name': 'INFINITE_SCROLL', 'confidence': 0.8, 'priority': 4},
        {'name': 'JSP_WEBSITE', 'confidence': 0.9, 'priority': 2},
        {'name': 'AJAX_PAGINATION', 'confidence': 0.7, 'priority': 3},
        {'name': 'IFRAME_PAGINATION', 'confidence': 0.6, 'priority': 3}
    ]
    
    print(f"原始策略顺序:")
    for strategy in mock_strategies:
        print(f"   {strategy['name']}: 置信度={strategy['confidence']}, 优先级={strategy['priority']}")
    
    # 按优先级排序
    sorted_strategies = sorted(mock_strategies, key=lambda x: x['priority'])
    
    print(f"\n按优先级排序后:")
    for strategy in sorted_strategies:
        print(f"   {strategy['name']}: 置信度={strategy['confidence']}, 优先级={strategy['priority']}")
    
    print(f"\n✅ 优先级排序测试完成")

async def test_caching_mechanism():
    """测试缓存机制"""
    print("\n🧪 测试缓存机制...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(test_url, wait_until='networkidle')
            
            detector = DynamicTypeDetector()
            
            # 第一次检测
            print(f"🔍 第一次检测...")
            import time
            start_time = time.time()
            result1 = await detector.detect_website_type(page, test_url)
            first_duration = time.time() - start_time
            print(f"   耗时: {first_duration:.2f}秒")
            print(f"   缓存大小: {len(detector.detection_cache)}")
            
            # 第二次检测（应该使用缓存）
            print(f"\n🔍 第二次检测（使用缓存）...")
            start_time = time.time()
            result2 = await detector.detect_website_type(page, test_url)
            second_duration = time.time() - start_time
            print(f"   耗时: {second_duration:.2f}秒")
            print(f"   缓存大小: {len(detector.detection_cache)}")
            
            # 验证结果一致性
            if result1['types'] == result2['types']:
                print(f"   ✅ 缓存结果一致")
            else:
                print(f"   ❌ 缓存结果不一致")
            
            # 验证性能提升
            if second_duration < first_duration:
                print(f"   ✅ 缓存提升性能: {((first_duration - second_duration) / first_duration * 100):.1f}%")
            else:
                print(f"   ⚠️ 缓存未提升性能")
                
        except Exception as e:
            print(f"❌ 缓存测试失败: {e}")
        
        finally:
            await browser.close()

async def main():
    """主测试函数"""
    print("🚀 开始智能动态类型检测模块测试...")
    
    # 运行所有测试
    await test_strategy_registry()
    await test_strategy_priority()
    await test_dynamic_type_detection()
    await test_smart_dynamic_handler()
    await test_caching_mechanism()
    
    print("\n🎯 所有测试完成！")
    print("\n📋 模块特性总结:")
    print("✅ 智能网站类型检测")
    print("✅ 多策略处理器")
    print("✅ 优先级排序机制")
    print("✅ 缓存优化")
    print("✅ 统一的策略规则注释标准")
    print("✅ 可扩展的架构设计")

if __name__ == "__main__":
    asyncio.run(main())
