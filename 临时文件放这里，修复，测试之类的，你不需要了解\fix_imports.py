#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复包重组后的导入引用
"""

import os
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_imports_in_file(file_path):
    """修复单个文件的导入语句"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 详细的导入修复规则
        import_fixes = [
            # 核心模块
            (r'from crawler import', 'from core.crawler import'),
            (r'import crawler\b', 'from core import crawler'),
            (r'from failed_url_processor import', 'from core.failed_url_processor import'),
            (r'import failed_url_processor\b', 'from core import failed_url_processor'),
            (r'from PaginationHandler import', 'from core.PaginationHandler import'),
            (r'import PaginationHandler\b', 'from core import PaginationHandler'),
            
            # GUI模块
            (r'from crawler_gui_new import', 'from gui.main_window import'),
            (r'import crawler_gui_new\b', 'from gui import main_window'),
            (r'from gui_config_manager import', 'from gui.config_manager import'),
            (r'import gui_config_manager\b', 'from gui import config_manager'),
            (r'from gui_crawler_thread import', 'from gui.crawler_thread import'),
            (r'import gui_crawler_thread\b', 'from gui import crawler_thread'),
            (r'from gui_utils import', 'from gui.utils import'),
            (r'import gui_utils\b', 'from gui import utils'),
            
            # AI模块
            (r'from ai_selector_analyzer_enhanced import', 'from ai.analyzer import'),
            (r'import ai_selector_analyzer_enhanced\b', 'from ai import analyzer'),
            (r'from gui_ai_helper_enhanced import', 'from ai.helper import'),
            (r'import gui_ai_helper_enhanced\b', 'from ai import helper'),
            (r'from interactive_ai_analyzer import', 'from ai.interactive import'),
            (r'import interactive_ai_analyzer\b', 'from ai import interactive'),
            
            # 模组模块
            (r'from module_manager import', 'from modules.manager import'),
            (r'import module_manager\b', 'from modules import manager'),
            (r'from module_config_manager import', 'from modules.config_manager import'),
            (r'import module_config_manager\b', 'from modules import config_manager'),
            
            # 测试模块
            (r'from selectors_test import', 'from testing.selectors_test import'),
            (r'import selectors_test\b', 'from testing import selectors_test'),
            (r'from selectors_test_config import', 'from testing.config import'),
            (r'import selectors_test_config\b', 'from testing import config'),
            
            # 配置模块
            (r'from myconfig import', 'from config.manager import'),
            (r'import myconfig\b', 'from config import manager'),
            
            # 工具模块
            (r'from txt_clear import', 'from utils.text_cleaner import'),
            (r'import txt_clear\b', 'from utils import text_cleaner'),
            (r'from playwright_optimized_config import', 'from utils.playwright_config import'),
            (r'import playwright_optimized_config\b', 'from utils import playwright_config'),
        ]
        
        # 应用修复规则
        for pattern, replacement in import_fixes:
            content = re.sub(pattern, replacement, content)
        
        # 特殊修复：处理一些复杂的导入情况
        special_fixes = [
            # 修复错误的导入语法
            (r'from module_manager from modules import manager', 'from modules import manager'),
            (r'from crawler from core import crawler', 'from core import crawler'),
            
            # 修复类名引用
            (r'crawler\.', 'core.crawler.'),
            (r'module_manager\.', 'modules.manager.'),
            (r'myconfig\.', 'config.manager.'),
        ]
        
        for pattern, replacement in special_fixes:
            content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"修复导入: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"修复导入失败 {file_path}: {e}")
        return False

def fix_all_imports():
    """修复所有文件的导入"""
    packages = ['core', 'gui', 'ai', 'modules', 'testing', 'config', 'utils']
    fixed_count = 0
    
    # 修复包内文件
    for package in packages:
        if os.path.exists(package):
            for root, dirs, files in os.walk(package):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        if fix_imports_in_file(file_path):
                            fixed_count += 1
    
    # 修复根目录的文件
    root_files = ['main.py']
    for file in root_files:
        if os.path.exists(file):
            if fix_imports_in_file(file):
                fixed_count += 1
    
    logger.info(f"总共修复了 {fixed_count} 个文件的导入")

def create_package_init_files():
    """创建包的__init__.py文件，导出主要类和函数"""
    
    init_contents = {
        'core/__init__.py': '''# core package
from .crawler import *
from .failed_url_processor import *
from .PaginationHandler import *
''',
        
        'gui/__init__.py': '''# gui package
from .main_window import CrawlerGUI
from .config_manager import GUIConfigManager, PaginationConfigHelper
from .crawler_thread import CrawlerThreadManager
from .utils import *
''',
        
        'ai/__init__.py': '''# ai package
from .analyzer import AIAnalyzerWithTesting
from .helper import EnhancedAIConfigManager, LLMConfigDialog
from .interactive import InteractiveAIAnalyzer
''',
        
        'modules/__init__.py': '''# modules package
from .manager import ModuleManager, module_manager
from .config_manager import ModuleConfigManager
''',
        
        'testing/__init__.py': '''# testing package
from .selectors_test import SelectorsTestManager
from .config import *
''',
        
        'config/__init__.py': '''# config package
from .manager import ConfigManager
''',
        
        'utils/__init__.py': '''# utils package
from .text_cleaner import *
from .playwright_config import *
'''
    }
    
    for file_path, content in init_contents.items():
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"更新 {file_path}")
        except Exception as e:
            logger.error(f"更新 {file_path} 失败: {e}")

def verify_imports():
    """验证导入是否正确"""
    logger.info("验证导入...")
    
    test_imports = [
        'from core import crawler',
        'from gui import main_window',
        'from ai import analyzer',
        'from modules import manager',
        'from testing import selectors_test',
        'from config import manager',
        'from utils import text_cleaner'
    ]
    
    for import_stmt in test_imports:
        try:
            exec(import_stmt)
            logger.info(f"✅ {import_stmt}")
        except Exception as e:
            logger.error(f"❌ {import_stmt}: {e}")

def main():
    """主函数"""
    logger.info("开始修复导入引用...")
    
    # 1. 修复所有文件的导入
    fix_all_imports()
    
    # 2. 更新包的__init__.py文件
    create_package_init_files()
    
    # 3. 验证导入
    verify_imports()
    
    logger.info("导入修复完成！")
    
    print("\n🔧 修复内容:")
    print("✅ 更新了所有文件的导入语句")
    print("✅ 创建了包的__init__.py文件")
    print("✅ 修复了导入语法错误")
    print("✅ 统一了导入风格")
    
    print("\n📦 现在可以使用以下导入:")
    print("from core import crawler")
    print("from gui import main_window")
    print("from ai import analyzer, helper")
    print("from modules import manager")
    print("from testing import selectors_test")
    print("from config import manager")
    print("from utils import text_cleaner, playwright_config")

if __name__ == "__main__":
    main()
