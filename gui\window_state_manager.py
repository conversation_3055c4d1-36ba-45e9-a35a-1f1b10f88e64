#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口状态管理器
负责保存和恢复GUI窗口的状态信息
"""

from PyQt5.QtCore import QSettings
from PyQt5.QtWidgets import QApplication


class WindowStateManager:
    """窗口状态管理器类"""
    
    def __init__(self, app_name="CrawlerApp", settings_name="WindowState"):
        """
        初始化窗口状态管理器
        
        Args:
            app_name: 应用程序名称
            settings_name: 设置名称
        """
        self.settings = QSettings(app_name, settings_name)
        
    def save_window_state(self, window):
        """
        保存窗口状态
        
        Args:
            window: QMainWindow实例
        """
        try:
            # 保存窗口几何信息
            self.settings.setValue("geometry", window.saveGeometry())
            
            # 保存窗口状态
            self.settings.setValue("windowState", window.saveState())
            
            # 保存是否最大化
            self.settings.setValue("isMaximized", window.isMaximized())
            
            # 如果不是最大化状态，保存正常窗口的尺寸和位置
            if not window.isMaximized():
                self.settings.setValue("normalGeometry", window.geometry())
            
            # 确保设置被写入
            self.settings.sync()
            
            return True
            
        except Exception as e:
            print(f"保存窗口状态失败: {e}")
            return False
    
    def restore_window_state(self, window, default_geometry=None):
        """
        恢复窗口状态
        
        Args:
            window: QMainWindow实例
            default_geometry: 默认几何信息 (x, y, width, height)
        """
        try:
            # 获取屏幕几何信息
            desktop = QApplication.desktop()
            screen_geometry = desktop.screenGeometry()
            
            # 设置默认值
            if default_geometry:
                default_x, default_y, default_width, default_height = default_geometry
            else:
                default_width = 1200
                default_height = 800
                default_x = max(100, (screen_geometry.width() - default_width) // 2)
                default_y = max(100, (screen_geometry.height() - default_height) // 2)
            
            # 从设置中恢复窗口几何信息
            geometry = self.settings.value("geometry")
            if geometry:
                window.restoreGeometry(geometry)
                
                # 验证窗口是否在可见区域内
                window_geometry = window.geometry()
                if self._is_geometry_valid(window_geometry, screen_geometry):
                    # 几何信息有效，继续恢复其他状态
                    pass
                else:
                    # 窗口位置无效，使用默认位置
                    window.setGeometry(default_x, default_y, default_width, default_height)
            else:
                # 没有保存的几何信息，使用默认设置
                window.setGeometry(default_x, default_y, default_width, default_height)
            
            # 恢复窗口状态（工具栏、停靠窗口等）
            window_state = self.settings.value("windowState")
            if window_state:
                window.restoreState(window_state)
                
            # 恢复是否最大化
            is_maximized = self.settings.value("isMaximized", False, type=bool)
            if is_maximized:
                window.showMaximized()
                
            return True
                
        except Exception as e:
            print(f"恢复窗口状态失败: {e}")
            # 使用默认设置
            if default_geometry:
                window.setGeometry(*default_geometry)
            else:
                window.setGeometry(100, 100, 1200, 800)
            return False
    
    def _is_geometry_valid(self, window_geometry, screen_geometry):
        """
        验证窗口几何信息是否有效
        
        Args:
            window_geometry: 窗口几何信息
            screen_geometry: 屏幕几何信息
        
        Returns:
            bool: 几何信息是否有效
        """
        # 检查窗口是否完全在屏幕外
        if (window_geometry.x() + window_geometry.width() < 0 or
            window_geometry.y() + window_geometry.height() < 0 or
            window_geometry.x() > screen_geometry.width() or
            window_geometry.y() > screen_geometry.height()):
            return False
        
        # 检查窗口大小是否合理
        if (window_geometry.width() < 300 or window_geometry.height() < 200 or
            window_geometry.width() > screen_geometry.width() * 2 or
            window_geometry.height() > screen_geometry.height() * 2):
            return False
        
        return True
    
    def clear_settings(self):
        """清除所有保存的窗口状态设置"""
        try:
            self.settings.clear()
            self.settings.sync()
            return True
        except Exception as e:
            print(f"清除窗口状态设置失败: {e}")
            return False
    
    def get_saved_geometry(self):
        """获取保存的窗口几何信息"""
        try:
            geometry = self.settings.value("geometry")
            normal_geometry = self.settings.value("normalGeometry")
            is_maximized = self.settings.value("isMaximized", False, type=bool)
            
            return {
                "geometry": geometry,
                "normal_geometry": normal_geometry,
                "is_maximized": is_maximized
            }
        except Exception as e:
            print(f"获取保存的几何信息失败: {e}")
            return None
    
    def has_saved_state(self):
        """检查是否有保存的窗口状态"""
        try:
            return self.settings.value("geometry") is not None
        except Exception as e:
            print(f"检查保存状态失败: {e}")
            return False


class MultiWindowStateManager:
    """多窗口状态管理器"""
    
    def __init__(self, app_name="CrawlerApp"):
        """
        初始化多窗口状态管理器
        
        Args:
            app_name: 应用程序名称
        """
        self.app_name = app_name
        self.managers = {}
    
    def get_manager(self, window_name):
        """
        获取指定窗口的状态管理器
        
        Args:
            window_name: 窗口名称
        
        Returns:
            WindowStateManager: 窗口状态管理器实例
        """
        if window_name not in self.managers:
            settings_name = f"{window_name}WindowState"
            self.managers[window_name] = WindowStateManager(self.app_name, settings_name)
        
        return self.managers[window_name]
    
    def save_all_states(self, windows_dict):
        """
        保存所有窗口的状态
        
        Args:
            windows_dict: 窗口字典 {window_name: window_instance}
        
        Returns:
            dict: 保存结果 {window_name: success}
        """
        results = {}
        for window_name, window in windows_dict.items():
            manager = self.get_manager(window_name)
            results[window_name] = manager.save_window_state(window)
        
        return results
    
    def restore_all_states(self, windows_dict, default_geometries=None):
        """
        恢复所有窗口的状态
        
        Args:
            windows_dict: 窗口字典 {window_name: window_instance}
            default_geometries: 默认几何信息字典 {window_name: (x, y, width, height)}
        
        Returns:
            dict: 恢复结果 {window_name: success}
        """
        results = {}
        default_geometries = default_geometries or {}
        
        for window_name, window in windows_dict.items():
            manager = self.get_manager(window_name)
            default_geometry = default_geometries.get(window_name)
            results[window_name] = manager.restore_window_state(window, default_geometry)
        
        return results
    
    def clear_all_settings(self):
        """清除所有窗口的状态设置"""
        results = {}
        for window_name, manager in self.managers.items():
            results[window_name] = manager.clear_settings()
        
        return results
