#!/usr/bin/env python3
"""
调试天津税务局网站页面结构
检查翻页按钮和文章列表的实际状态
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.crawler import launch_browser
from playwright.async_api import async_playwright

async def debug_tjszx_page():
    """调试天津税务局网站页面结构"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("🔍 调试天津税务局网站页面结构")
    print(f"📍 URL: {url}")
    
    async with async_playwright() as p:
        # 启动浏览器（可见模式）
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            # 访问页面
            print("🌐 正在访问页面...")
            await page.goto(url, wait_until='networkidle', timeout=30000)

            # 等待翻页JavaScript加载完成
            print("⏳ 等待翻页JavaScript加载...")
            try:
                # 等待pagetemple容器有内容
                await page.wait_for_function(
                    "document.getElementById('pagetemple') && document.getElementById('pagetemple').innerHTML.trim() !== ''",
                    timeout=10000
                )
                print("✅ 翻页JavaScript已加载")
            except Exception as e:
                print(f"⚠️ 等待翻页JavaScript超时: {e}")

            await page.wait_for_timeout(3000)  # 额外等待确保所有内容加载完成
            
            print(f"✅ 页面加载完成，当前URL: {page.url}")
            
            # 检查页面标题
            title = await page.title()
            print(f"📄 页面标题: {title}")
            
            # 0. 检查pagetemple容器
            print("\n=== 🔍 检查pagetemple容器 ===")
            try:
                pagetemple = await page.query_selector('#pagetemple')
                if pagetemple:
                    content = await pagetemple.inner_html()
                    print(f"✅ 找到pagetemple容器，内容长度: {len(content)}")
                    print(f"内容预览: {content[:200]}...")
                else:
                    print("❌ 未找到pagetemple容器")
            except Exception as e:
                print(f"❌ 检查pagetemple容器时出错: {e}")

            # 1. 检查翻页相关元素
            print("\n=== 🔍 检查翻页相关元素 ===")

            pagination_selectors = [
                '.pagination',
                '.page-nav',
                '.pager',
                '.fenye',
                'div[class*="page"]',
                'div[class*="Page"]',
                '#pagetemple'  # 添加pagetemple容器
            ]
            
            for selector in pagination_selectors:
                elements = await page.query_selector_all(selector)
                if elements:
                    print(f"✅ 找到分页容器: {selector} ({len(elements)} 个)")
                    for i, elem in enumerate(elements):
                        text = await elem.text_content()
                        print(f"   [{i+1}] 内容: {text[:100]}...")
                else:
                    print(f"❌ 未找到: {selector}")
            
            # 2. 检查下一页按钮
            print("\n=== 🔍 检查下一页按钮 ===")
            
            next_selectors = [
                'a:has-text("下一页")',
                'a:has-text("下页")',
                'a:has-text("下一頁")',
                'a.next',
                'a.next-page',
                '.pagination .next',
                'a[title="下一页"]',
                'button:has-text("下一页")',
                'a[href*="index_"]',  # 可能的页码链接
                'a[onclick*="page"]'   # 可能的JS翻页
            ]
            
            for selector in next_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"✅ 找到按钮: {selector} ({len(elements)} 个)")
                        for i, elem in enumerate(elements):
                            text = await elem.text_content()
                            href = await elem.get_attribute('href')
                            onclick = await elem.get_attribute('onclick')
                            is_visible = await elem.is_visible()
                            print(f"   [{i+1}] 文本: '{text}', href: '{href}', onclick: '{onclick}', 可见: {is_visible}")
                    else:
                        print(f"❌ 未找到: {selector}")
                except Exception as e:
                    print(f"❌ 检查 {selector} 时出错: {e}")
            
            # 3. 检查所有包含"下一页"文本的元素
            print("\n=== 🔍 检查所有包含'下一页'的元素 ===")
            try:
                all_elements = await page.query_selector_all('*')
                next_page_elements = []
                for elem in all_elements:
                    try:
                        text = await elem.text_content()
                        if text and '下一页' in text:
                            tag_name = await elem.evaluate('el => el.tagName')
                            class_name = await elem.get_attribute('class')
                            id_attr = await elem.get_attribute('id')
                            href = await elem.get_attribute('href')
                            onclick = await elem.get_attribute('onclick')
                            is_visible = await elem.is_visible()
                            
                            next_page_elements.append({
                                'tag': tag_name,
                                'text': text.strip(),
                                'class': class_name,
                                'id': id_attr,
                                'href': href,
                                'onclick': onclick,
                                'visible': is_visible
                            })
                    except:
                        continue
                
                if next_page_elements:
                    print(f"✅ 找到 {len(next_page_elements)} 个包含'下一页'的元素:")
                    for i, elem in enumerate(next_page_elements):
                        print(f"   [{i+1}] {elem['tag']}: '{elem['text'][:50]}...'")
                        print(f"       class='{elem['class']}', id='{elem['id']}'")
                        print(f"       href='{elem['href']}', onclick='{elem['onclick']}'")
                        print(f"       可见={elem['visible']}")
                        print()
                else:
                    print("❌ 未找到任何包含'下一页'的元素")
            except Exception as e:
                print(f"❌ 搜索包含'下一页'的元素时出错: {e}")
            
            # 4. 检查文章列表
            print("\n=== 🔍 检查文章列表 ===")
            
            list_selectors = [
                'ul li',
                '.list-content li',
                '.list-box li',
                '.news-list li',
                '.content-list li',
                '.main ul li',
                'li a'
            ]
            
            for selector in list_selectors:
                elements = await page.query_selector_all(selector)
                if elements:
                    print(f"✅ 找到列表项: {selector} ({len(elements)} 个)")
                    # 显示前3个项目的详细信息
                    for i, elem in enumerate(elements[:3]):
                        try:
                            text = await elem.text_content()
                            href = await elem.get_attribute('href') if await elem.evaluate('el => el.tagName') == 'A' else None
                            print(f"   [{i+1}] {text[:60]}... | href: {href}")
                        except:
                            print(f"   [{i+1}] (无法获取内容)")
                    if len(elements) > 3:
                        print(f"   ... 还有 {len(elements) - 3} 个项目")
                    break  # 找到第一个有效的选择器就停止
            
            # 5. 截图保存
            await page.screenshot(path='tjszx_debug_page.png', full_page=True)
            print(f"\n📸 页面截图已保存: tjszx_debug_page.png")
            
            # 6. 保存页面HTML
            html_content = await page.content()
            with open('tjszx_debug_page.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"💾 页面HTML已保存: tjszx_debug_page.html")
            
            # 关闭浏览器
            await context.close()
            await browser.close()
            
            print(f"\n🎯 调试完成！请检查截图和HTML文件以了解页面结构")
            
        except Exception as e:
            print(f"❌ 调试过程中出错: {e}")
            await page.screenshot(path='tjszx_debug_error.png')
            try:
                await context.close()
                await browser.close()
            except:
                pass
            raise e

if __name__ == "__main__":
    print("天津税务局网站页面结构调试工具")
    print("=" * 50)
    
    asyncio.run(debug_tjszx_page())
