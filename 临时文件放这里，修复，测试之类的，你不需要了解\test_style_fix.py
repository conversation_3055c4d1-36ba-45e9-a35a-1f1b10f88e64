#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试样式标签清理修复效果
"""

import sys
import os
from bs4 import BeautifulSoup

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from utils.text_cleaner import clean_html_tags, clean_html_before_text_extraction

def test_style_cleaning():
    """测试样式清理功能"""
    
    # 模拟珠海政协网站的HTML结构，包含问题样式
    test_html = '''
    <div class="article_content">
        <style type="text/css">
        .TRS_Editor P{line-height:1.75;font-family:宋体;font-size:12pt;}
        .TRS_Editor DIV{line-height:1.75;font-family:宋体;font-size:12pt;}
        .TRS_Editor TD{line-height:1.75;font-family:宋体;font-size:12pt;}
        .TRS_Editor TH{line-height:1.75;font-family:宋体;font-size:12pt;}
        .TRS_Editor SPAN{line-height:1.75;font-family:宋体;font-size:12pt;}
        .TRS_Editor FONT{line-height:1.75;font-family:宋体;font-size:12pt;}
        .TRS_Editor UL{line-height:1.75;font-family:宋体;font-size:12pt;}
        .TRS_Editor LI{line-height:1.75;font-family:宋体;font-size:12pt;}
        .TRS_Editor A{line-height:1.75;font-family:宋体;font-size:12pt;}
        </style>
        
        <div class="TRS_Editor">
            <p>为充分发挥政协专门协商机构作用，强化委员责任担当，面向社会传递正能量，6月5日，市政协科教文卫体委联合提案委、经济委以及社会和法制委举办2023年第二期"委员讲坛"活动。市政协副主席王红勤、李秉勇、陈依兰出席活动。</p>
            
            <p>本期"委员讲坛"以"新时代法律人的情怀与担当"为主题，四位市政协委员结合自身工作实际，分享法律事务经验及从业感悟。</p>
            
            <p>讲坛中，珠海国际仲裁院党组成员、副院长许智铭科普了仲裁法的定义，概述了市场经济与商务仲裁的趋势，提出打造珠海仲裁新名片的建议；北京市京师（珠海）律师事务所联合创始人沈继光浅析了股权设计比例、股东与公司的人格混同、买卖合同、房屋租赁等有关商事争议的热点法律问题；北京德恒（珠海）律师事务所合伙人郭蕾分享了多个典型案例，讲解了企业合规管理的内容与要求；北京市盈科（珠海）律师事务所高级合伙人滕琛分析了AIGC时代下公司法律顾问的特征，认为法律从业者应紧跟科技最新变革提高生产力和工作效率，进一步贴近市场，为客户提供增值服务。</p>
        </div>
    </div>
    '''
    
    print("🧪 === 样式清理测试 ===")
    print(f"原始HTML长度: {len(test_html)} 字符")
    
    # 测试1: 直接使用BeautifulSoup提取文本（模拟原始问题）
    print("\n1️⃣ 原始方法（直接提取文本）:")
    soup = BeautifulSoup(test_html, 'html.parser')
    original_text = soup.get_text(separator='\n', strip=True)
    print(f"提取文本长度: {len(original_text)} 字符")
    
    if 'TRS_Editor' in original_text:
        print("❌ 包含样式内容")
        style_lines = [line for line in original_text.split('\n') if 'TRS_Editor' in line]
        print(f"样式行数: {len(style_lines)}")
        for line in style_lines[:3]:  # 只显示前3行
            print(f"  {line}")
    else:
        print("✅ 不包含样式内容")
    
    # 测试2: 使用新的HTML清理函数
    print("\n2️⃣ 新方法（先清理HTML再提取文本）:")
    cleaned_html = clean_html_before_text_extraction(test_html)
    print(f"清理后HTML长度: {len(cleaned_html)} 字符")
    
    cleaned_soup = BeautifulSoup(cleaned_html, 'html.parser')
    cleaned_text = cleaned_soup.get_text(separator='\n', strip=True)
    print(f"提取文本长度: {len(cleaned_text)} 字符")
    
    if 'TRS_Editor' in cleaned_text:
        print("❌ 仍包含样式内容")
        style_lines = [line for line in cleaned_text.split('\n') if 'TRS_Editor' in line]
        print(f"样式行数: {len(style_lines)}")
    else:
        print("✅ 样式内容已清理")
    
    # 测试3: 使用改进的clean_html_tags函数
    print("\n3️⃣ 改进的clean_html_tags函数:")
    tags_cleaned_text = clean_html_tags(test_html)
    print(f"清理后文本长度: {len(tags_cleaned_text)} 字符")
    
    if 'TRS_Editor' in tags_cleaned_text:
        print("❌ 仍包含样式内容")
        style_lines = [line for line in tags_cleaned_text.split('\n') if 'TRS_Editor' in line]
        print(f"样式行数: {len(style_lines)}")
    else:
        print("✅ 样式内容已清理")
    
    # 显示清理后的正文内容
    print("\n📄 === 清理后的正文内容 ===")
    print(cleaned_text)
    
    return True

if __name__ == "__main__":
    test_style_cleaning()
