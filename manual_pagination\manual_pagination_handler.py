#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动翻页处理器
用于处理手动填写的URL列表
"""

import pandas as pd
import asyncio
import logging
import os
from typing import List, Dict, Tuple, Optional
from datetime import datetime

# 添加项目根目录到路径
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 不需要导入ArticleExtractor，直接使用页面操作

logger = logging.getLogger(__name__)

class ManualPaginationHandler:
    """手动翻页处理器"""
    
    def __init__(self, page):
        """
        初始化手动翻页处理器
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        self.all_articles = []
        self.processed_urls = []
        self.failed_urls = []
        
    def load_urls_from_excel(self, excel_path: str) -> List[Dict]:
        """
        从Excel文件加载URL列表
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            URL信息列表
        """
        try:
            if not os.path.exists(excel_path):
                logger.error(f"Excel文件不存在: {excel_path}")
                return []
            
            # 读取Excel文件
            df = pd.read_excel(excel_path)
            
            # 检查必需的列
            required_columns = ['URL']
            if not all(col in df.columns for col in required_columns):
                logger.error(f"Excel文件缺少必需的列: {required_columns}")
                return []
            
            # 转换为字典列表
            url_list = []
            for index, row in df.iterrows():
                url_info = {
                    'index': index,
                    'url': str(row['URL']).strip(),
                    'name': str(row.get('页面名称', f'页面{index+1}')).strip(),
                    'status': str(row.get('状态', '待处理')).strip(),
                    'article_count': row.get('文章数量', 0),
                    'note': str(row.get('备注', '')).strip()
                }
                
                # 跳过空URL
                if url_info['url'] and url_info['url'] != 'nan':
                    url_list.append(url_info)
            
            logger.info(f"从Excel文件加载了 {len(url_list)} 个URL")
            return url_list
            
        except Exception as e:
            logger.error(f"加载Excel文件失败: {e}")
            return []
    
    def save_urls_to_excel(self, url_list: List[Dict], excel_path: str) -> bool:
        """
        保存URL列表到Excel文件
        
        Args:
            url_list: URL信息列表
            excel_path: Excel文件路径
            
        Returns:
            是否保存成功
        """
        try:
            # 转换为DataFrame
            df = pd.DataFrame(url_list)
            
            # 重新排列列的顺序
            columns_order = ['URL', '页面名称', '状态', '文章数量', '备注']
            df = df.rename(columns={
                'url': 'URL',
                'name': '页面名称',
                'status': '状态',
                'article_count': '文章数量',
                'note': '备注'
            })
            
            # 确保所有列都存在
            for col in columns_order:
                if col not in df.columns:
                    df[col] = ''
            
            df = df[columns_order]
            
            # 保存到Excel
            df.to_excel(excel_path, index=False)
            logger.info(f"URL列表已保存到: {excel_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")
            return False
    
    def create_template_excel(self, excel_path: str, sample_urls: List[str] = None) -> bool:
        """
        创建Excel模板文件
        
        Args:
            excel_path: Excel文件路径
            sample_urls: 示例URL列表
            
        Returns:
            是否创建成功
        """
        try:
            # 创建示例数据
            if sample_urls is None:
                sample_urls = [
                    "https://www.tjszx.gov.cn/tagz/taxd/index.shtml",
                    "https://www.tjszx.gov.cn/tagz/taxd/index_2.shtml",
                    "https://www.tjszx.gov.cn/tagz/taxd/index_3.shtml"
                ]
            
            data = []
            for i, url in enumerate(sample_urls, 1):
                data.append({
                    'URL': url,
                    '页面名称': f'第{i}页',
                    '状态': '待处理',
                    '文章数量': 0,
                    '备注': '示例URL，请替换为实际URL'
                })
            
            # 创建DataFrame并保存
            df = pd.DataFrame(data)
            df.to_excel(excel_path, index=False)
            
            logger.info(f"Excel模板文件已创建: {excel_path}")
            return True
            
        except Exception as e:
            logger.error(f"创建Excel模板失败: {e}")
            return False
    
    async def process_manual_pagination(
        self,
        excel_path: str,
        extract_config: Dict,
        timeout: int = 30000,
        wait_between_pages: int = 2000,
        save_progress: bool = True
    ) -> Tuple[int, int]:
        """
        处理手动翻页
        
        Args:
            excel_path: Excel文件路径
            extract_config: 文章提取配置
            timeout: 页面加载超时时间
            wait_between_pages: 页面间等待时间
            save_progress: 是否保存进度到Excel
            
        Returns:
            (成功处理的页面数, 总页面数)
        """
        logger.info("🚀 开始手动翻页处理")
        
        # 加载URL列表
        url_list = self.load_urls_from_excel(excel_path)
        if not url_list:
            logger.error("无法加载URL列表")
            return 0, 0
        
        total_pages = len(url_list)
        processed_pages = 0
        
        for url_info in url_list:
            try:
                url = url_info['url']
                page_name = url_info['name']

                logger.info(f"📋 处理页面: {page_name} ({url})")

                # 更新状态为处理中
                url_info['status'] = '处理中'
                if save_progress:
                    self.save_urls_to_excel(url_list, excel_path)

                # 访问页面
                await self.page.goto(url, timeout=timeout)
                await self.page.wait_for_load_state('networkidle', timeout=15000)

                # 等待页面稳定
                await self.page.wait_for_timeout(wait_between_pages)

                # 提取文章链接
                articles = await self._extract_articles_from_page(extract_config)
                
                if articles:
                    # 为每篇文章添加页面信息
                    for article in articles:
                        if len(article) >= 2:
                            article.append(page_name)  # 添加页面名称
                            article.append(url)        # 添加页面URL
                    
                    self.all_articles.extend(articles)
                    article_count = len(articles)
                    
                    logger.info(f"✅ 从 {page_name} 提取了 {article_count} 篇文章")
                    
                    # 更新状态
                    url_info['status'] = '完成'
                    url_info['article_count'] = article_count
                    processed_pages += 1
                    
                else:
                    logger.warning(f"⚠️ 从 {page_name} 未提取到文章")
                    url_info['status'] = '无文章'
                    url_info['article_count'] = 0
                
                self.processed_urls.append(url_info)
                
            except Exception as e:
                logger.error(f"❌ 处理页面 {url_info['name']} 失败: {e}")
                url_info['status'] = f'失败: {str(e)[:50]}'
                url_info['article_count'] = 0
                self.failed_urls.append(url_info)
            
            finally:
                # 保存进度
                if save_progress:
                    self.save_urls_to_excel(url_list, excel_path)
        
        logger.info(f"🎉 手动翻页处理完成: {processed_pages}/{total_pages} 页成功")
        logger.info(f"📊 总共提取了 {len(self.all_articles)} 篇文章")
        
        return processed_pages, total_pages

    async def _extract_articles_from_page(self, extract_config: Dict) -> List:
        """
        从页面提取文章链接

        Args:
            extract_config: 提取配置

        Returns:
            文章列表
        """
        try:
            list_container_selector = extract_config.get('list_container_selector', 'body')
            article_item_selector = extract_config.get('article_item_selector', 'a')
            url_mode = extract_config.get('url_mode', 'absolute')

            # 查找文章链接
            if list_container_selector and list_container_selector != 'body':
                # 在指定容器中查找
                container = await self.page.query_selector(list_container_selector)
                if container:
                    elements = await container.query_selector_all(article_item_selector)
                else:
                    elements = []
            else:
                # 在整个页面中查找
                elements = await self.page.query_selector_all(article_item_selector)

            articles = []
            current_url = self.page.url

            for element in elements:
                try:
                    # 获取链接和标题
                    href = await element.get_attribute('href')
                    title = await element.text_content()

                    if not href or not title:
                        continue

                    # 处理相对URL
                    if url_mode == 'absolute' and href:
                        if href.startswith('http'):
                            full_url = href
                        elif href.startswith('/'):
                            from urllib.parse import urljoin
                            full_url = urljoin(current_url, href)
                        else:
                            from urllib.parse import urljoin
                            full_url = urljoin(current_url, href)
                    else:
                        full_url = href

                    # 清理标题
                    title = title.strip() if title else ""

                    if title and full_url:
                        articles.append([title, full_url])

                except Exception as e:
                    logger.debug(f"提取单个文章失败: {e}")
                    continue

            logger.info(f"从页面提取了 {len(articles)} 篇文章")
            return articles

        except Exception as e:
            logger.error(f"文章提取失败: {e}")
            return []

    def get_all_articles(self) -> List:
        """获取所有提取的文章"""
        return self.all_articles
    
    def get_processed_urls(self) -> List[Dict]:
        """获取已处理的URL信息"""
        return self.processed_urls
    
    def get_failed_urls(self) -> List[Dict]:
        """获取失败的URL信息"""
        return self.failed_urls
    
    def clear_articles(self):
        """清空文章列表"""
        self.all_articles = []
        self.processed_urls = []
        self.failed_urls = []
    
    def get_statistics(self) -> Dict:
        """获取处理统计信息"""
        return {
            'total_articles': len(self.all_articles),
            'processed_pages': len(self.processed_urls),
            'failed_pages': len(self.failed_urls),
            'success_rate': len(self.processed_urls) / (len(self.processed_urls) + len(self.failed_urls)) * 100 if (len(self.processed_urls) + len(self.failed_urls)) > 0 else 0
        }

# 示例使用
async def example_usage():
    """示例使用方法"""
    from playwright.async_api import async_playwright
    from core.crawler import launch_browser
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            # 创建手动翻页处理器
            handler = ManualPaginationHandler(page)
            
            # 创建Excel模板（如果不存在）
            excel_path = "manual_pagination/url_templates.xlsx"
            if not os.path.exists(excel_path):
                handler.create_template_excel(excel_path)
                print(f"请编辑Excel文件: {excel_path}")
                return
            
            # 配置文章提取参数
            extract_config = {
                'list_container_selector': 'body',
                'article_item_selector': 'a[href*="/tagz/system/"]',
                'url_mode': 'absolute'
            }
            
            # 处理手动翻页
            processed, total = await handler.process_manual_pagination(
                excel_path=excel_path,
                extract_config=extract_config,
                timeout=30000,
                wait_between_pages=2000
            )
            
            # 获取结果
            articles = handler.get_all_articles()
            stats = handler.get_statistics()
            
            print(f"处理结果: {processed}/{total} 页成功")
            print(f"提取文章: {stats['total_articles']} 篇")
            print(f"成功率: {stats['success_rate']:.1f}%")
            
        finally:
            await context.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(example_usage())
