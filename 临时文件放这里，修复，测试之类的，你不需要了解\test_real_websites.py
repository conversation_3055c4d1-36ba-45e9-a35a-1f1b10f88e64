#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实网址测试健壮等待策略
"""

import asyncio
import sys
import os
import time
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from utils.robust_wait_strategy import robust_goto, get_success_stats

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_real_websites():
    """测试真实网站的健壮等待策略"""
    
    # 真实测试网址列表
    test_urls = [
        {
            "url": "https://www.shrd.gov.cn/",
            "name": "上海人大官网",
            "description": "政府网站，可能有较慢的加载速度",
            "expected_elements": ["title", ".main", ".content"]
        },
        {
            "url": "https://www.baidu.com",
            "name": "百度首页",
            "description": "快速响应的网站",
            "expected_elements": ["#kw", "#su", ".s_ipt"]
        },
        {
            "url": "https://www.gov.cn/",
            "name": "中国政府网",
            "description": "政府门户网站",
            "expected_elements": ["title", ".main", ".content"]
        },
        {
            "url": "https://news.sina.com.cn/",
            "name": "新浪新闻",
            "description": "新闻网站，内容丰富",
            "expected_elements": ["title", ".news", ".content"]
        },
        {
            "url": "https://httpbin.org/delay/2",
            "name": "延迟测试页面",
            "description": "模拟2秒延迟的测试页面",
            "expected_elements": ["title"]
        },
        {
            "url": "https://httpbin.org/html",
            "name": "HTML测试页面",
            "description": "简单的HTML测试页面",
            "expected_elements": ["h1"]
        }
    ]
    
    print("🧪 开始测试健壮等待策略 - 真实网站测试")
    print("=" * 70)
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled'
            ]
        )
        
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            viewport={'width': 1920, 'height': 1080}
        )
        
        page = await context.new_page()
        
        results = []
        
        for i, test_case in enumerate(test_urls, 1):
            print(f"\n📋 测试 {i}/{len(test_urls)}: {test_case['name']}")
            print(f"🔗 URL: {test_case['url']}")
            print(f"📝 描述: {test_case['description']}")
            print("-" * 50)
            
            start_time = time.time()
            
            try:
                # 使用健壮等待策略访问页面
                logger.info(f"开始访问: {test_case['url']}")
                success = await robust_goto(page, test_case['url'])
                
                load_time = time.time() - start_time
                
                if success:
                    # 获取页面基本信息
                    title = await page.title()
                    content = await page.content()
                    content_length = len(content)
                    url = page.url
                    
                    print(f"✅ 访问成功")
                    print(f"⏱️  加载时间: {load_time:.2f} 秒")
                    print(f"📄 页面标题: {title[:60]}...")
                    print(f"📊 内容长度: {content_length:,} 字符")
                    print(f"🔗 最终URL: {url}")
                    
                    # 测试预期元素是否存在
                    element_results = {}
                    for element in test_case.get('expected_elements', []):
                        try:
                            if element == "title":
                                element_results[element] = bool(title)
                            else:
                                element_count = await page.locator(element).count()
                                element_results[element] = element_count > 0
                        except Exception as e:
                            element_results[element] = False
                            logger.warning(f"元素检查失败 {element}: {e}")
                    
                    if element_results:
                        print(f"🔍 元素检查:")
                        for element, found in element_results.items():
                            status = "✅" if found else "❌"
                            print(f"   {status} {element}")
                    
                    results.append({
                        "name": test_case['name'],
                        "url": test_case['url'],
                        "success": True,
                        "load_time": load_time,
                        "title": title,
                        "content_length": content_length,
                        "final_url": url,
                        "elements": element_results,
                        "error": None
                    })
                    
                else:
                    print(f"❌ 访问失败")
                    print(f"⏱️  尝试时间: {load_time:.2f} 秒")
                    
                    results.append({
                        "name": test_case['name'],
                        "url": test_case['url'],
                        "success": False,
                        "load_time": load_time,
                        "title": None,
                        "content_length": 0,
                        "final_url": None,
                        "elements": {},
                        "error": "所有等待策略都失败"
                    })
                    
            except Exception as e:
                load_time = time.time() - start_time
                error_msg = str(e)
                print(f"❌ 测试异常: {error_msg}")
                print(f"⏱️  异常时间: {load_time:.2f} 秒")
                
                results.append({
                    "name": test_case['name'],
                    "url": test_case['url'],
                    "success": False,
                    "load_time": load_time,
                    "title": None,
                    "content_length": 0,
                    "final_url": None,
                    "elements": {},
                    "error": error_msg
                })
            
            # 短暂休息，避免请求过于频繁
            await asyncio.sleep(1)
        
        await browser.close()
        
        # 显示测试结果总结
        print("\n" + "=" * 70)
        print("📊 测试结果总结")
        print("=" * 70)
        
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        success_rate = success_count / total_count * 100
        avg_load_time = sum(r['load_time'] for r in results) / total_count
        
        print(f"✅ 成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        print(f"⏱️  平均加载时间: {avg_load_time:.2f} 秒")
        print(f"❌ 失败数: {total_count - success_count}")
        
        # 详细结果表格
        print(f"\n📋 详细结果:")
        print(f"{'网站':<15} {'状态':<6} {'时间':<8} {'内容长度':<10} {'错误'}")
        print("-" * 70)
        
        for result in results:
            status = "✅成功" if result['success'] else "❌失败"
            time_str = f"{result['load_time']:.2f}s"
            length_str = f"{result['content_length']:,}" if result['content_length'] > 0 else "0"
            error_str = result['error'][:30] + "..." if result['error'] and len(result['error']) > 30 else (result['error'] or "")
            
            print(f"{result['name'][:14]:<15} {status:<6} {time_str:<8} {length_str:<10} {error_str}")
        
        # 显示等待策略成功率统计
        print(f"\n📈 等待策略成功率统计:")
        stats = get_success_stats()
        if stats:
            for domain, strategies in stats.items():
                print(f"\n🌐 {domain}:")
                for strategy, data in strategies.items():
                    print(f"  📊 {strategy}: {data['success_rate']} ({data['success']}/{data['total']})")
        else:
            print("  📊 暂无统计数据")
        
        # 性能分析
        print(f"\n⚡ 性能分析:")
        successful_results = [r for r in results if r['success']]
        if successful_results:
            fastest = min(successful_results, key=lambda x: x['load_time'])
            slowest = max(successful_results, key=lambda x: x['load_time'])
            
            print(f"  🚀 最快: {fastest['name']} ({fastest['load_time']:.2f}s)")
            print(f"  🐌 最慢: {slowest['name']} ({slowest['load_time']:.2f}s)")
            
            # 按加载时间分类
            fast_count = sum(1 for r in successful_results if r['load_time'] < 3)
            medium_count = sum(1 for r in successful_results if 3 <= r['load_time'] < 10)
            slow_count = sum(1 for r in successful_results if r['load_time'] >= 10)
            
            print(f"  ⚡ 快速 (<3s): {fast_count} 个")
            print(f"  🔄 中等 (3-10s): {medium_count} 个")
            print(f"  🐌 缓慢 (>10s): {slow_count} 个")
        
        return results


async def test_strategy_comparison():
    """比较不同等待策略的效果"""
    print(f"\n🔬 等待策略比较测试")
    print("=" * 50)
    
    test_url = "https://www.baidu.com"
    strategies = ["networkidle", "domcontentloaded", "load", "commit"]
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        print(f"🎯 测试URL: {test_url}")
        print(f"📊 测试策略: {', '.join(strategies)}")
        print("-" * 30)
        
        for strategy in strategies:
            print(f"\n🧪 测试策略: {strategy}")
            start_time = time.time()
            
            try:
                success = await robust_goto(
                    page, 
                    test_url, 
                    preferred_strategy=strategy,
                    timeout=30000
                )
                
                load_time = time.time() - start_time
                
                if success:
                    title = await page.title()
                    content_length = len(await page.content())
                    print(f"  ✅ 成功 - {load_time:.2f}s - {title[:20]}... ({content_length:,} 字符)")
                else:
                    print(f"  ❌ 失败 - {load_time:.2f}s")
                    
            except Exception as e:
                load_time = time.time() - start_time
                print(f"  ❌ 异常 - {load_time:.2f}s - {e}")
        
        await browser.close()


async def main():
    """主函数"""
    print("🚀 健壮等待策略真实网站测试程序")
    print("测试渐进式等待机制在真实网站上的表现")
    print()
    
    try:
        # 主要测试
        results = await test_real_websites()
        
        # 策略比较测试
        await test_strategy_comparison()
        
        print(f"\n🎉 测试完成！")
        print(f"📊 总体成功率: {sum(1 for r in results if r['success'])}/{len(results)}")
        
        # 保存测试结果
        import json
        with open('robust_wait_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"📁 测试结果已保存到: robust_wait_test_results.json")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
