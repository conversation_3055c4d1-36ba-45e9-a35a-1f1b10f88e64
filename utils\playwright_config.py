#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的Playwright配置
针对高失败率问题的优化方案
"""

import asyncio
import random
import time
from typing import Dict, Optional, Tuple
from playwright.async_api import Browser, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, async_playwright

class OptimizedPlaywrightConfig:
    """优化的Playwright配置类"""
    
    # 真实的User-Agent列表
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
    ]

    @classmethod
    def get_random_user_agent(cls):
        """获取随机User-Agent"""
        return random.choice(cls.USER_AGENTS)

    # 基础HTTP请求头
    BASE_HEADERS = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1",
        "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"'
    }
    
    # 浏览器启动参数
    BROWSER_ARGS = [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--disable-gpu",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
        "--disable-features=TranslateUI",
        "--disable-ipc-flooding-protection",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--disable-blink-features=AutomationControlled",
        "--exclude-switches=enable-automation",
        "--disable-extensions"
    ]

async def launch_browser_optimized(
    p,
    headless: bool = True,
    browser_type: str = "chromium",
    slow_mo: int = 0,
    proxy: Optional[Dict] = None,
    viewport: Dict[str, int] = {"width": 1920, "height": 1080},
    user_agent: Optional[str] = None,
    **launch_kwargs
) -> Tuple[Browser, BrowserContext, Page]:
    """
    优化的浏览器启动函数
    
    Args:
        p: async_playwright 实例
        headless: 是否无头模式
        browser_type: 浏览器类型
        slow_mo: 操作延迟
        proxy: 代理设置
        viewport: 视口大小
        user_agent: 用户代理
        
    Returns:
        (browser, context, page) 元组
    """
    # 随机选择User-Agent
    if not user_agent:
        user_agent = random.choice(OptimizedPlaywrightConfig.USER_AGENTS)
    
    # 浏览器启动参数
    launch_args = {
        "headless": headless,
        "slow_mo": slow_mo,
        "args": OptimizedPlaywrightConfig.BROWSER_ARGS.copy()
    }
    
    if proxy:
        launch_args["proxy"] = proxy
    
    # 启动浏览器
    browser = await getattr(p, browser_type).launch(**launch_args)
    
    # 创建上下文
    context_args = {
        "viewport": viewport,
        "user_agent": user_agent,
        "extra_http_headers": OptimizedPlaywrightConfig.BASE_HEADERS.copy(),
        "java_script_enabled": True,
        "bypass_csp": True,
        "ignore_https_errors": True,
        "locale": "zh-CN",
        "timezone_id": "Asia/Shanghai"
    }
    
    context = await browser.new_context(**context_args)
    
    # 注入反检测脚本
    await context.add_init_script("""
        // 移除webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 伪造chrome属性
        window.chrome = {
            runtime: {},
        };
        
        // 伪造权限查询
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        
        // 伪造插件数组
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // 伪造语言
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en'],
        });
    """)
    
    page = await context.new_page()
    
    return browser, context, page


async def visit_page_optimized(
    page: Page,
    url: str,
    max_retries: int = 3,
    timeout: int = 90000,
    wait_strategy: str = "networkidle"
) -> bool:
    """
    优化的页面访问函数 - 现在使用健壮等待策略

    Args:
        page: 页面对象
        url: 目标URL
        max_retries: 最大重试次数（已集成到健壮等待策略中）
        timeout: 超时时间（毫秒）
        wait_strategy: 等待策略（作为首选策略）

    Returns:
        是否访问成功
    """
    try:
        # 导入并使用健壮等待策略
        from .robust_wait_strategy import robust_goto

        print(f"正在访问: {url}")
        success = await robust_goto(
            page,
            url,
            timeout=timeout,
            preferred_strategy=wait_strategy,
            max_retries=max_retries
        )

        if success:
            # 添加随机人类行为
            await simulate_human_behavior(page)
            print(f"✅ 页面访问成功")
        else:
            print(f"🔴 页面访问失败: {url}")

        return success

    except ImportError:
        # 回退到原有实现
        print("⚠️ 健壮等待策略不可用，使用原有方式")
        return await _visit_page_fallback(page, url, max_retries, timeout, wait_strategy)


async def _visit_page_fallback(page: Page, url: str, max_retries: int,
                              timeout: int, wait_strategy: str) -> bool:
    """回退的页面访问实现"""
    for attempt in range(max_retries):
        try:
            # 重试时添加随机延迟
            if attempt > 0:
                delay = random.uniform(2, 5) * (attempt + 1)
                print(f"第 {attempt + 1} 次重试，等待 {delay:.1f} 秒...")
                await asyncio.sleep(delay)

            # 访问页面
            await page.goto(url, timeout=timeout, wait_until=wait_strategy)

            # 额外等待确保内容加载
            if wait_strategy == "networkidle":
                await page.wait_for_load_state("networkidle", timeout=45000)
            else:
                await page.wait_for_load_state("load", timeout=30000)
                await page.wait_for_load_state("networkidle", timeout=15000)

            # 检查页面是否正常加载
            title = await page.title()
            content = await page.content()

            # 验证页面内容
            if not title:
                raise Exception("页面标题为空")

            if len(content) < 1000:
                raise Exception(f"页面内容过短: {len(content)} 字符")

            if any(keyword in title.lower() for keyword in ["error", "404", "403", "500", "blocked"]):
                raise Exception(f"页面显示错误: {title}")

            if any(keyword in content.lower() for keyword in ["access denied", "blocked", "captcha", "验证码"]):
                raise Exception("页面被阻止或需要验证")

            return True

        except Exception as e:
            print(f"❌ 第 {attempt + 1} 次尝试失败: {e}")
            if attempt == max_retries - 1:
                return False

    return False


async def simulate_human_behavior(page: Page):
    """模拟人类行为"""
    try:
        # 随机滚动
        await page.evaluate("""
            window.scrollTo({
                top: Math.random() * 500,
                behavior: 'smooth'
            });
        """)
        
        # 随机等待
        await asyncio.sleep(random.uniform(0.5, 2.0))
        
        # 模拟鼠标移动
        viewport = page.viewport_size
        if viewport:
            x = random.randint(100, viewport["width"] - 100)
            y = random.randint(100, viewport["height"] - 100)
            await page.mouse.move(x, y)
        
    except Exception:
        # 忽略模拟行为的错误
        pass


async def extract_content_optimized(
    page: Page,
    selectors: list,
    selector_type: str = "CSS",
    max_wait: int = 10000
) -> str:
    """
    优化的内容提取函数
    
    Args:
        page: 页面对象
        selectors: 选择器列表
        selector_type: 选择器类型
        max_wait: 最大等待时间
        
    Returns:
        提取的内容
    """
    for selector in selectors:
        try:
            # 等待元素出现
            if selector_type.upper() == "XPATH":
                await page.wait_for_selector(f"xpath={selector}", timeout=max_wait)
                element = await page.query_selector(f"xpath={selector}")
            else:
                await page.wait_for_selector(selector, timeout=max_wait)
                element = await page.query_selector(selector)
            
            if element:
                # 检查元素是否可见
                is_visible = await element.is_visible()
                if not is_visible:
                    continue
                
                # 提取文本内容
                content = await element.text_content()
                if content and content.strip():
                    print(f"✅ 使用选择器 {selector} 成功提取内容")
                    return content.strip()
                
                # 如果文本内容为空，尝试获取innerHTML
                html_content = await element.inner_html()
                if html_content and html_content.strip():
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(html_content, "html.parser")
                    text = soup.get_text(strip=True)
                    if text:
                        print(f"✅ 使用选择器 {selector} 从HTML提取内容")
                        return text
                        
        except Exception as e:
            print(f"⚠️ 选择器 {selector} 失败: {e}")
            continue
    
    print(f"❌ 所有选择器都失败了")
    return ""


# 网站特定的优化配置
SITE_SPECIFIC_CONFIGS = {
    "mp.weixin.qq.com": {
        "timeout": 120000,  # 微信公众号加载较慢
        "wait_strategy": "networkidle",
        "additional_wait": 5000,  # 额外等待时间
        "retry_count": 5,
        "headers": {
            "Referer": "https://mp.weixin.qq.com/",
        }
    },
    "shrd.gov.cn": {
        "timeout": 60000,
        "wait_strategy": "load",
        "additional_wait": 2000,
        "retry_count": 3,
        "headers": {}
    },
    "default": {
        "timeout": 90000,
        "wait_strategy": "networkidle",
        "additional_wait": 3000,
        "retry_count": 3,
        "headers": {}
    }
}


def get_site_config(url: str) -> dict:
    """根据URL获取网站特定配置"""
    from urllib.parse import urlparse
    
    domain = urlparse(url).netloc.lower()
    
    for site_pattern, config in SITE_SPECIFIC_CONFIGS.items():
        if site_pattern in domain:
            return config
    
    return SITE_SPECIFIC_CONFIGS["default"]


# 使用示例
async def example_usage():
    """使用示例"""
    async with async_playwright() as p:
        browser, context, page = await launch_browser_optimized(p)
        
        try:
            url = "https://mp.weixin.qq.com/s/example"
            site_config = get_site_config(url)
            
            success = await visit_page_optimized(
                page, 
                url,
                max_retries=site_config["retry_count"],
                timeout=site_config["timeout"],
                wait_strategy=site_config["wait_strategy"]
            )
            
            if success:
                content = await extract_content_optimized(
                    page,
                    ["#js_content", ".rich_media_content"],
                    "CSS"
                )
                print(f"提取内容长度: {len(content)}")
            
        finally:
            await browser.close()


if __name__ == "__main__":
    asyncio.run(example_usage())
