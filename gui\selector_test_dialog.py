#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选择器测试对话框
测试字段选择器是否能正确提取数据
"""

import sys
import os
import asyncio
import logging
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QProgressBar,
                            QGroupBox, QScrollArea, QWidget, QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logger = logging.getLogger(__name__)

class SelectorTestThread(QThread):
    """选择器测试线程"""
    
    result_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(str)
    
    def __init__(self, test_url, field_selectors):
        super().__init__()
        self.test_url = test_url
        self.field_selectors = field_selectors
    
    def run(self):
        """运行测试"""
        try:
            # 运行异步测试
            result = asyncio.run(self.test_selectors_async())
            self.result_signal.emit(result)
        except Exception as e:
            self.error_signal.emit(str(e))
    
    async def test_selectors_async(self):
        """异步测试选择器"""
        try:
            from playwright.async_api import async_playwright
            from bs4 import BeautifulSoup

            self.progress_signal.emit("正在启动浏览器...")

            async with async_playwright() as p:
                # 启动浏览器，使用更稳定的配置
                browser = await p.chromium.launch(
                    headless=True,
                    args=[
                        '--no-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor'
                    ]
                )

                context = await browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    ignore_https_errors=True
                )

                page = await context.new_page()

                self.progress_signal.emit(f"正在访问页面: {self.test_url}")

                # 使用健壮等待策略访问页面
                try:
                    from utils.robust_wait_strategy import robust_goto
                    success = await robust_goto(page, self.test_url, timeout=30000, preferred_strategy="domcontentloaded")
                    if not success:
                        raise Exception("页面访问失败")
                except ImportError:
                    # 回退到原有方式
                    if not page.is_closed():
                        await page.goto(self.test_url, timeout=30000, wait_until="domcontentloaded")
                        await page.wait_for_load_state('domcontentloaded', timeout=10000)
                    else:
                        raise Exception("页面已关闭，无法访问")
                
                # 检查页面状态
                if page.is_closed():
                    raise Exception("页面在加载后被关闭")

                # 获取页面内容
                content = await page.content()
                soup = BeautifulSoup(content, 'html.parser')

                self.progress_signal.emit("正在测试选择器...")

                # 测试每个字段的选择器
                results = {}

                for field_name, selectors in self.field_selectors.items():
                    field_results = []

                    for i, selector in enumerate(selectors):
                        try:
                            # 检查页面状态
                            if page.is_closed():
                                self.progress_signal.emit("⚠️ 警告: 页面已关闭，无法继续测试")
                                raise Exception("页面在测试过程中被关闭")

                            # 测试选择器
                            elements = soup.select(selector)

                            if elements:
                                # 获取前3个元素的文本
                                texts = []
                                for elem in elements[:3]:
                                    text = elem.get_text(strip=True)
                                    if text:
                                        texts.append(text)

                                field_results.append({
                                    'selector': selector,
                                    'status': 'success',
                                    'count': len(elements),
                                    'samples': texts[:3],
                                    'priority': i + 1
                                })
                                self.progress_signal.emit(f"✅ 选择器 '{selector}' 找到 {len(elements)} 个元素")
                            else:
                                field_results.append({
                                    'selector': selector,
                                    'status': 'no_match',
                                    'count': 0,
                                    'samples': [],
                                    'priority': i + 1
                                })
                                self.progress_signal.emit(f"❌ 选择器 '{selector}' 未找到匹配元素")

                        except Exception as e:
                            field_results.append({
                                'selector': selector,
                                'status': 'error',
                                'error': str(e),
                                'count': 0,
                                'samples': [],
                                'priority': i + 1
                            })
                            self.progress_signal.emit(f"⚠️ 选择器 '{selector}' 测试出错: {e}")

                    results[field_name] = field_results

                # 安全关闭浏览器
                try:
                    if not page.is_closed():
                        await page.close()
                    await browser.close()
                    self.progress_signal.emit("✅ 浏览器已安全关闭")
                except Exception as e:
                    self.progress_signal.emit(f"⚠️ 关闭浏览器时出错: {e}")

                self.progress_signal.emit("✅ 测试完成")
                return results
                
        except Exception as e:
            raise Exception(f"测试选择器时出错: {e}")


class AIAnalysisThread(QThread):
    """AI分析线程"""

    result_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(str)

    def __init__(self, test_url, field_name):
        super().__init__()
        self.test_url = test_url
        self.field_name = field_name

    def run(self):
        """运行AI分析"""
        try:
            # 运行异步AI分析
            result = asyncio.run(self.ai_analysis_async())
            self.result_signal.emit(result)
        except Exception as e:
            self.error_signal.emit(str(e))

    async def ai_analysis_async(self):
        """异步AI分析"""
        try:
            self.progress_signal.emit("🤖 正在初始化AI分析器...")

            # 导入AI分析器
            try:
                from ai.analyzer import AIAnalyzerWithTesting
                analyzer = AIAnalyzerWithTesting()

                if not analyzer.test_manager:
                    raise Exception("AI分析器初始化失败：测试管理器不可用")

                self.progress_signal.emit("🌐 正在分析页面结构...")

                # 执行AI分析
                if self.field_name == 'content':
                    # 分析文章页面
                    analysis_result = await analyzer.analyze_article_page_selectors(self.test_url)
                else:
                    # 使用字段配置分析
                    custom_fields = [self.field_name]
                    analysis_result = await analyzer.analyze_with_field_config(
                        self.test_url,
                        field_preset=None,
                        custom_fields=custom_fields
                    )

                self.progress_signal.emit("🧪 正在测试推荐选择器...")

                # 提取推荐的选择器
                recommended_selectors = []
                confidence = 0.0
                analysis_text = "AI分析完成"

                if analysis_result and 'selectors' in analysis_result:
                    selectors_data = analysis_result['selectors']

                    # 根据字段类型提取选择器
                    field_key = f"{self.field_name}_selectors"
                    if field_key in selectors_data:
                        recommended_selectors = selectors_data[field_key]
                    elif 'content_selectors' in selectors_data and self.field_name == 'content':
                        recommended_selectors = selectors_data['content_selectors']

                    # 提取置信度
                    if 'confidence' in analysis_result:
                        confidence = analysis_result['confidence']

                    # 提取分析说明
                    if 'analysis' in analysis_result:
                        analysis_text = analysis_result['analysis']

                # 如果没有找到推荐选择器，使用默认策略
                if not recommended_selectors:
                    recommended_selectors = self.get_default_selectors(self.field_name)
                    analysis_text = f"AI分析未找到特定选择器，使用默认的 {self.field_name} 选择器策略"
                    confidence = 0.5

                self.progress_signal.emit("✅ 正在验证推荐选择器...")

                # 测试推荐的选择器
                test_config = {f"{self.field_name}_selectors": recommended_selectors}

                # 创建独立的浏览器实例进行测试
                from playwright.async_api import async_playwright

                async with async_playwright() as p:
                    browser = await p.chromium.launch(headless=True)
                    context = await browser.new_context()
                    page = await context.new_page()

                    try:
                        test_result = await analyzer.test_manager.test_selectors_on_page(
                            page,
                            self.test_url,
                            test_config
                        )
                    finally:
                        await browser.close()

                # 构建结果
                result = {
                    'field_name': self.field_name,
                    'recommended_selectors': recommended_selectors,
                    'confidence': confidence,
                    'analysis': analysis_text,
                    'test_results': test_result,
                    'url': self.test_url
                }

                self.progress_signal.emit("🎉 AI分析完成！")
                return result

            except ImportError as e:
                raise Exception(f"AI分析功能不可用: {e}")

        except Exception as e:
            raise Exception(f"AI分析失败: {e}")

    def get_default_selectors(self, field_name):
        """获取默认选择器"""
        default_selectors = {
            'content': [
                'article', '.content', '.article-content', '.post-content',
                '.entry-content', 'main', '.main-content', '#content'
            ],
            'title': [
                'h1', '.title', '.article-title', '.post-title',
                '.entry-title', 'title', '.headline'
            ],
            'date': [
                '.date', '.time', '.publish-time', '.post-date',
                '.entry-date', 'time', '.timestamp'
            ],
            'source': [
                '.source', '.author', '.site', '.publisher',
                '.byline', '.attribution'
            ],
            'author': [
                '.author', '.writer', '.byline', '.author-name',
                '.post-author', '.entry-author'
            ],
            'views': [
                '.views', '.view-count', '.read-count', '.pageviews',
                '.hit-count', '.visit-count'
            ],
            'likes': [
                '.likes', '.like-count', '.praise-count', '.thumbs-up',
                '.upvote', '.favorite-count'
            ],
            'comments': [
                '.comments', '.comment-count', '.reply-count',
                '.discussion-count', '.feedback-count'
            ]
        }

        return default_selectors.get(field_name, [f'.{field_name}', f'#{field_name}'])


class SelectorTestDialog(QDialog):
    """选择器测试对话框"""
    
    def __init__(self, parent=None, test_url="", field_selectors=None):
        super().__init__(parent)
        self.setWindowTitle("选择器测试结果")
        self.setModal(True)
        self.resize(900, 700)
        
        self.test_url = test_url
        self.field_selectors = field_selectors or {}
        
        self.setup_ui()
        self.start_test()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 测试信息
        info_group = QGroupBox("测试信息")
        info_layout = QVBoxLayout()
        
        url_label = QLabel(f"测试URL: {self.test_url}")
        url_label.setWordWrap(True)
        info_layout.addWidget(url_label)
        
        field_count_label = QLabel(f"测试字段数量: {len(self.field_selectors)}")
        info_layout.addWidget(field_count_label)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 不确定进度
        layout.addWidget(self.progress_bar)
        
        # 进度信息
        self.progress_label = QLabel("准备开始测试...")
        layout.addWidget(self.progress_label)
        
        # 结果显示区域
        self.result_area = QScrollArea()
        self.result_widget = QWidget()
        self.result_layout = QVBoxLayout()
        self.result_widget.setLayout(self.result_layout)
        self.result_area.setWidget(self.result_widget)
        self.result_area.setWidgetResizable(True)
        layout.addWidget(self.result_area)
        
        # 按钮
        button_layout = QHBoxLayout()

        # AI分析按钮
        ai_analyze_btn = QPushButton("🤖 AI智能分析")
        ai_analyze_btn.setToolTip("使用AI自动分析页面并生成最佳选择器")
        ai_analyze_btn.clicked.connect(self.start_ai_analysis)
        button_layout.addWidget(ai_analyze_btn)

        # 重新测试按钮
        retest_btn = QPushButton("🔄 重新测试")
        retest_btn.clicked.connect(self.start_test)
        button_layout.addWidget(retest_btn)

        button_layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def start_test(self):
        """开始测试"""
        # 清空结果区域
        for i in reversed(range(self.result_layout.count())):
            child = self.result_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 显示进度
        self.progress_bar.setVisible(True)
        self.progress_label.setText("正在启动测试...")
        
        # 启动测试线程
        self.test_thread = SelectorTestThread(self.test_url, self.field_selectors)
        self.test_thread.result_signal.connect(self.show_results)
        self.test_thread.error_signal.connect(self.show_error)
        self.test_thread.progress_signal.connect(self.update_progress)
        self.test_thread.start()
    
    def update_progress(self, message):
        """更新进度信息"""
        self.progress_label.setText(message)

    def start_ai_analysis(self):
        """开始AI分析"""
        try:
            from PyQt5.QtWidgets import QMessageBox, QInputDialog

            # 询问用户要分析的字段类型
            field_types = [
                "文章内容 (content)",
                "文章标题 (title)",
                "发布日期 (date)",
                "文章来源 (source)",
                "作者信息 (author)",
                "阅读量 (views)",
                "点赞数 (likes)",
                "评论数 (comments)"
            ]

            field_type, ok = QInputDialog.getItem(
                self,
                "AI智能分析",
                "请选择要分析的字段类型：",
                field_types,
                0,
                False
            )

            if not ok:
                return

            # 提取字段名称
            field_name = field_type.split('(')[1].split(')')[0]

            # 显示进度
            self.progress_bar.setVisible(True)
            self.progress_label.setText("🤖 正在启动AI分析...")

            # 启动AI分析线程
            self.ai_thread = AIAnalysisThread(self.test_url, field_name)
            self.ai_thread.result_signal.connect(self.show_ai_results)
            self.ai_thread.error_signal.connect(self.show_ai_error)
            self.ai_thread.progress_signal.connect(self.update_progress)
            self.ai_thread.start()

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"启动AI分析失败: {e}")

    def show_ai_results(self, ai_results):
        """显示AI分析结果"""
        try:
            from PyQt5.QtWidgets import QMessageBox, QTextEdit, QVBoxLayout, QDialog, QPushButton

            # 创建结果显示对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("🤖 AI分析结果")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout()

            # 结果文本
            result_text = QTextEdit()
            result_text.setReadOnly(True)

            # 格式化AI分析结果
            formatted_result = self.format_ai_results(ai_results)
            result_text.setHtml(formatted_result)

            layout.addWidget(result_text)

            # 按钮
            button_layout = QHBoxLayout()

            # 应用选择器按钮
            apply_btn = QPushButton("✅ 应用推荐选择器")
            apply_btn.clicked.connect(lambda: self.apply_ai_selectors(ai_results, dialog))
            button_layout.addWidget(apply_btn)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.accept)
            button_layout.addWidget(close_btn)

            layout.addLayout(button_layout)
            dialog.setLayout(layout)

            # 隐藏进度条
            self.progress_bar.setVisible(False)
            self.progress_label.setText("AI分析完成")

            dialog.exec_()

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"显示AI结果失败: {e}")

    def format_ai_results(self, ai_results):
        """格式化AI分析结果"""
        html = "<h2>🤖 AI智能分析结果</h2>"

        if 'recommended_selectors' in ai_results:
            html += "<h3>📋 推荐选择器</h3>"
            html += "<ul>"
            for selector in ai_results['recommended_selectors']:
                html += f"<li><code>{selector}</code></li>"
            html += "</ul>"

        if 'analysis' in ai_results:
            html += "<h3>📊 分析说明</h3>"
            html += f"<p>{ai_results['analysis']}</p>"

        if 'confidence' in ai_results:
            confidence = ai_results['confidence']
            color = "green" if confidence > 0.8 else "orange" if confidence > 0.6 else "red"
            html += f"<h3>🎯 置信度</h3>"
            html += f"<p style='color: {color}; font-weight: bold;'>{confidence:.1%}</p>"

        if 'test_results' in ai_results:
            html += "<h3>🧪 测试结果</h3>"
            test_results = ai_results['test_results']
            if test_results.get('success'):
                html += "<p style='color: green;'>✅ 选择器测试成功</p>"
                if 'extractions' in test_results:
                    html += "<h4>提取内容预览：</h4>"
                    for field, data in test_results['extractions'].items():
                        if isinstance(data, dict) and 'text' in data:
                            preview = data['text'][:100] + "..." if len(data['text']) > 100 else data['text']
                            html += f"<p><strong>{field}:</strong> {preview}</p>"
            else:
                html += "<p style='color: red;'>❌ 选择器测试失败</p>"
                if 'errors' in test_results:
                    html += "<ul>"
                    for error in test_results['errors']:
                        html += f"<li style='color: red;'>{error}</li>"
                    html += "</ul>"

        return html

    def apply_ai_selectors(self, ai_results, dialog):
        """应用AI推荐的选择器"""
        try:
            if 'recommended_selectors' in ai_results:
                # 更新字段选择器
                field_name = ai_results.get('field_name', 'content')
                selectors = ai_results['recommended_selectors']

                # 更新当前测试的选择器
                self.field_selectors[field_name] = selectors

                # 关闭AI结果对话框
                dialog.accept()

                # 重新开始测试
                self.start_test()

                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "成功", f"已应用AI推荐的 {field_name} 选择器，正在重新测试...")

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"应用AI选择器失败: {e}")

    def show_ai_error(self, error_message):
        """显示AI分析错误"""
        self.progress_bar.setVisible(False)
        self.progress_label.setText("AI分析失败")

        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.warning(self, "AI分析失败", f"AI分析过程中出现错误：\n{error_message}")
    
    def show_error(self, error_message):
        """显示错误"""
        self.progress_bar.setVisible(False)
        self.progress_label.setText(f"测试失败: {error_message}")
        
        error_label = QLabel(f"❌ 测试失败: {error_message}")
        error_label.setStyleSheet("color: red; font-weight: bold; padding: 10px;")
        error_label.setWordWrap(True)
        self.result_layout.addWidget(error_label)
    
    def show_results(self, results):
        """显示测试结果"""
        self.progress_bar.setVisible(False)
        self.progress_label.setText("测试完成")
        
        if not results:
            no_result_label = QLabel("没有测试结果")
            self.result_layout.addWidget(no_result_label)
            return
        
        # 显示每个字段的测试结果
        for field_name, field_results in results.items():
            self.create_field_result_group(field_name, field_results)
    
    def create_field_result_group(self, field_name, field_results):
        """创建字段结果组"""
        # 计算成功的选择器数量
        success_count = sum(1 for r in field_results if r['status'] == 'success')
        total_count = len(field_results)
        
        # 确定状态颜色
        if success_count > 0:
            status_color = "green"
            status_text = f"✅ {success_count}/{total_count} 个选择器有效"
        else:
            status_color = "red"
            status_text = f"❌ 0/{total_count} 个选择器有效"
        
        group = QGroupBox(f"{field_name} - {status_text}")
        group.setStyleSheet(f"QGroupBox::title {{ color: {status_color}; font-weight: bold; }}")
        layout = QVBoxLayout()
        
        # 显示每个选择器的结果
        for result in field_results:
            selector_widget = self.create_selector_result_widget(result)
            layout.addWidget(selector_widget)
        
        group.setLayout(layout)
        self.result_layout.addWidget(group)
    
    def create_selector_result_widget(self, result):
        """创建选择器结果控件"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 选择器信息
        selector = result['selector']
        status = result['status']
        priority = result['priority']
        
        # 状态图标和文本
        if status == 'success':
            status_icon = "✅"
            status_color = "green"
            count = result['count']
            status_text = f"找到 {count} 个元素"
        elif status == 'no_match':
            status_icon = "⚠️"
            status_color = "orange"
            status_text = "未找到匹配元素"
        else:  # error
            status_icon = "❌"
            status_color = "red"
            error = result.get('error', '未知错误')
            status_text = f"错误: {error}"
        
        # 选择器标题
        title_label = QLabel(f"{status_icon} 优先级 {priority}: {selector}")
        title_label.setStyleSheet(f"color: {status_color}; font-weight: bold;")
        layout.addWidget(title_label)
        
        # 状态信息
        status_label = QLabel(f"   {status_text}")
        status_label.setStyleSheet(f"color: {status_color}; margin-left: 20px;")
        layout.addWidget(status_label)
        
        # 示例数据
        if status == 'success' and result.get('samples'):
            samples_label = QLabel("   示例数据:")
            samples_label.setStyleSheet("color: #666; margin-left: 20px;")
            layout.addWidget(samples_label)
            
            for i, sample in enumerate(result['samples'][:3]):
                sample_label = QLabel(f"     {i+1}. {sample}")
                sample_label.setStyleSheet("color: #888; margin-left: 40px;")
                sample_label.setWordWrap(True)
                layout.addWidget(sample_label)
        
        widget.setLayout(layout)
        return widget


if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 测试数据
    test_selectors = {
        "likes": [".like-count", ".praise-num"],
        "views": [".view-count", ".read-num"]
    }
    
    dialog = SelectorTestDialog(None, "https://example.com", test_selectors)
    dialog.show()
    sys.exit(app.exec_())
