#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析合肥市政协网站的文章列表结构
专门针对 JSP 动态网站的内容提取策略
"""

import asyncio
import sys
import os
from playwright.async_api import async_playwright
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def analyze_hfszx_detailed():
    """详细分析合肥市政协网站的文章列表"""
    url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    print("🔍 开始详细分析合肥市政协网站的文章列表...")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = await context.new_page()
        
        try:
            print(f"📋 访问URL: {url}")
            await page.goto(url, wait_until='networkidle', timeout=30000)
            await asyncio.sleep(3)  # 等待JavaScript执行
            
            print("\n🎯 分析页面中的所有表格和列表结构...")
            
            # 获取页面的完整HTML结构分析
            structure_analysis = await page.evaluate("""
                () => {
                    const analysis = {
                        tables: [],
                        lists: [],
                        divs_with_content: [],
                        potential_article_containers: []
                    };
                    
                    // 分析表格
                    const tables = document.querySelectorAll('table');
                    tables.forEach((table, index) => {
                        const rows = table.querySelectorAll('tr');
                        const cells = table.querySelectorAll('td, th');
                        const hasLinks = table.querySelectorAll('a').length > 0;
                        const textContent = table.textContent.trim().substring(0, 200);
                        
                        analysis.tables.push({
                            index: index,
                            rows: rows.length,
                            cells: cells.length,
                            hasLinks: hasLinks,
                            className: table.className,
                            id: table.id,
                            textSample: textContent
                        });
                    });
                    
                    // 分析列表
                    const lists = document.querySelectorAll('ul, ol');
                    lists.forEach((list, index) => {
                        const items = list.querySelectorAll('li');
                        const hasLinks = list.querySelectorAll('a').length > 0;
                        const textContent = list.textContent.trim().substring(0, 200);
                        
                        analysis.lists.push({
                            index: index,
                            items: items.length,
                            hasLinks: hasLinks,
                            className: list.className,
                            id: list.id,
                            textSample: textContent
                        });
                    });
                    
                    // 分析包含链接的div
                    const divsWithLinks = document.querySelectorAll('div');
                    divsWithLinks.forEach((div, index) => {
                        const links = div.querySelectorAll('a');
                        if (links.length > 0) {
                            const textContent = div.textContent.trim().substring(0, 200);
                            if (textContent.length > 20) {  // 过滤掉太短的内容
                                analysis.divs_with_content.push({
                                    index: index,
                                    links: links.length,
                                    className: div.className,
                                    id: div.id,
                                    textSample: textContent
                                });
                            }
                        }
                    });
                    
                    // 查找可能的文章容器（包含多个链接的元素）
                    const allElements = document.querySelectorAll('*');
                    allElements.forEach((element, index) => {
                        const links = element.querySelectorAll('a');
                        const directLinks = Array.from(element.children).filter(child => child.tagName === 'A');
                        
                        if (links.length >= 3 || directLinks.length >= 2) {  // 包含多个链接的元素
                            const textContent = element.textContent.trim().substring(0, 300);
                            if (textContent.length > 50) {
                                analysis.potential_article_containers.push({
                                    tagName: element.tagName,
                                    className: element.className,
                                    id: element.id,
                                    totalLinks: links.length,
                                    directLinks: directLinks.length,
                                    textSample: textContent
                                });
                            }
                        }
                    });
                    
                    return analysis;
                }
            """)
            
            print(f"📊 结构分析结果:")
            print(f"   - 表格数量: {len(structure_analysis['tables'])}")
            print(f"   - 列表数量: {len(structure_analysis['lists'])}")
            print(f"   - 包含链接的div: {len(structure_analysis['divs_with_content'])}")
            print(f"   - 潜在文章容器: {len(structure_analysis['potential_article_containers'])}")
            
            # 详细分析表格（最可能包含文章列表）
            if structure_analysis['tables']:
                print(f"\n📋 表格详细分析:")
                for i, table in enumerate(structure_analysis['tables']):
                    print(f"   表格 {i+1}:")
                    print(f"     - 行数: {table['rows']}, 单元格数: {table['cells']}")
                    print(f"     - 包含链接: {'✅' if table['hasLinks'] else '❌'}")
                    print(f"     - 类名: {table['className'] or '无'}")
                    print(f"     - ID: {table['id'] or '无'}")
                    print(f"     - 内容样本: {table['textSample'][:100]}...")
            
            # 分析潜在的文章容器
            if structure_analysis['potential_article_containers']:
                print(f"\n🎯 潜在文章容器分析:")
                for i, container in enumerate(structure_analysis['potential_article_containers'][:5]):  # 只显示前5个
                    print(f"   容器 {i+1}:")
                    print(f"     - 标签: {container['tagName']}")
                    print(f"     - 类名: {container['className'] or '无'}")
                    print(f"     - 总链接数: {container['totalLinks']}")
                    print(f"     - 直接链接数: {container['directLinks']}")
                    print(f"     - 内容样本: {container['textSample'][:150]}...")
            
            print(f"\n🔗 提取所有文章链接...")
            
            # 提取所有可能的文章链接
            article_links = await page.evaluate("""
                () => {
                    const links = [];
                    const allLinks = document.querySelectorAll('a');
                    
                    allLinks.forEach(link => {
                        const href = link.href;
                        const text = link.textContent.trim();
                        
                        // 过滤掉明显不是文章的链接
                        if (text.length > 5 && 
                            !href.includes('javascript:') && 
                            !href.includes('#') &&
                            !text.includes('首页') &&
                            !text.includes('返回') &&
                            !text.includes('更多') &&
                            text.length < 100) {
                            
                            links.push({
                                href: href,
                                text: text,
                                isJsp: href.includes('.jsp'),
                                hasParams: href.includes('?')
                            });
                        }
                    });
                    
                    return links;
                }
            """)
            
            print(f"🔗 找到 {len(article_links)} 个可能的文章链接:")
            for i, link in enumerate(article_links[:10]):  # 只显示前10个
                print(f"   {i+1}. {link['text']} -> {link['href']}")
            
            # 查找翻页相关元素
            print(f"\n📄 查找翻页元素...")
            pagination_analysis = await page.evaluate("""
                () => {
                    const pagination = {
                        forms: [],
                        inputs: [],
                        buttons: [],
                        links_with_page: []
                    };
                    
                    // 查找表单
                    const forms = document.querySelectorAll('form');
                    forms.forEach(form => {
                        const inputs = form.querySelectorAll('input');
                        const buttons = form.querySelectorAll('button, input[type="button"], input[type="submit"]');
                        
                        pagination.forms.push({
                            action: form.action,
                            method: form.method,
                            inputs: inputs.length,
                            buttons: buttons.length
                        });
                    });
                    
                    // 查找输入框
                    const inputs = document.querySelectorAll('input');
                    inputs.forEach(input => {
                        if (input.name && (input.name.toLowerCase().includes('page') || 
                                          input.name.toLowerCase().includes('num'))) {
                            pagination.inputs.push({
                                name: input.name,
                                type: input.type,
                                value: input.value
                            });
                        }
                    });
                    
                    // 查找按钮
                    const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
                    buttons.forEach(button => {
                        const text = button.textContent || button.value || '';
                        if (text.includes('下一页') || text.includes('下页') || text.includes('下') ||
                            text.includes('上一页') || text.includes('上页') || text.includes('上') ||
                            text.includes('首页') || text.includes('末页') || text.includes('尾页')) {
                            pagination.buttons.push({
                                text: text,
                                type: button.type,
                                onclick: button.onclick ? button.onclick.toString() : null
                            });
                        }
                    });
                    
                    return pagination;
                }
            """)
            
            print(f"📄 翻页元素分析:")
            print(f"   - 表单数量: {len(pagination_analysis['forms'])}")
            print(f"   - 页码输入框: {len(pagination_analysis['inputs'])}")
            print(f"   - 翻页按钮: {len(pagination_analysis['buttons'])}")
            
            if pagination_analysis['buttons']:
                print("   翻页按钮详情:")
                for button in pagination_analysis['buttons']:
                    print(f"     - {button['text']} (类型: {button['type']})")
            
            # 保存详细分析结果
            detailed_result = {
                'url': url,
                'structure_analysis': structure_analysis,
                'article_links': article_links,
                'pagination_analysis': pagination_analysis,
                'analysis_time': '2025-07-24 11:15:00'
            }
            
            with open('hfszx_detailed_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(detailed_result, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 详细分析结果已保存到: hfszx_detailed_analysis.json")
            print("\n🎉 详细分析完成！")
            
        except Exception as e:
            print(f"❌ 分析过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await context.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(analyze_hfszx_detailed())
