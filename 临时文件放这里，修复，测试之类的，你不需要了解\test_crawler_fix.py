#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试爬虫修复
验证crawl_articles_async函数参数问题是否已修复
"""

import asyncio
import inspect
from crawler import crawl_articles_async

def test_function_signature():
    """测试函数签名"""
    print("测试crawl_articles_async函数签名...")
    
    # 获取函数签名
    sig = inspect.signature(crawl_articles_async)
    params = list(sig.parameters.keys())
    
    print(f"函数参数列表: {params}")
    
    # 检查关键参数
    required_params = [
        'all_articles', 'input_url', 'base_url', 'max_pages',
        'content_selectors', 'mode', 'export_filename', 'file_format',
        'retry', 'interval', 'max_workers', 'log_callback', 'stop_check_callback'
    ]
    
    missing_params = []
    for param in required_params:
        if param in params:
            print(f"✅ {param}: 存在")
        else:
            print(f"❌ {param}: 缺失")
            missing_params.append(param)
    
    # 检查不应该存在的参数
    unsupported_params = ['use_module_config']
    for param in unsupported_params:
        if param in params:
            print(f"❌ {param}: 不应该存在但存在了")
            missing_params.append(param)
        else:
            print(f"✅ {param}: 正确地不存在")
    
    return len(missing_params) == 0

def test_gui_thread_config():
    """测试GUI线程配置"""
    print("\n测试GUI线程配置...")
    
    try:
        from gui_crawler_thread import CrawlerThread
        
        # 创建测试配置
        test_config = {
            'input_url': 'https://example.com',
            'base_url': 'https://example.com',
            'max_pages': 1,
            'content_selectors': ['.content'],
            'mode': 'balance',
            'export_filename': 'test',
            'file_format': 'CSV',
            'retry': 2,
            'interval': 0,
            'max_workers': 1,
            'use_module_config': True,  # 这个参数应该被过滤掉
            'module_config': {'enabled': True},  # 这个参数也应该被过滤掉
        }
        
        # 创建线程实例
        thread = CrawlerThread(test_config)
        
        # 测试配置过滤
        filtered_config = thread._prepare_crawler_config()
        
        print(f"原始配置参数数量: {len(test_config)}")
        print(f"过滤后配置参数数量: {len(filtered_config)}")
        
        # 检查不支持的参数是否被过滤掉
        unsupported_params = ['use_module_config', 'module_config']
        for param in unsupported_params:
            if param in filtered_config:
                print(f"❌ {param}: 应该被过滤但仍存在")
                return False
            else:
                print(f"✅ {param}: 正确地被过滤掉")
        
        # 检查支持的参数是否保留
        supported_params = ['input_url', 'content_selectors', 'mode', 'retry']
        for param in supported_params:
            if param in filtered_config:
                print(f"✅ {param}: 正确地保留")
            else:
                print(f"❌ {param}: 应该保留但被过滤掉")
                return False
        
        print("✅ GUI线程配置过滤测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI线程配置测试失败: {e}")
        return False

async def test_async_call():
    """测试异步调用"""
    print("\n测试异步调用...")
    
    try:
        # 测试最小配置调用
        result = await crawl_articles_async(
            all_articles=[],  # 空文章列表，不会实际爬取
            content_selectors=['.content'],
            mode='balance',
            export_filename='test',
            file_format='CSV',
            retry=1,
            interval=0,
            max_workers=1,
            log_callback=lambda msg: print(f"日志: {msg}")
        )
        
        print(f"✅ 异步调用成功，结果: {result}")
        return True
        
    except TypeError as e:
        if "unexpected keyword argument" in str(e):
            print(f"❌ 参数错误: {e}")
            return False
        else:
            print(f"⚠️ 其他类型错误（可能正常）: {e}")
            return True
    except Exception as e:
        print(f"⚠️ 其他异常（可能正常）: {e}")
        return True

def test_module_config_integration():
    """测试模组配置集成"""
    print("\n测试模组配置集成...")
    
    try:
        from crawler import save_article_async
        
        # 检查save_article_async是否支持use_module_config
        sig = inspect.signature(save_article_async)
        params = list(sig.parameters.keys())
        
        if 'use_module_config' in params:
            print("✅ save_article_async支持use_module_config参数")
        else:
            print("❌ save_article_async不支持use_module_config参数")
            return False
        
        # 检查模组管理器
        try:
            from module_manager import module_manager, get_config_for_url
            print("✅ 模组管理器导入成功")
            
            # 测试URL匹配
            test_url = "https://mp.weixin.qq.com/s/test"
            config = get_config_for_url(test_url)
            print(f"✅ URL匹配测试成功: {test_url} -> {type(config)}")
            
        except Exception as e:
            print(f"⚠️ 模组管理器测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模组配置集成测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("爬虫修复验证测试")
    print("="*50)
    
    tests = [
        ("函数签名测试", test_function_signature),
        ("GUI线程配置测试", test_gui_thread_config),
        ("异步调用测试", test_async_call),
        ("模组配置集成测试", test_module_config_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "="*50)
    print("测试结果:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！爬虫修复成功。")
        print("\n修复内容:")
        print("✅ 移除了crawl_articles_async不支持的use_module_config参数")
        print("✅ 保留了stop_check_callback参数支持")
        print("✅ GUI线程配置过滤正常工作")
        print("✅ 模组配置在save_article层面正常集成")
        
        print("\n现在可以正常使用:")
        print("1. GUI传统分页爬取")
        print("2. 模组配置系统")
        print("3. 停止功能")
        print("4. 微信公众号优化配置")
        
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件")

if __name__ == "__main__":
    asyncio.run(main())
