#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的选择器测试功能
验证页面关闭问题是否已解决
"""

import asyncio
import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_selector_functionality():
    """测试选择器功能是否正常工作"""
    
    from playwright.async_api import async_playwright
    from testing.selectors_test import SelectorsTestManager
    
    print("🧪 测试修复后的选择器测试功能")
    print("=" * 50)
    
    # 测试配置
    test_config = {
        'content_selectors': ['body', '.content', 'main', 'article'],
        'title_selectors': ['title', 'h1', '.title'],
        'date_selectors': ['.date', '.time', '.publish-time'],
        'source_selectors': ['.source', '.author', '.site'],
        'content_type': 'CSS'
    }
    
    test_url = "https://www.baidu.com"
    
    # 创建测试管理器
    test_manager = SelectorsTestManager()
    
    async with async_playwright() as p:
        try:
            # 启动浏览器
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            )
            page = await context.new_page()
            
            print(f"✅ 浏览器启动成功")
            print(f"🔗 测试URL: {test_url}")
            print(f"📋 测试配置: {len(test_config)} 个选择器组")
            
            # 测试选择器功能
            print(f"\n🧪 开始测试选择器...")
            result = await test_manager.test_selectors_on_page(page, test_url, test_config)
            
            # 显示结果
            print(f"\n📊 测试结果:")
            print(f"✅ 成功: {result['success']}")
            print(f"🔗 URL: {result['url']}")
            print(f"⏰ 时间: {result['timestamp']}")
            
            if result['extractions']:
                print(f"\n📄 提取结果:")
                for field, data in result['extractions'].items():
                    if isinstance(data, dict):
                        print(f"  {field}: {data.get('text', '')[:50]}...")
                        print(f"    选择器: {data.get('selector', 'N/A')}")
                    else:
                        print(f"  {field}: {str(data)[:50]}...")
            
            if result['errors']:
                print(f"\n❌ 错误信息:")
                for error in result['errors']:
                    print(f"  - {error}")
            
            # 检查页面状态
            if not page.is_closed():
                print(f"\n✅ 页面状态正常，未被意外关闭")
                
                # 尝试再次访问页面
                print(f"\n🔄 测试页面重复访问...")
                result2 = await test_manager.test_selectors_on_page(page, "https://httpbin.org/html", test_config)
                print(f"✅ 重复访问成功: {result2['success']}")
                
            else:
                print(f"\n❌ 页面被意外关闭")
            
            await browser.close()
            print(f"\n✅ 浏览器正常关闭")
            
            return result
            
        except Exception as e:
            print(f"\n❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None


async def test_ai_analyzer_integration():
    """测试AI分析器集成"""
    
    print(f"\n🤖 测试AI分析器集成")
    print("=" * 30)
    
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        
        # 创建AI分析器
        analyzer = AIAnalyzerWithTesting()
        
        if analyzer.test_manager:
            print(f"✅ AI分析器中的测试管理器初始化成功")
            
            # 测试简单的选择器测试
            from playwright.async_api import async_playwright
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context()
                page = await context.new_page()
                
                test_config = {
                    'content_selectors': ['body'],
                    'title_selectors': ['title'],
                }
                
                result = await analyzer.test_manager.test_selectors_on_page(
                    page, "https://httpbin.org/html", test_config
                )
                
                print(f"✅ AI分析器集成测试成功: {result['success']}")
                
                await browser.close()
        else:
            print(f"❌ AI分析器中的测试管理器未初始化")
            
    except Exception as e:
        print(f"❌ AI分析器集成测试失败: {e}")


async def test_concurrent_access():
    """测试并发访问是否会导致页面关闭问题"""
    
    print(f"\n🔄 测试并发访问")
    print("=" * 20)
    
    from playwright.async_api import async_playwright
    from testing.selectors_test import SelectorsTestManager
    
    test_manager = SelectorsTestManager()
    
    test_config = {
        'content_selectors': ['body'],
        'title_selectors': ['title'],
    }
    
    urls = [
        "https://httpbin.org/html",
        "https://www.baidu.com",
        "https://httpbin.org/delay/1"
    ]
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()
        
        # 创建多个页面进行并发测试
        tasks = []
        pages = []
        
        for i, url in enumerate(urls):
            page = await context.new_page()
            pages.append(page)
            
            task = test_manager.test_selectors_on_page(page, url, test_config)
            tasks.append(task)
        
        try:
            # 并发执行
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            print(f"📊 并发测试结果:")
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    print(f"  URL {i+1}: ❌ 异常 - {result}")
                else:
                    print(f"  URL {i+1}: {'✅ 成功' if result['success'] else '❌ 失败'}")
            
            # 检查页面状态
            closed_pages = sum(1 for page in pages if page.is_closed())
            print(f"📄 页面状态: {len(pages) - closed_pages}/{len(pages)} 个页面仍然打开")
            
        except Exception as e:
            print(f"❌ 并发测试失败: {e}")
        
        finally:
            # 清理资源
            for page in pages:
                if not page.is_closed():
                    await page.close()
            await browser.close()


async def main():
    """主函数"""
    print("🚀 选择器测试功能修复验证")
    print("验证页面关闭问题是否已解决")
    print()
    
    try:
        # 基本功能测试
        result = await test_selector_functionality()
        
        if result and result['success']:
            print(f"\n🎉 基本功能测试通过！")
        else:
            print(f"\n⚠️ 基本功能测试未完全通过")
        
        # AI分析器集成测试
        await test_ai_analyzer_integration()
        
        # 并发访问测试
        await test_concurrent_access()
        
        print(f"\n🎉 所有测试完成！")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
