#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试iframe中的翻页结构
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright

async def debug_iframe_pagination():
    """调试iframe中的翻页结构"""
    print("🔍 调试iframe中的翻页结构...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(test_url, wait_until='networkidle')
            print(f"✅ 成功访问页面")
            
            # 1. 检查页面中的iframe
            print(f"\n🖼️ 检查页面中的iframe:")
            iframes = await page.query_selector_all('iframe')
            print(f"   找到 {len(iframes)} 个iframe")
            
            for i, iframe in enumerate(iframes):
                src = await iframe.get_attribute('src')
                name = await iframe.get_attribute('name')
                id_attr = await iframe.get_attribute('id')
                print(f"   [{i+1}] src: '{src}', name: '{name}', id: '{id_attr}'")
            
            # 2. 检查每个iframe的内容
            for i, iframe in enumerate(iframes):
                print(f"\n📋 检查iframe[{i+1}]的内容:")
                try:
                    # 获取iframe的frame对象
                    frame = await iframe.content_frame()
                    if frame:
                        # 等待iframe内容加载
                        await frame.wait_for_load_state('networkidle')
                        
                        # 检查iframe中的翻页元素
                        print(f"   🔍 查找翻页相关元素...")
                        
                        # 查找所有可能的翻页链接
                        pagination_links = await frame.evaluate("""
                            () => {
                                const links = [];
                                document.querySelectorAll('a').forEach((link, index) => {
                                    const text = link.textContent.trim();
                                    const href = link.getAttribute('href') || '';
                                    const onclick = link.getAttribute('onclick') || '';
                                    const className = link.className || '';
                                    
                                    // 查找翻页相关的链接
                                    if (text.includes('下一页') || text.includes('下页') || text === '>' || 
                                        text.includes('Next') || text.includes('next') ||
                                        href.includes('javascript:') || onclick.includes('goto') ||
                                        onclick.includes('nextPage') || onclick.includes('goPage') ||
                                        /^\\d+$/.test(text) || text.includes('页')) {
                                        links.push({
                                            index: index,
                                            text: text,
                                            href: href,
                                            onclick: onclick,
                                            className: className,
                                            outerHTML: link.outerHTML
                                        });
                                    }
                                });
                                return links;
                            }
                        """)
                        
                        if pagination_links:
                            print(f"   ✅ 找到 {len(pagination_links)} 个翻页相关链接:")
                            for j, link in enumerate(pagination_links):
                                print(f"      [{j+1}] 文本: '{link['text']}'")
                                print(f"          href: '{link['href']}'")
                                print(f"          onclick: '{link['onclick']}'")
                                print(f"          class: '{link['className']}'")
                                print()
                        else:
                            print(f"   ❌ 未找到翻页链接")
                        
                        # 检查JavaScript函数
                        print(f"   🔧 检查JavaScript翻页函数:")
                        js_functions = await frame.evaluate("""
                            () => {
                                const functions = {};
                                
                                // 检查常见函数
                                if (typeof goto === 'function') {
                                    functions.goto = goto.toString().substring(0, 200);
                                }
                                if (typeof nextPage === 'function') {
                                    functions.nextPage = nextPage.toString().substring(0, 200);
                                }
                                if (typeof goPage === 'function') {
                                    functions.goPage = goPage.toString().substring(0, 200);
                                }
                                
                                // 检查其他可能的翻页函数
                                for (let prop in window) {
                                    if (typeof window[prop] === 'function' && 
                                        (prop.toLowerCase().includes('page') || 
                                         prop.toLowerCase().includes('goto') ||
                                         prop.toLowerCase().includes('next'))) {
                                        functions[prop] = window[prop].toString().substring(0, 200);
                                    }
                                }
                                
                                return functions;
                            }
                        """)
                        
                        if js_functions:
                            for func_name, source in js_functions.items():
                                print(f"      - {func_name}: {source}...")
                        else:
                            print(f"      ❌ 未找到JavaScript翻页函数")
                        
                        # 尝试测试翻页选择器
                        print(f"   🖱️ 测试翻页选择器:")
                        test_selectors = [
                            'a:has-text("下一页")',
                            'a:has-text("下页")', 
                            'a:has-text(">")',
                            'a[href*="javascript:goto"]',
                            'a[onclick*="goto"]',
                            'a[href*="javascript:nextPage"]',
                            'a[onclick*="nextPage"]',
                            'a[onclick*="javascript:"]'
                        ]
                        
                        working_selectors = []
                        for selector in test_selectors:
                            try:
                                elements = await frame.query_selector_all(selector)
                                if elements:
                                    print(f"      ✅ {selector}: {len(elements)} 个元素")
                                    working_selectors.append(selector)
                                    
                                    # 获取第一个元素的详细信息
                                    first_elem = elements[0]
                                    text = await first_elem.text_content()
                                    href = await first_elem.get_attribute('href')
                                    onclick = await first_elem.get_attribute('onclick')
                                    print(f"         第一个元素: 文本='{text}', href='{href}', onclick='{onclick}'")
                                else:
                                    print(f"      ❌ {selector}: 未找到")
                            except Exception as e:
                                print(f"      ❌ {selector}: 错误 - {e}")
                        
                        if working_selectors:
                            print(f"\n   🎯 可用的选择器: {working_selectors}")
                        
                    else:
                        print(f"   ❌ 无法获取iframe内容")
                        
                except Exception as e:
                    print(f"   ❌ 检查iframe[{i+1}]时出错: {e}")
            
            # 3. 如果没有iframe，检查是否有其他容器
            if not iframes:
                print(f"\n📄 没有iframe，检查页面主体内容...")
                
                # 检查是否有动态加载的内容
                await page.wait_for_timeout(3000)  # 等待3秒让动态内容加载
                
                # 再次检查翻页元素
                print(f"🔍 重新检查主页面的翻页元素...")
                all_elements = await page.evaluate("""
                    () => {
                        const elements = [];
                        
                        // 检查所有可能包含翻页的元素
                        document.querySelectorAll('*').forEach(elem => {
                            const text = elem.textContent || '';
                            const tagName = elem.tagName.toLowerCase();
                            
                            if ((text.includes('下一页') || text.includes('下页') || text.includes('>')) &&
                                (tagName === 'a' || tagName === 'button' || tagName === 'span' || tagName === 'div')) {
                                elements.push({
                                    tagName: tagName,
                                    text: text.trim(),
                                    innerHTML: elem.innerHTML,
                                    outerHTML: elem.outerHTML.substring(0, 200)
                                });
                            }
                        });
                        
                        return elements;
                    }
                """)
                
                if all_elements:
                    print(f"   找到 {len(all_elements)} 个可能的翻页元素:")
                    for elem in all_elements[:5]:  # 只显示前5个
                        print(f"      {elem['tagName']}: '{elem['text'][:50]}...'")
                else:
                    print(f"   ❌ 仍未找到翻页元素")
            
        except Exception as e:
            print(f"❌ 调试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_iframe_pagination())
