#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复微信公众号爬取问题
"""

import asyncio
import csv
import os
from playwright.async_api import async_playwright
import time
import random

class WeChatCrawler:
    """微信公众号专用爬虫"""
    
    def __init__(self):
        self.success_count = 0
        self.failed_count = 0
        self.results = []
    
    async def create_browser_context(self, p):
        """创建浏览器上下文"""
        browser = await p.chromium.launch(
            headless=False,  # 使用非无头模式，更难被检测
            args=[
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-extensions",
                "--disable-default-apps",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_http_headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1"
            }
        )
        
        # 添加反检测脚本
        await context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            window.chrome = { runtime: {} };
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
        """)
        
        return browser, context
    
    async def crawl_single_url(self, url, title="", max_retries=3):
        """爬取单个URL"""
        print(f"\n🔍 处理URL: {url}")
        
        for attempt in range(max_retries):
            try:
                async with async_playwright() as p:
                    browser, context = await self.create_browser_context(p)
                    page = await context.new_page()
                    
                    try:
                        print(f"📱 尝试 {attempt + 1}/{max_retries} - 访问页面...")
                        
                        # 访问页面
                        response = await page.goto(url, timeout=90000, wait_until="networkidle")
                        
                        if response.status != 200:
                            print(f"⚠️ HTTP状态码: {response.status}")
                            if response.status == 404:
                                return self.create_result(url, title, "", "", "文章不存在或已删除", False)
                        
                        # 等待页面完全加载
                        await page.wait_for_load_state('networkidle', timeout=45000)
                        await asyncio.sleep(random.uniform(3, 6))  # 随机等待
                        
                        # 检查是否被重定向
                        current_url = page.url
                        if "mp.weixin.qq.com" not in current_url:
                            print(f"⚠️ 页面被重定向到: {current_url}")
                            return self.create_result(url, title, "", "", "页面被重定向", False)
                        
                        # 提取标题
                        extracted_title = await self.extract_title(page)
                        if not extracted_title:
                            extracted_title = title or "未知标题"
                        
                        # 提取内容
                        content = await self.extract_content(page)
                        
                        if not content or len(content.strip()) < 100:
                            print(f"⚠️ 内容为空或过短: {len(content) if content else 0} 字符")
                            
                            # 尝试等待更长时间
                            if attempt < max_retries - 1:
                                print("⏳ 等待更长时间后重试...")
                                await asyncio.sleep(10)
                                continue
                            else:
                                return self.create_result(url, extracted_title, "", "", "内容为空或过短", False)
                        
                        # 提取发布时间和来源
                        publish_time = await self.extract_publish_time(page)
                        source = await self.extract_source(page)
                        
                        print(f"✅ 成功提取内容: {len(content)} 字符")
                        return self.create_result(url, extracted_title, content, publish_time, source, True)
                        
                    finally:
                        await browser.close()
                        
            except Exception as e:
                print(f"❌ 尝试 {attempt + 1} 失败: {e}")
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(1, 3)
                    print(f"⏳ 等待 {wait_time:.1f} 秒后重试...")
                    await asyncio.sleep(wait_time)
        
        return self.create_result(url, title, "", "", f"重试{max_retries}次后仍失败", False)
    
    async def extract_title(self, page):
        """提取标题"""
        selectors = [
            "#activity-name",
            ".rich_media_title",
            "h1.rich_media_title",
            ".rich_media_area_primary h1",
            "[data-role='title']",
            "h1"
        ]
        
        for selector in selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    text = await element.inner_text()
                    if text and text.strip():
                        return text.strip()
            except:
                continue
        
        return ""
    
    async def extract_content(self, page):
        """提取内容"""
        selectors = [
            "#js_content",
            ".rich_media_content",
            "#img-content",
            ".rich_media_area_primary",
            "[data-role='outer']",
            ".rich_media_wrp"
        ]
        
        for selector in selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    text = await element.inner_text()
                    if text and len(text.strip()) > 100:
                        return text.strip()
            except:
                continue
        
        return ""
    
    async def extract_publish_time(self, page):
        """提取发布时间"""
        selectors = [
            "#publish_time",
            ".rich_media_meta_text",
            "#post-date",
            ".rich_media_meta_list .rich_media_meta_text"
        ]
        
        for selector in selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    text = await element.inner_text()
                    if text and text.strip():
                        return text.strip()
            except:
                continue
        
        return ""
    
    async def extract_source(self, page):
        """提取来源"""
        selectors = [
            ".rich_media_meta_nickname",
            "#js_name",
            ".profile_nickname",
            ".rich_media_meta_list .rich_media_meta_nickname"
        ]
        
        for selector in selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    text = await element.inner_text()
                    if text and text.strip():
                        return text.strip()
            except:
                continue
        
        return ""
    
    def create_result(self, url, title, content, publish_time, source, success):
        """创建结果记录"""
        return {
            'url': url,
            'title': title,
            'content': content,
            'publish_time': publish_time,
            'source': source,
            'success': success,
            'content_length': len(content) if content else 0
        }
    
    async def process_failed_urls(self, failed_csv_path, output_dir="articles", max_urls=10):
        """处理失败的URL"""
        print(f"📂 读取失败文件: {failed_csv_path}")
        
        # 读取失败的URL
        urls_to_process = []
        try:
            with open(failed_csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row.get('failed_url'):
                        urls_to_process.append({
                            'url': row['failed_url'],
                            'title': row.get('title', '')
                        })
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return
        
        if not urls_to_process:
            print("⚠️ 没有找到需要处理的URL")
            return
        
        # 限制处理数量
        if len(urls_to_process) > max_urls:
            print(f"⚠️ URL数量过多({len(urls_to_process)})，限制处理前{max_urls}个")
            urls_to_process = urls_to_process[:max_urls]
        
        print(f"🚀 开始处理 {len(urls_to_process)} 个URL")
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 处理每个URL
        for i, url_info in enumerate(urls_to_process):
            print(f"\n📋 进度: {i+1}/{len(urls_to_process)}")
            
            result = await self.crawl_single_url(url_info['url'], url_info['title'])
            self.results.append(result)
            
            if result['success']:
                self.success_count += 1
                # 保存成功的文章
                await self.save_article(result, output_dir)
            else:
                self.failed_count += 1
            
            # 进度报告
            print(f"📊 当前统计: 成功 {self.success_count}, 失败 {self.failed_count}")
            
            # 间隔等待，避免被检测
            if i < len(urls_to_process) - 1:
                wait_time = random.uniform(5, 10)
                print(f"⏳ 等待 {wait_time:.1f} 秒...")
                await asyncio.sleep(wait_time)
        
        # 保存结果
        await self.save_results(output_dir)
        
        print(f"\n🎉 处理完成!")
        print(f"📊 总计: {len(urls_to_process)}, 成功: {self.success_count}, 失败: {self.failed_count}")
        print(f"📈 成功率: {(self.success_count/len(urls_to_process)*100):.1f}%")
    
    async def save_article(self, result, output_dir):
        """保存文章"""
        if not result['success'] or not result['content']:
            return
        
        # 创建文件名
        safe_title = "".join(c for c in result['title'] if c.isalnum() or c in (' ', '-', '_')).strip()
        if not safe_title:
            safe_title = f"article_{int(time.time())}"
        
        filename = f"{safe_title}.txt"
        filepath = os.path.join(output_dir, filename)
        
        # 保存文章内容
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"标题: {result['title']}\n")
                f.write(f"来源: {result['source']}\n")
                f.write(f"发布时间: {result['publish_time']}\n")
                f.write(f"URL: {result['url']}\n")
                f.write(f"内容长度: {result['content_length']} 字符\n")
                f.write("-" * 50 + "\n")
                f.write(result['content'])
            
            print(f"💾 已保存: {filename}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    async def save_results(self, output_dir):
        """保存处理结果"""
        # 保存成功的URL列表
        success_file = os.path.join(output_dir, "wechat_success.csv")
        failed_file = os.path.join(output_dir, "wechat_failed.csv")
        
        try:
            # 保存成功的
            with open(success_file, 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['URL', '标题', '来源', '发布时间', '内容长度'])
                for result in self.results:
                    if result['success']:
                        writer.writerow([
                            result['url'],
                            result['title'],
                            result['source'],
                            result['publish_time'],
                            result['content_length']
                        ])
            
            # 保存失败的
            with open(failed_file, 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['URL', '标题', '失败原因'])
                for result in self.results:
                    if not result['success']:
                        writer.writerow([
                            result['url'],
                            result['title'],
                            result['source']  # 这里存储失败原因
                        ])
            
            print(f"📊 结果已保存:")
            print(f"  成功: {success_file}")
            print(f"  失败: {failed_file}")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

async def main():
    """主函数"""
    print("微信公众号爬取修复工具")
    print("=" * 40)
    
    crawler = WeChatCrawler()
    
    # 处理失败的URL
    failed_file = "articles/重试结果_failed.csv"
    if os.path.exists(failed_file):
        await crawler.process_failed_urls(failed_file, max_urls=5)  # 先测试5个
    else:
        print(f"❌ 失败文件不存在: {failed_file}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
