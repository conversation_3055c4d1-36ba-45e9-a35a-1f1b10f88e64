#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新版文章智能采集器GUI
使用新版core.crawler.py和模块化设计
"""

import sys
import os
import time
import json
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QVBoxLayout, QHBoxLayout,
                            QGroupBox, QGridLayout, QProgressBar,
                            QComboBox, QTabWidget, QCheckBox, QSpinBox, QDoubleSpinBox,
                            QDialog, QMessageBox, QFileDialog, QScrollArea, QTreeWidget,
                            QTreeWidgetItem, QSplitter, QMenu, QAction, QInputDialog,
                            QFormLayout)
from PyQt5.QtCore import Qt, QTimer, QSettings
from PyQt5.QtGui import QFont

# 导入新的模块化组件
from ai.helper import EnhancedA<PERSON>onfigManager, LLMConfigDialog, fill_gui_from_ai_result
from gui.crawler_thread import <PERSON>rawler<PERSON>hreadManager
from gui.config_manager import GUIConfigManager, PaginationConfigHelper
from gui.window_state_manager import WindowStateManager
from gui.utils import (get_application_stylesheet, show_info_message, show_warning_message,
                      show_error_message, show_question_message, get_text_input,
                      format_result_message, ConfigValidator)
from gui.custom_selector_dialog import CustomSelectorDialog
from gui.selector_test_dialog import SelectorTestDialog

# 尝试导入模组管理器
try:
    import modules
    MODULE_MANAGER_AVAILABLE = True
except ImportError:
    MODULE_MANAGER_AVAILABLE = False
    print("警告: 模组管理器不可用")

# 尝试导入字段配置管理器
try:
    from core.field_config_manager import (get_field_config_manager, apply_field_preset,
                                          apply_custom_field_list, get_field_presets,
                                          get_available_field_names)
    FIELD_CONFIG_AVAILABLE = True
except ImportError:
    FIELD_CONFIG_AVAILABLE = False
    print("警告: 字段配置管理器不可用")


class CrawlerGUI(QMainWindow):
    """新版文章智能采集器GUI主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("文章智能采集器 v2.0 (新版)")

        # 初始化窗口状态管理器
        self.window_state_manager = WindowStateManager("HILLSUN", "MainWindow")

        # 设置默认窗口大小和位置
        default_geometry = (100, 100, 1200, 800)
        self.setGeometry(*default_geometry)

        # 恢复窗口状态
        self.window_state_manager.restore_window_state(self, default_geometry)

        # 设置应用样式 - 使用微软雅黑13px
        font = QFont("Microsoft YaHei", 13)
        self.setFont(font)

        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                margin-top: 12px;
                padding: 12px;
                color: #333;
                font-size: 14px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                font-size: 14px;
            }
            QLineEdit, QComboBox {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 6px;
                font-size: 13px;
                font-family: "Microsoft YaHei";
                min-height: 28px;
            }
            QLineEdit:focus, QComboBox:focus {
                border: 2px solid #0078d4;
            }
            QPushButton {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 6px 12px;
                font-size: 13px;
                font-family: "Microsoft YaHei";
                min-height: 28px;
                background-color: #f8f8f8;
            }
            QPushButton:hover {
                background-color: #e8e8e8;
                border: 1px solid #999;
            }
            QPushButton:pressed {
                background-color: #ddd;
            }
            QLabel {
                font-size: 13px;
                font-family: "Microsoft YaHei";
                color: #333;
                padding: 2px;
            }
        """)

        # 初始化管理器
        self.config_manager = GUIConfigManager()
        self.ai_manager = EnhancedAIConfigManager()
        self.crawler_manager = CrawlerThreadManager()

        # 失败URL处理控制
        self.failed_processing_stop_flag = False
        self.failed_processing_thread = None

        # 初始化界面
        self.init_ui()

        # 加载配置
        self.load_config()

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 保存窗口状态
            self.window_state_manager.save_window_state(self)

            # 停止所有正在运行的任务
            if hasattr(self, 'crawler_manager'):
                self.crawler_manager.stop_crawling(lambda msg: None)

            # 停止失败URL处理
            if hasattr(self, 'failed_processing_thread') and self.failed_processing_thread:
                self.failed_processing_stop_flag = True
                self.failed_processing_thread.wait(3000)  # 等待最多3秒

            # 接受关闭事件
            event.accept()

        except Exception as e:
            print(f"关闭窗口时出错: {e}")
            event.accept()

    def init_ui(self):
        """初始化用户界面"""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 使用分割器实现可调整的布局
        self.main_splitter = QSplitter(Qt.Horizontal)
        self.main_splitter.setChildrenCollapsible(False)

        # 主布局
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.addWidget(self.main_splitter)
        self.central_widget.setLayout(main_layout)

        # 左侧：标签页配置区
        self.create_left_panel()

        # 右侧：控制和日志区
        self.create_right_panel()

        # 设置分割器比例 (70% : 30%)
        self.main_splitter.setSizes([800, 400])

        # 创建模组配置（需要在日志控件创建后）
        self.create_module_config()
    
    def create_left_panel(self):
        """创建左侧配置面板"""
        # 创建左侧容器
        left_container = QWidget()
        left_layout = QVBoxLayout()
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(0)
        left_container.setLayout(left_layout)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setMinimumWidth(600)

        # 重新组织标签页结构，更加合理
        # 1. 基础配置标签页 - 包含URL和选择器
        self.basic_tab = QWidget()
        self.basic_layout = QVBoxLayout()
        self.basic_layout.setSpacing(10)
        self.basic_layout.setContentsMargins(10, 10, 10, 10)
        self.basic_tab.setLayout(self.basic_layout)
        self.tab_widget.addTab(self.basic_tab, "📝 基础配置")

        # 2. 高级设置标签页 - 包含爬取和输出设置
        self.advanced_tab = QWidget()
        self.advanced_layout = QVBoxLayout()
        self.advanced_layout.setSpacing(10)
        self.advanced_layout.setContentsMargins(10, 10, 10, 10)
        self.advanced_tab.setLayout(self.advanced_layout)
        self.tab_widget.addTab(self.advanced_tab, "⚙️ 高级设置")

        # 3. 字段配置标签页
        self.fields_tab = QWidget()
        self.fields_layout = QVBoxLayout()
        self.fields_layout.setSpacing(10)
        self.fields_layout.setContentsMargins(10, 10, 10, 10)
        self.fields_tab.setLayout(self.fields_layout)
        self.tab_widget.addTab(self.fields_tab, "📋 字段配置")

        # 4. 翻页设置标签页
        self.pagination_tab = QWidget()
        self.pagination_layout = QVBoxLayout()
        self.pagination_layout.setSpacing(10)
        self.pagination_layout.setContentsMargins(10, 10, 10, 10)
        self.pagination_tab.setLayout(self.pagination_layout)
        self.tab_widget.addTab(self.pagination_tab, "📄 翻页设置")

        # 5. 模组配置标签页
        self.module_tab = QWidget()
        self.module_layout = QVBoxLayout()
        self.module_layout.setSpacing(10)
        self.module_layout.setContentsMargins(10, 10, 10, 10)
        self.module_tab.setLayout(self.module_layout)
        self.tab_widget.addTab(self.module_tab, "🔧 模组配置")

        # 6. 更新模块标签页
        try:
            from gui.update_tab import UpdateTab
            self.update_tab = UpdateTab(self)
            self.tab_widget.addTab(self.update_tab, "🔄 更新模块")
        except ImportError as e:
            print(f"⚠️ 更新模块导入失败: {e}")
            # 创建一个简单的占位标签页
            self.update_tab = QWidget()
            update_layout = QVBoxLayout()
            update_layout.addWidget(QLabel("更新模块暂不可用"))
            self.update_tab.setLayout(update_layout)
            self.tab_widget.addTab(self.update_tab, "🔄 更新模块")

        # 为字段配置创建子标签页
        self.create_fields_sub_tabs()

        # 创建各个配置组
        self.create_basic_config()
        self.create_advanced_config()
        self.create_pagination_config()
        self.create_fields_config()

        # 初始化完成后，确保开关状态正确
        self.initialize_field_config_state()

        left_layout.addWidget(self.tab_widget)
        self.main_splitter.addWidget(left_container)

    def create_fields_sub_tabs(self):
        """创建字段配置的子标签页"""
        # 在字段配置标签页中创建子标签页
        self.fields_sub_tab_widget = QTabWidget()

        # 字段选择子标签页
        self.field_selection_tab = QWidget()
        self.field_selection_layout = QVBoxLayout()
        self.field_selection_layout.setSpacing(10)
        self.field_selection_layout.setContentsMargins(10, 10, 10, 10)
        self.field_selection_tab.setLayout(self.field_selection_layout)
        self.fields_sub_tab_widget.addTab(self.field_selection_tab, "字段选择")

        self.fields_layout.addWidget(self.fields_sub_tab_widget)
    
    def create_basic_config(self):
        """创建基础配置"""
        # 使用滚动区域以防内容过多
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        scroll_layout.setSpacing(10)
        scroll_layout.setContentsMargins(10, 10, 10, 10)

        # 配置管理组
        self.create_config_management_group(scroll_layout)

        # URL设置组 - 优化布局
        self.create_url_group_optimized(scroll_layout)

        # 选择器设置组 - 优化布局
        self.create_selector_group_optimized(scroll_layout)

        # 添加弹性空间
        scroll_layout.addStretch()

        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        self.basic_layout.addWidget(scroll_area)
    
    def create_config_management_group(self, parent_layout):
        """创建配置管理组"""
        group = QGroupBox("📁 配置管理")
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # 配置分类区域 - 使用网格布局更紧凑
        category_widget = QWidget()
        category_layout = QGridLayout()
        category_layout.setSpacing(10)
        category_layout.setContentsMargins(0, 0, 0, 0)

        # 第一行：分类选择
        category_layout.addWidget(QLabel("分类:"), 0, 0)

        self.parent_category_combo = QComboBox()
        self.parent_category_combo.setMinimumWidth(80)
        self.parent_category_combo.currentTextChanged.connect(self.on_parent_category_changed)
        category_layout.addWidget(self.parent_category_combo, 0, 1)

        self.sub_category_combo = QComboBox()
        self.sub_category_combo.setMinimumWidth(80)
        self.sub_category_combo.currentTextChanged.connect(self.on_sub_category_changed)
        category_layout.addWidget(self.sub_category_combo, 0, 2)

        self.child_category_combo = QComboBox()
        self.child_category_combo.setMinimumWidth(80)
        self.child_category_combo.currentTextChanged.connect(self.on_child_category_changed)
        category_layout.addWidget(self.child_category_combo, 0, 3)

        # 第二行：配置选择（4级结构中，第3级就是最终分类）
        category_layout.addWidget(QLabel("配置:"), 1, 0)
        self.config_combo = QComboBox()
        self.config_combo.setMinimumWidth(120)
        self.config_combo.currentTextChanged.connect(self.on_config_changed)
        category_layout.addWidget(self.config_combo, 1, 1, 1, 3)

        category_widget.setLayout(category_layout)
        main_layout.addWidget(category_widget)

        # 操作按钮区域 - 分两行显示
        buttons_widget = QWidget()
        buttons_layout = QGridLayout()
        buttons_layout.setSpacing(4)
        buttons_layout.setContentsMargins(0, 0, 0, 0)

        # 第一行按钮 - AI分析和基础操作
        self.ai_button = QPushButton("🤖 AI分析")
        self.ai_button.setObjectName("aiButton")
        self.ai_button.clicked.connect(self.start_ai_config)
        self.ai_button.setToolTip("使用AI智能分析页面结构")
        buttons_layout.addWidget(self.ai_button, 0, 0)

        self.save_config_btn = QPushButton("💾 保存配置")
        self.save_config_btn.clicked.connect(self.save_config)
        self.save_config_btn.setToolTip("保存当前配置到选中的配置组")
        buttons_layout.addWidget(self.save_config_btn, 0, 1)

        self.new_config_btn = QPushButton("🆕 新建配置")
        self.new_config_btn.clicked.connect(self.create_new_config)
        self.new_config_btn.setToolTip("在当前4级分类下创建新的配置组")
        buttons_layout.addWidget(self.new_config_btn, 0, 2)

        # 第二行按钮 - 高级操作
        self.edit_config_btn = QPushButton("✏️ 编辑配置")
        self.edit_config_btn.clicked.connect(self.edit_config_group)
        self.edit_config_btn.setToolTip("编辑配置组信息（名称、分类等）")
        buttons_layout.addWidget(self.edit_config_btn, 1, 0)

        self.delete_config_btn = QPushButton("🗑️ 删除配置")
        self.delete_config_btn.clicked.connect(self.delete_config)
        self.delete_config_btn.setToolTip("删除选中的配置组")
        buttons_layout.addWidget(self.delete_config_btn, 1, 1)

        self.config_manager_btn = QPushButton("⚙️ 高级管理")
        self.config_manager_btn.clicked.connect(self.open_config_manager)
        self.config_manager_btn.setToolTip("打开高级配置管理器")
        buttons_layout.addWidget(self.config_manager_btn, 1, 2)

        buttons_widget.setLayout(buttons_layout)
        main_layout.addWidget(buttons_widget)

        group.setLayout(main_layout)
        parent_layout.addWidget(group)
    
    def create_url_group_optimized(self, parent_layout):
        """创建紧凑的URL设置组"""
        group = QGroupBox("🌐 URL设置")
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # URL输入区域
        url_layout = QGridLayout()
        url_layout.setSpacing(10)

        url_layout.addWidget(QLabel("目标URL:"), 0, 0)
        self.input_url_edit = QLineEdit()
        self.input_url_edit.setPlaceholderText("https://example.com/news")
        url_layout.addWidget(self.input_url_edit, 0, 1, 1, 3)

        url_layout.addWidget(QLabel("基础URL:"), 1, 0)
        self.base_url_edit = QLineEdit()
        self.base_url_edit.setPlaceholderText("可选，用于相对链接补全")
        url_layout.addWidget(self.base_url_edit, 1, 1, 1, 3)

        main_layout.addLayout(url_layout)

        # 翻页控制区域
        page_control_group = QGroupBox("翻页控制")
        page_control_layout = QGridLayout()
        page_control_layout.setSpacing(10)

        # 第一行：页数范围
        page_control_layout.addWidget(QLabel("起始页:"), 0, 0)
        self.start_page_edit = QLineEdit()
        self.start_page_edit.setPlaceholderText("1")
        self.start_page_edit.setText("1")
        self.start_page_edit.setFixedWidth(60)
        page_control_layout.addWidget(self.start_page_edit, 0, 1)

        page_control_layout.addWidget(QLabel("最大页数:"), 0, 2)
        self.max_pages_edit = QLineEdit()
        self.max_pages_edit.setPlaceholderText("5")
        self.max_pages_edit.setFixedWidth(60)
        page_control_layout.addWidget(self.max_pages_edit, 0, 3)

        # 第二行：URL模式
        page_control_layout.addWidget(QLabel("URL模式:"), 1, 0)
        self.url_mode_combo = QComboBox()
        self.url_mode_combo.addItems(["绝对", "相对"])
        self.url_mode_combo.setCurrentText("绝对")
        self.url_mode_combo.setFixedWidth(80)
        page_control_layout.addWidget(self.url_mode_combo, 1, 1)

        # 第三行：翻页格式
        page_control_layout.addWidget(QLabel("翻页格式:"), 2, 0)
        self.page_suffix_edit = QLineEdit()
        self.page_suffix_edit.setPlaceholderText("index_{n}.html")
        self.page_suffix_edit.setText("index_{n}.html")
        page_control_layout.addWidget(self.page_suffix_edit, 2, 1, 1, 3)

        page_control_group.setLayout(page_control_layout)
        main_layout.addWidget(page_control_group)

        group.setLayout(main_layout)
        parent_layout.addWidget(group)
    
    def create_selector_group_optimized(self, parent_layout):
        """创建舒适的选择器设置组"""
        group = QGroupBox("🎯 内容选择器")
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # 列表页选择器组
        list_selectors_group = QGroupBox("列表页选择器")
        list_layout = QGridLayout()
        list_layout.setSpacing(10)

        list_layout.addWidget(QLabel("列表容器:"), 0, 0)
        self.list_container_edit = QLineEdit()
        self.list_container_edit.setPlaceholderText(".main")
        self.list_container_edit.setText(".main")
        self.list_container_edit.setToolTip("包含文章列表的容器选择器")
        list_layout.addWidget(self.list_container_edit, 0, 1)

        list_layout.addWidget(QLabel("文章链接:"), 1, 0)
        self.article_item_edit = QLineEdit()
        self.article_item_edit.setPlaceholderText(".clearfix li a")
        self.article_item_edit.setText(".clearfix.ty_list li a")
        self.article_item_edit.setToolTip("文章链接的选择器")
        list_layout.addWidget(self.article_item_edit, 1, 1)

        list_selectors_group.setLayout(list_layout)
        main_layout.addWidget(list_selectors_group)

        # 文章页选择器组
        article_selectors_group = QGroupBox("文章页选择器")
        article_layout = QGridLayout()
        article_layout.setSpacing(10)

        article_layout.addWidget(QLabel("标题:"), 0, 0)
        self.title_selectors_edit = QLineEdit()
        self.title_selectors_edit.setPlaceholderText("h1, .title")
        self.title_selectors_edit.setToolTip("文章标题选择器，多个用逗号分隔")
        article_layout.addWidget(self.title_selectors_edit, 0, 1)

        article_layout.addWidget(QLabel("内容:"), 1, 0)
        self.content_selectors_edit = QLineEdit()
        self.content_selectors_edit.setPlaceholderText(".article_cont")
        self.content_selectors_edit.setToolTip("文章内容选择器，多个用逗号分隔")
        article_layout.addWidget(self.content_selectors_edit, 1, 1)

        article_layout.addWidget(QLabel("日期:"), 2, 0)
        self.date_selectors_edit = QLineEdit()
        self.date_selectors_edit.setPlaceholderText(".date")
        self.date_selectors_edit.setToolTip("发布日期选择器，多个用逗号分隔")
        article_layout.addWidget(self.date_selectors_edit, 2, 1)

        article_layout.addWidget(QLabel("来源:"), 3, 0)
        self.source_selectors_edit = QLineEdit()
        self.source_selectors_edit.setPlaceholderText(".source")
        self.source_selectors_edit.setToolTip("文章来源选择器，多个用逗号分隔")
        article_layout.addWidget(self.source_selectors_edit, 3, 1)

        article_selectors_group.setLayout(article_layout)
        main_layout.addWidget(article_selectors_group)

        # 操作按钮
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        test_selectors_btn = QPushButton("🧪 测试选择器")
        test_selectors_btn.setToolTip("测试当前配置的选择器是否能正确提取内容")
        test_selectors_btn.clicked.connect(self.test_basic_selectors)
        buttons_layout.addWidget(test_selectors_btn)

        custom_selector_btn = QPushButton("⚙️ 高级选择器")
        custom_selector_btn.setToolTip("打开高级选择器配置对话框")
        custom_selector_btn.clicked.connect(self.open_custom_selector_dialog)
        buttons_layout.addWidget(custom_selector_btn)

        buttons_layout.addStretch()
        main_layout.addLayout(buttons_layout)

        group.setLayout(main_layout)
        parent_layout.addWidget(group)

    def create_advanced_config(self):
        """创建高级配置"""
        # 使用滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        scroll_layout.setSpacing(10)
        scroll_layout.setContentsMargins(10, 10, 10, 10)

        # 爬取设置组
        self.create_crawl_settings_group(scroll_layout)

        # 输出设置组
        self.create_output_settings_group(scroll_layout)

        # 性能设置组
        self.create_performance_settings_group(scroll_layout)

        # LLM AI配置组
        self.create_llm_config_group(scroll_layout)

        # 添加弹性空间
        scroll_layout.addStretch()

        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        self.advanced_layout.addWidget(scroll_area)

    def create_crawl_settings_group(self, parent_layout):
        """创建爬取设置组"""
        group = QGroupBox("🕷️ 爬取设置")
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # 基础设置
        basic_settings_layout = QGridLayout()
        basic_settings_layout.setSpacing(10)

        # 第一行：爬取模式和采集链接
        basic_settings_layout.addWidget(QLabel("爬取模式:"), 0, 0)
        self.crawl_mode_combo = QComboBox()
        self.crawl_mode_combo.addItems(["balance", "fast", "safe"])
        self.crawl_mode_combo.setCurrentText("balance")
        self.crawl_mode_combo.setToolTip("balance: 平衡模式，fast: 快速模式，safe: 安全模式")
        basic_settings_layout.addWidget(self.crawl_mode_combo, 0, 1)

        self.collect_links_checkbox = QCheckBox("采集图片与附件链接")
        self.collect_links_checkbox.setChecked(True)
        self.collect_links_checkbox.setToolTip("注意：当启用模组配置时，此设置可能被模组配置覆盖")
        basic_settings_layout.addWidget(self.collect_links_checkbox, 0, 2, 1, 2)

        # 第二行：运行模式选项
        self.headless_checkbox = QCheckBox("无头模式（后台运行）")
        self.headless_checkbox.setChecked(True)
        self.headless_checkbox.setToolTip("启用后浏览器将在后台运行，不显示界面")
        basic_settings_layout.addWidget(self.headless_checkbox, 1, 0, 1, 2)

        self.skip_pagination_checkbox = QCheckBox("跳过翻页（使用URL缓存）")
        self.skip_pagination_checkbox.setChecked(False)
        self.skip_pagination_checkbox.setToolTip("如果存在URL缓存文件，跳过翻页收集步骤直接使用缓存")
        basic_settings_layout.addWidget(self.skip_pagination_checkbox, 1, 2, 1, 2)

        # 第三行：浏览器兼容性选项
        self.disable_js_injection_checkbox = QCheckBox("禁用JS注入（兼容模式）")
        self.disable_js_injection_checkbox.setChecked(False)
        self.disable_js_injection_checkbox.setToolTip("禁用反检测JS注入，避免影响某些网站的JS加载，适用于JS加载失败的网站")
        basic_settings_layout.addWidget(self.disable_js_injection_checkbox, 2, 0, 1, 4)

        layout.addLayout(basic_settings_layout)

        # 高级设置
        advanced_settings_layout = QHBoxLayout()
        advanced_settings_layout.setSpacing(10)

        # 内容过滤按钮
        filter_button = QPushButton("🔍 设置内容过滤规则")
        filter_button.clicked.connect(self.set_filter_rules)
        filter_button.setToolTip("设置文章内容过滤规则")
        advanced_settings_layout.addWidget(filter_button)



        layout.addLayout(advanced_settings_layout)

        group.setLayout(layout)
        parent_layout.addWidget(group)

    def create_output_settings_group(self, parent_layout):
        """创建输出设置组"""
        group = QGroupBox("💾 输出设置")
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # 文件设置
        file_settings_layout = QGridLayout()
        file_settings_layout.setSpacing(10)

        # 第一行：导出文件路径
        file_settings_layout.addWidget(QLabel("导出文件:"), 0, 0)

        file_path_layout = QHBoxLayout()
        file_path_layout.setSpacing(10)

        self.export_filename_edit = QLineEdit()
        self.export_filename_edit.setPlaceholderText("可选，例如: 文章数据 或选择完整路径")
        file_path_layout.addWidget(self.export_filename_edit)

        self.export_save_as_btn = QPushButton("📁 浏览")
        self.export_save_as_btn.clicked.connect(self.browse_export_file)
        self.export_save_as_btn.setToolTip("选择保存文件路径")
        file_path_layout.addWidget(self.export_save_as_btn)

        file_path_widget = QWidget()
        file_path_widget.setLayout(file_path_layout)
        file_settings_layout.addWidget(file_path_widget, 0, 1, 1, 3)

        # 第二行：文件格式和Excel模式
        file_settings_layout.addWidget(QLabel("文件格式:"), 1, 0)
        self.file_format_combo = QComboBox()
        self.file_format_combo.addItems(["CSV", "Excel"])
        self.file_format_combo.currentTextChanged.connect(self.update_filename_placeholder)
        self.file_format_combo.currentTextChanged.connect(self.on_file_format_changed)
        file_settings_layout.addWidget(self.file_format_combo, 1, 1)

        file_settings_layout.addWidget(QLabel("Excel模式:"), 1, 2)
        self.excel_mode_combo = QComboBox()
        self.excel_mode_combo.addItems([
            "智能批量 (推荐)",
            "直接写入 (兼容)",
            "混合策略 (CSV转Excel)",
            "批量写入 (手动)"
        ])
        self.excel_mode_combo.setToolTip(
            "智能批量: 自动收集数据批量写入，性能最佳\n"
            "直接写入: 传统模式，每次直接写入文件\n"
            "混合策略: 先写CSV后转Excel，平衡速度和格式\n"
            "批量写入: 需要手动调用批量写入功能"
        )
        file_settings_layout.addWidget(self.excel_mode_combo, 1, 3)

        # 保存Excel模式标签的引用
        self.excel_mode_label = file_settings_layout.itemAtPosition(1, 2).widget()

        # 初始状态下隐藏Excel模式选择
        self.excel_mode_label.hide()
        self.excel_mode_combo.hide()

        layout.addLayout(file_settings_layout)

        # 其他设置
        other_settings_layout = QHBoxLayout()
        other_settings_layout.setSpacing(10)

        other_settings_layout.addWidget(QLabel("分类ID:"))
        self.classid_edit = QLineEdit()
        self.classid_edit.setPlaceholderText("可选，分类标识")
        self.classid_edit.setToolTip("用于标识文章分类的ID")
        other_settings_layout.addWidget(self.classid_edit)

        layout.addLayout(other_settings_layout)

        group.setLayout(layout)
        parent_layout.addWidget(group)

    def on_file_format_changed(self, format_text):
        """处理文件格式变化"""
        if format_text == "Excel":
            # 显示Excel模式选择
            self.excel_mode_label.show()
            self.excel_mode_combo.show()
        else:
            # 隐藏Excel模式选择
            self.excel_mode_label.hide()
            self.excel_mode_combo.hide()

    def get_excel_write_strategy(self):
        """获取Excel写入策略"""
        if self.file_format_combo.currentText() != "Excel":
            return "auto"  # 非Excel格式使用默认策略

        mode_text = self.excel_mode_combo.currentText()
        if "智能批量" in mode_text:
            return "smart"
        elif "直接写入" in mode_text:
            return "direct"
        elif "混合策略" in mode_text:
            return "hybrid"
        elif "批量写入" in mode_text:
            return "batch"
        else:
            return "smart"  # 默认使用智能批量

    def create_performance_settings_group(self, parent_layout):
        """创建性能设置组"""
        group = QGroupBox("⚡ 性能设置")
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # 性能参数设置
        perf_layout = QGridLayout()
        perf_layout.setSpacing(10)

        # 第一行：并发和重试
        perf_layout.addWidget(QLabel("并发线程数:"), 0, 0)
        self.thread_count_spin = QSpinBox()
        self.thread_count_spin.setRange(1, 20)
        self.thread_count_spin.setValue(5)
        self.thread_count_spin.setToolTip("同时处理的文章数量，建议1-10")
        self.thread_count_spin.setFixedWidth(80)
        perf_layout.addWidget(self.thread_count_spin, 0, 1)

        perf_layout.addWidget(QLabel("重试次数:"), 0, 2)
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(0, 10)
        self.retry_spin.setValue(2)
        self.retry_spin.setToolTip("失败时的重试次数")
        self.retry_spin.setFixedWidth(80)
        perf_layout.addWidget(self.retry_spin, 0, 3)

        # 第二行：间隔设置
        perf_layout.addWidget(QLabel("下载间隔(秒):"), 1, 0)
        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(0, 10)
        self.interval_spin.setSingleStep(0.1)
        self.interval_spin.setValue(0)
        self.interval_spin.setToolTip("请求之间的等待时间，避免过于频繁")
        self.interval_spin.setFixedWidth(80)
        perf_layout.addWidget(self.interval_spin, 1, 1)

        # 添加说明文字
        perf_layout.addWidget(QLabel("提示: 适当降低并发数和增加间隔可提高成功率"), 1, 2, 1, 2)

        layout.addLayout(perf_layout)

        group.setLayout(layout)
        parent_layout.addWidget(group)

    def create_llm_config_group(self, parent_layout):
        """创建LLM AI配置组"""
        group = QGroupBox("🤖 AI智能分析配置")
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # AI状态和配置
        status_layout = QHBoxLayout()
        status_layout.setSpacing(10)

        status_layout.addWidget(QLabel("AI状态:"))
        self.ai_status_label = QLabel("未配置")
        self.ai_status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        status_layout.addWidget(self.ai_status_label)

        self.llm_config_button = QPushButton("⚙️ 配置LLM API")
        self.llm_config_button.clicked.connect(self.open_llm_config)
        self.llm_config_button.setToolTip("配置AI模型的API密钥和参数")
        status_layout.addWidget(self.llm_config_button)

        status_layout.addStretch()
        layout.addLayout(status_layout)

        # AI分析操作
        analysis_layout = QHBoxLayout()
        analysis_layout.setSpacing(10)

        self.ai_analyze_button = QPushButton("🔍 AI智能分析")
        self.ai_analyze_button.clicked.connect(self.start_ai_analysis)
        self.ai_analyze_button.setEnabled(False)
        self.ai_analyze_button.setToolTip("使用AI分析页面结构并生成选择器")
        analysis_layout.addWidget(self.ai_analyze_button)

        # 分析进度显示
        self.ai_progress_label = QLabel("")
        self.ai_progress_label.setStyleSheet("color: #666; font-size: 12px;")
        analysis_layout.addWidget(self.ai_progress_label)

        analysis_layout.addStretch()
        layout.addLayout(analysis_layout)

        # 模组检查状态
        self.module_check_label = QLabel("")
        self.module_check_label.setStyleSheet("color: #666; font-size: 12px; padding: 4px;")
        self.module_check_label.setWordWrap(True)
        layout.addWidget(self.module_check_label)

        group.setLayout(layout)
        parent_layout.addWidget(group)

        # 初始化AI状态
        self.update_ai_status()

    def create_pagination_config(self):
        """创建动态翻页配置"""
        # 翻页类型选择组
        self.create_pagination_type_group()

        # 点击翻页设置组
        self.create_click_pagination_group()

        # 滚动翻页设置组
        self.create_scroll_pagination_group()

        # 手动翻页设置组
        self.create_manual_pagination_group()

        # 测试按钮
        test_button = QPushButton("测试翻页设置")
        test_button.clicked.connect(self.test_pagination_settings)
        self.pagination_layout.addWidget(test_button)

        # 添加弹性空间
        self.pagination_layout.addStretch()

    def create_fields_config(self):
        """创建字段配置"""
        if not FIELD_CONFIG_AVAILABLE:
            # 如果字段配置不可用，显示提示信息
            info_label = QLabel("字段配置功能不可用，请检查相关模块是否正确安装。")
            info_label.setStyleSheet("color: orange; font-weight: bold; padding: 10px;")
            self.fields_layout.addWidget(info_label)
            return

        # 字段预设选择组
        self.create_field_preset_group()

        # 自定义字段选择组
        self.create_custom_fields_group()

        # 字段预览组
        self.create_field_preview_group()

        # 操作按钮组
        self.create_field_actions_group()

        # 添加弹性空间
        self.fields_layout.addStretch()

    def create_field_preset_group(self):
        """创建字段预设选择组"""
        group = QGroupBox("字段配置")
        layout = QVBoxLayout()

        # 字段配置开关
        switch_layout = QHBoxLayout()
        self.enable_field_config_checkbox = QCheckBox("启用字段配置")
        self.enable_field_config_checkbox.setToolTip("启用后可以自定义字段配置，否则使用默认字段")
        self.enable_field_config_checkbox.setChecked(False)  # 默认关闭
        self.enable_field_config_checkbox.stateChanged.connect(self.on_field_config_enabled_changed)
        switch_layout.addWidget(self.enable_field_config_checkbox)

        # 状态指示器
        self.field_config_status_label = QLabel("🔒 使用默认字段")
        self.field_config_status_label.setStyleSheet("color: #666; font-size: 12px;")
        switch_layout.addWidget(self.field_config_status_label)
        switch_layout.addStretch()

        layout.addLayout(switch_layout)

        # 字段配置区域（初始禁用）
        self.field_config_area = QWidget()
        field_config_layout = QVBoxLayout()

        # 预设选择
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("选择预设:"))

        self.field_preset_combo = QComboBox()
        self.field_preset_combo.setToolTip("选择预定义的字段组合")

        # 加载预设选项
        try:
            presets = get_field_presets()
            preset_descriptions = {
                "basic": "基础字段 (8个) - 标题、内容、链接等基本信息",
                "social_media": "社交媒体 (10个) - 包含点赞数、阅读量、评论数等",
                "ecommerce": "电商字段 (8个) - 包含价格、成交量、评分等",
                "news": "新闻字段 (9个) - 包含分类、标签、阅读量等",
                "blog": "博客字段 (8个) - 包含字数、阅读时长等",
                "comprehensive": "综合字段 (17个) - 包含所有常用字段"
            }

            for preset_name in presets.keys():
                description = preset_descriptions.get(preset_name, f"{preset_name} 预设")
                self.field_preset_combo.addItem(description, preset_name)

        except Exception as e:
            self.field_preset_combo.addItem("加载预设失败", "basic")

        self.field_preset_combo.currentTextChanged.connect(self.on_preset_changed)
        preset_layout.addWidget(self.field_preset_combo)

        # 应用预设按钮
        apply_preset_btn = QPushButton("应用预设")
        apply_preset_btn.setToolTip("应用选中的字段预设")
        apply_preset_btn.clicked.connect(self.apply_field_preset)
        preset_layout.addWidget(apply_preset_btn)

        field_config_layout.addLayout(preset_layout)

        # 预设说明
        self.preset_description = QLabel("选择一个预设查看详细说明")
        self.preset_description.setWordWrap(True)
        self.preset_description.setStyleSheet("color: #666; font-size: 12px; padding: 5px;")
        field_config_layout.addWidget(self.preset_description)

        self.field_config_area.setLayout(field_config_layout)
        self.field_config_area.setEnabled(False)  # 初始禁用
        layout.addWidget(self.field_config_area)

        group.setLayout(layout)
        self.field_selection_layout.addWidget(group)

    def create_custom_fields_group(self):
        """创建自定义字段选择组"""
        self.custom_fields_group = QGroupBox("自定义字段选择")
        layout = QVBoxLayout()

        # 说明文字
        info_label = QLabel("勾选需要的字段，或直接使用上方的预设配置：")
        info_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(info_label)

        # 字段复选框容器 - 使用滚动区域
        scroll_area = QScrollArea()
        scroll_area.setMaximumHeight(300)  # 限制高度
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self.fields_scroll_area = QWidget()
        self.fields_scroll_layout = QGridLayout()
        self.fields_scroll_layout.setSpacing(5)  # 减少间距
        self.fields_scroll_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        self.fields_scroll_area.setLayout(self.fields_scroll_layout)

        scroll_area.setWidget(self.fields_scroll_area)

        # 字段复选框字典
        self.field_checkboxes = {}

        # 加载可用字段
        self.load_available_fields()

        layout.addWidget(scroll_area)

        # 操作按钮
        button_layout = QHBoxLayout()

        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all_fields)
        button_layout.addWidget(select_all_btn)

        clear_all_btn = QPushButton("清空")
        clear_all_btn.clicked.connect(self.clear_all_fields)
        button_layout.addWidget(clear_all_btn)

        apply_custom_btn = QPushButton("应用自定义字段")
        apply_custom_btn.setToolTip("应用当前选中的字段")
        apply_custom_btn.clicked.connect(self.apply_custom_fields)
        button_layout.addWidget(apply_custom_btn)

        layout.addLayout(button_layout)

        self.custom_fields_group.setLayout(layout)
        self.custom_fields_group.setEnabled(False)  # 初始禁用
        self.field_selection_layout.addWidget(self.custom_fields_group)

    def create_field_preview_group(self):
        """创建字段预览组"""
        self.field_preview_group = QGroupBox("当前字段配置")
        layout = QVBoxLayout()

        # 当前字段列表 - 使用更紧凑的显示
        self.current_fields_text = QTextEdit()
        self.current_fields_text.setMaximumHeight(80)  # 减少高度
        self.current_fields_text.setReadOnly(True)
        self.current_fields_text.setPlaceholderText("当前没有配置字段")
        self.current_fields_text.setStyleSheet("""
            QTextEdit {
                font-size: 11px;
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: #f8f9fa;
            }
        """)
        layout.addWidget(self.current_fields_text)

        # 字段数量统计
        self.field_count_label = QLabel("字段数量: 0")
        self.field_count_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(self.field_count_label)

        self.field_preview_group.setLayout(layout)
        self.field_preview_group.setEnabled(False)  # 初始禁用
        self.field_selection_layout.addWidget(self.field_preview_group)

    def create_field_actions_group(self):
        """创建字段操作按钮组"""
        self.field_actions_group = QGroupBox("操作")
        layout = QVBoxLayout()

        # 第一行按钮
        button_row1 = QHBoxLayout()

        # 刷新字段列表
        refresh_btn = QPushButton("刷新字段列表")
        refresh_btn.setToolTip("重新加载可用字段")
        refresh_btn.clicked.connect(self.refresh_field_list)
        button_row1.addWidget(refresh_btn)

        # 重置为默认
        reset_btn = QPushButton("重置为默认")
        reset_btn.setToolTip("重置为基础字段配置")
        reset_btn.clicked.connect(self.reset_to_default_fields)
        button_row1.addWidget(reset_btn)

        # 自定义选择器
        custom_selector_btn = QPushButton("自定义选择器")
        custom_selector_btn.setToolTip("为字段自定义CSS选择器")
        custom_selector_btn.clicked.connect(self.open_custom_selector_dialog)
        button_row1.addWidget(custom_selector_btn)

        # 自定义字段
        custom_field_btn = QPushButton("自定义字段")
        custom_field_btn.setToolTip("添加自定义字段名和值")
        custom_field_btn.clicked.connect(self.open_custom_field_dialog)
        button_row1.addWidget(custom_field_btn)

        layout.addLayout(button_row1)

        # 第二行按钮
        button_row2 = QHBoxLayout()

        # 导出配置
        export_btn = QPushButton("导出配置")
        export_btn.setToolTip("导出当前字段配置")
        export_btn.clicked.connect(self.export_field_config)
        button_row2.addWidget(export_btn)

        # 导入配置
        import_btn = QPushButton("导入配置")
        import_btn.setToolTip("从文件导入字段配置")
        import_btn.clicked.connect(self.import_field_config)
        button_row2.addWidget(import_btn)

        # 测试选择器
        test_selector_btn = QPushButton("测试选择器")
        test_selector_btn.setToolTip("测试选中字段的选择器是否有效")
        test_selector_btn.clicked.connect(self.test_selected_field_selectors)
        button_row2.addWidget(test_selector_btn)

        layout.addLayout(button_row2)

        self.field_actions_group.setLayout(layout)
        self.field_actions_group.setEnabled(False)  # 初始禁用
        self.field_selection_layout.addWidget(self.field_actions_group)

        # 添加弹性空间
        self.field_selection_layout.addStretch()





    def load_available_fields(self):
        """加载可用字段"""
        if not FIELD_CONFIG_AVAILABLE:
            return

        try:
            # 获取字段配置管理器
            manager = get_field_config_manager()
            all_fields = manager.get_available_fields()

            # 清空现有复选框
            for checkbox in self.field_checkboxes.values():
                checkbox.setParent(None)
            self.field_checkboxes.clear()

            # 分类显示字段
            default_fields = manager.config_data.get("default_fields", {})
            extended_fields = manager.config_data.get("extended_fields", {})

            row = 0
            col = 0
            max_cols = 4  # 增加到4列，更紧凑

            # 添加基础字段标题
            if default_fields:
                title_label = QLabel("📁 基础字段")
                title_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px; margin: 5px 0;")
                self.fields_scroll_layout.addWidget(title_label, row, 0, 1, max_cols)
                row += 1

                # 添加基础字段复选框
                for field_name, field_config in default_fields.items():
                    # 简化显示文本，只显示中文名
                    display_name = field_config.get('name', field_name)
                    checkbox = QCheckBox(display_name)

                    # 详细信息放在工具提示中
                    description = field_config.get('description', '')
                    tooltip = f"字段: {field_name}\n名称: {display_name}"
                    if description:
                        tooltip += f"\n说明: {description}"
                    checkbox.setToolTip(tooltip)

                    checkbox.stateChanged.connect(self.on_field_selection_changed)
                    checkbox.setStyleSheet("QCheckBox { font-size: 12px; padding: 2px; }")

                    self.field_checkboxes[field_name] = checkbox
                    self.fields_scroll_layout.addWidget(checkbox, row, col)

                    col += 1
                    if col >= max_cols:
                        col = 0
                        row += 1

                if col > 0:
                    row += 1
                col = 0

            # 添加扩展字段标题
            if extended_fields:
                title_label = QLabel("🔧 扩展字段")
                title_label.setStyleSheet("font-weight: bold; color: #27ae60; font-size: 13px; margin: 5px 0;")
                self.fields_scroll_layout.addWidget(title_label, row, 0, 1, max_cols)
                row += 1

                # 添加扩展字段复选框
                for field_name, field_config in extended_fields.items():
                    # 简化显示文本，只显示中文名
                    display_name = field_config.get('name', field_name)
                    checkbox = QCheckBox(display_name)

                    # 详细信息放在工具提示中
                    description = field_config.get('description', '')
                    tooltip = f"字段: {field_name}\n名称: {display_name}"
                    if description:
                        tooltip += f"\n说明: {description}"
                    checkbox.setToolTip(tooltip)

                    checkbox.stateChanged.connect(self.on_field_selection_changed)
                    checkbox.setStyleSheet("QCheckBox { font-size: 12px; padding: 2px; }")

                    self.field_checkboxes[field_name] = checkbox
                    self.fields_scroll_layout.addWidget(checkbox, row, col)

                    col += 1
                    if col >= max_cols:
                        col = 0
                        row += 1

            # 初始化为基础字段选中状态
            self.select_basic_fields()

        except Exception as e:
            self.log_message(f"加载字段列表失败: {e}")

    def create_pagination_type_group(self):
        """创建翻页类型选择组"""
        group = QGroupBox("翻页类型设置")
        layout = QGridLayout()

        # 启用动态翻页
        self.enable_pagination_checkbox = QCheckBox("启用动态翻页")
        self.enable_pagination_checkbox.stateChanged.connect(self.on_pagination_enabled_changed)
        layout.addWidget(self.enable_pagination_checkbox, 0, 0, 1, 2)

        # 翻页类型
        layout.addWidget(QLabel("翻页类型:"), 1, 0)
        self.pagination_type_combo = QComboBox()
        pagination_options = PaginationConfigHelper.get_pagination_type_options()
        pagination_options.append("手动翻页")  # 添加手动翻页选项
        self.pagination_type_combo.addItems(pagination_options)
        self.pagination_type_combo.currentTextChanged.connect(self.on_pagination_type_changed)
        layout.addWidget(self.pagination_type_combo, 1, 1)

        group.setLayout(layout)
        self.pagination_layout.addWidget(group)

    def create_click_pagination_group(self):
        """创建点击翻页设置组"""
        self.click_pagination_group = QGroupBox("点击翻页设置")
        layout = QGridLayout()

        # 下一页按钮选择器
        layout.addWidget(QLabel("下一页按钮选择器:"), 0, 0)
        self.next_button_selector_edit = QLineEdit()
        self.next_button_selector_edit.setText("a.next:not(.lose)")
        self.next_button_selector_edit.setPlaceholderText("例如: a.next:not(.lose)")
        layout.addWidget(self.next_button_selector_edit, 0, 1)

        # 内容加载完成选择器
        layout.addWidget(QLabel("内容加载完成选择器:"), 1, 0)
        self.content_ready_selector_edit = QLineEdit()
        self.content_ready_selector_edit.setPlaceholderText("可选，例如: .article-list-loaded")
        layout.addWidget(self.content_ready_selector_edit, 1, 1)

        # 等待时间和超时时间合并为一行
        layout.addWidget(QLabel("点击后等待(ms):"), 2, 0)
        self.wait_after_click_spin = QSpinBox()
        self.wait_after_click_spin.setRange(500, 10000)
        self.wait_after_click_spin.setValue(2000)
        self.wait_after_click_spin.setMaximumWidth(100)
        layout.addWidget(self.wait_after_click_spin, 2, 1)

        layout.addWidget(QLabel("超时时间(ms):"), 2, 2)
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5000, 60000)
        self.timeout_spin.setValue(10000)
        self.timeout_spin.setMaximumWidth(100)
        layout.addWidget(self.timeout_spin, 2, 3)

        # 禁用检查
        self.disabled_check_checkbox = QCheckBox("检查按钮禁用状态")
        self.disabled_check_checkbox.setChecked(True)
        layout.addWidget(self.disabled_check_checkbox, 3, 0, 1, 2)

        # 新增：重复item提前结束开关
        self.duplicate_last_item_checkbox = QCheckBox("遇到重复最后一项网址提前结束翻页")
        self.duplicate_last_item_checkbox.setChecked(True)
        layout.addWidget(self.duplicate_last_item_checkbox, 3, 2, 1, 2)

        self.click_pagination_group.setLayout(layout)
        self.pagination_layout.addWidget(self.click_pagination_group)

    def create_scroll_pagination_group(self):
        """创建滚动翻页设置组"""
        self.scroll_pagination_group = QGroupBox("滚动翻页设置")
        layout = QGridLayout()

        # 滚动容器选择器
        layout.addWidget(QLabel("滚动容器选择器:"), 0, 0)
        self.scroll_container_selector_edit = QLineEdit()
        self.scroll_container_selector_edit.setText("body")
        self.scroll_container_selector_edit.setPlaceholderText("例如: body")
        layout.addWidget(self.scroll_container_selector_edit, 0, 1)

        # 滚动步长和滚动延迟合并为一行
        layout.addWidget(QLabel("滚动步长(px):"), 1, 0)
        self.scroll_step_spin = QSpinBox()
        self.scroll_step_spin.setRange(100, 2000)
        self.scroll_step_spin.setValue(800)
        self.scroll_step_spin.setMaximumWidth(100)
        layout.addWidget(self.scroll_step_spin, 1, 1)

        layout.addWidget(QLabel("滚动延迟(ms):"), 1, 2)
        self.scroll_delay_spin = QSpinBox()
        self.scroll_delay_spin.setRange(500, 10000)
        self.scroll_delay_spin.setValue(2000)
        self.scroll_delay_spin.setMaximumWidth(100)
        layout.addWidget(self.scroll_delay_spin, 1, 3)

        # 加载指示器选择器
        layout.addWidget(QLabel("加载指示器选择器:"), 2, 0)
        self.load_indicator_selector_edit = QLineEdit()
        self.load_indicator_selector_edit.setPlaceholderText("可选，例如: .loading")
        layout.addWidget(self.load_indicator_selector_edit, 2, 1, 1, 3)

        self.scroll_pagination_group.setLayout(layout)
        self.pagination_layout.addWidget(self.scroll_pagination_group)

    def create_manual_pagination_group(self):
        """创建手动翻页设置组"""
        self.manual_pagination_group = QGroupBox("手动翻页设置")
        layout = QGridLayout()

        # Excel文件路径
        layout.addWidget(QLabel("Excel文件:"), 0, 0)
        self.excel_file_edit = QLineEdit()
        self.excel_file_edit.setPlaceholderText("选择包含URL列表的Excel文件")
        self.excel_file_edit.setText("manual_pagination/url_templates.xlsx")
        layout.addWidget(self.excel_file_edit, 0, 1, 1, 2)

        # 浏览按钮
        browse_button = QPushButton("浏览")
        browse_button.clicked.connect(self.browse_excel_file)
        layout.addWidget(browse_button, 0, 3)

        # 创建模板按钮
        create_template_button = QPushButton("创建Excel模板")
        create_template_button.clicked.connect(self.create_excel_template)
        layout.addWidget(create_template_button, 1, 0)

        # 编辑Excel按钮
        edit_excel_button = QPushButton("编辑Excel文件")
        edit_excel_button.clicked.connect(self.edit_excel_file)
        layout.addWidget(edit_excel_button, 1, 1)

        # 页面间等待时间
        layout.addWidget(QLabel("页面间等待(ms):"), 2, 0)
        self.manual_wait_spin = QSpinBox()
        self.manual_wait_spin.setRange(500, 10000)
        self.manual_wait_spin.setValue(2000)
        self.manual_wait_spin.setSuffix(" ms")
        layout.addWidget(self.manual_wait_spin, 2, 1)

        # 保存进度选项
        self.save_progress_checkbox = QCheckBox("保存处理进度到Excel")
        self.save_progress_checkbox.setChecked(True)
        self.save_progress_checkbox.setToolTip("将处理状态和结果保存回Excel文件")
        layout.addWidget(self.save_progress_checkbox, 2, 2, 1, 2)

        self.manual_pagination_group.setLayout(layout)
        self.pagination_layout.addWidget(self.manual_pagination_group)

    def create_right_panel(self):
        """创建右侧控制和日志面板"""
        right_widget = QWidget()
        right_widget.setMinimumWidth(350)
        right_layout = QVBoxLayout()
        right_layout.setSpacing(10)
        right_layout.setContentsMargins(10, 10, 10, 10)
        right_widget.setLayout(right_layout)

        # 控制面板组
        control_group = QGroupBox("🎮 控制面板")
        control_layout = QVBoxLayout()
        control_layout.setSpacing(10)
        control_layout.setContentsMargins(10, 10, 10, 10)

        # 控制按钮组
        self.create_control_buttons(control_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setTextVisible(True)
        control_layout.addWidget(self.progress_bar)

        # 状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px; padding: 4px;")
        control_layout.addWidget(self.status_label)

        control_group.setLayout(control_layout)
        right_layout.addWidget(control_group)

        # 日志显示区
        self.create_log_area(right_layout)

        self.main_splitter.addWidget(right_widget)

    def create_control_buttons(self, layout):
        """创建控制按钮"""
        # 主要控制按钮
        main_buttons_layout = QHBoxLayout()
        main_buttons_layout.setSpacing(10)

        # 开始爬取按钮
        self.start_button = QPushButton("🚀 开始爬取")
        self.start_button.setObjectName("startButton")
        self.start_button.clicked.connect(self.start_crawling)
        self.start_button.setMinimumHeight(40)
        main_buttons_layout.addWidget(self.start_button)

        # 停止爬取按钮
        self.stop_button = QPushButton("⏹️ 停止爬取")
        self.stop_button.setObjectName("stopButton")
        self.stop_button.clicked.connect(self.stop_crawling)
        self.stop_button.setEnabled(False)
        self.stop_button.setMinimumHeight(40)
        main_buttons_layout.addWidget(self.stop_button)

        layout.addLayout(main_buttons_layout)

        # 辅助按钮
        aux_buttons_layout = QHBoxLayout()
        aux_buttons_layout.setSpacing(10)

        # 清空日志按钮
        clear_button = QPushButton("🗑️ 清空日志")
        clear_button.clicked.connect(self.clear_log)
        clear_button.setToolTip("清空运行日志")
        aux_buttons_layout.addWidget(clear_button)

        # 导出日志按钮
        export_log_button = QPushButton("📄 导出日志")
        export_log_button.clicked.connect(self.export_log)
        export_log_button.setToolTip("导出运行日志到文件")
        aux_buttons_layout.addWidget(export_log_button)

        layout.addLayout(aux_buttons_layout)

    def export_log(self):
        """导出日志到文件"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import datetime

            # 获取当前时间作为默认文件名
            current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"crawler_log_{current_time}.txt"

            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出日志", default_filename, "文本文件 (*.txt);;所有文件 (*)"
            )

            if file_path:
                # 获取日志内容
                log_content = self.log_text.toPlainText()

                # 写入文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"爬虫运行日志\n")
                    f.write(f"导出时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(log_content)

                self.log_message(f"✅ 日志已导出到: {file_path}")
                show_info_message(self, "成功", f"日志已成功导出到:\n{file_path}")

        except Exception as e:
            self.log_message(f"❌ 导出日志失败: {e}")
            show_error_message(self, "错误", f"导出日志失败: {e}")

    def create_log_area(self, layout):
        """创建日志显示区"""
        log_group = QGroupBox("📋 运行日志")
        log_layout = QVBoxLayout()
        log_layout.setSpacing(10)
        log_layout.setContentsMargins(10, 10, 10, 10)

        # 日志过滤器
        filter_layout = QHBoxLayout()
        filter_layout.setSpacing(10)

        filter_layout.addWidget(QLabel("过滤:"))
        self.log_filter_combo = QComboBox()
        self.log_filter_combo.addItems(["全部", "信息", "警告", "错误", "成功"])
        self.log_filter_combo.currentTextChanged.connect(self.filter_log_messages)
        self.log_filter_combo.setFixedWidth(80)
        filter_layout.addWidget(self.log_filter_combo)

        # 自动滚动开关
        self.auto_scroll_checkbox = QCheckBox("自动滚动")
        self.auto_scroll_checkbox.setChecked(True)
        self.auto_scroll_checkbox.setToolTip("新消息时自动滚动到底部")
        filter_layout.addWidget(self.auto_scroll_checkbox)

        filter_layout.addStretch()
        log_layout.addLayout(filter_layout)

        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(250)
        self.log_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 11px;
                line-height: 1.4;
                background-color: #1e1e1e;
                color: #d4d4d4;
                border: 1px solid #3c3c3c;
                border-radius: 4px;
                padding: 8px;
            }
        """)
        log_layout.addWidget(self.log_text)

        log_group.setLayout(log_layout)
        layout.addWidget(log_group)

    def filter_log_messages(self, filter_type):
        """过滤日志消息"""
        # 这里可以实现日志过滤逻辑
        # 暂时保留原有功能，后续可以扩展
        pass

    # ==================== 事件处理方法 ====================

    def on_config_changed(self, config_name):
        """配置组改变时的处理"""
        if config_name:
            config_data = self.config_manager.get_group(config_name)
            if config_data:
                self.load_config_to_gui(config_data)
                # 保存窗口状态
                self.save_window_state()

    def on_pagination_enabled_changed(self, state):
        """动态翻页启用状态改变"""
        enabled = state == Qt.Checked
        self.pagination_type_combo.setEnabled(enabled)
        self.click_pagination_group.setEnabled(enabled)
        self.scroll_pagination_group.setEnabled(enabled)

    def on_pagination_type_changed(self, pagination_type):
        """翻页类型改变"""
        if pagination_type == '点击翻页':
            self.click_pagination_group.setVisible(True)
            self.scroll_pagination_group.setVisible(False)
            self.manual_pagination_group.setVisible(False)
        elif pagination_type == '滚动翻页':
            self.click_pagination_group.setVisible(False)
            self.scroll_pagination_group.setVisible(True)
            self.manual_pagination_group.setVisible(False)
        elif pagination_type == '手动翻页':
            self.click_pagination_group.setVisible(False)
            self.scroll_pagination_group.setVisible(False)
            self.manual_pagination_group.setVisible(True)
        else:
            self.click_pagination_group.setVisible(False)
            self.scroll_pagination_group.setVisible(False)
            self.manual_pagination_group.setVisible(False)

    def update_filename_placeholder(self, format_type):
        """更新文件名占位符"""
        if format_type == "Excel":
            self.export_filename_edit.setPlaceholderText("可选，例如: 文章数据 (将保存为.xlsx)")
        else:
            self.export_filename_edit.setPlaceholderText("可选，例如: 文章数据 (将保存为.csv)")

    # ==================== AI配置相关方法 ====================

    def start_ai_config(self):
        """启动AI智能配置"""
        input_url = self.input_url_edit.text().strip()
        if not input_url:
            show_warning_message(self, "警告", "请先输入要分析的URL")
            return

        if self.ai_manager.is_running():
            show_warning_message(self, "警告", "AI分析正在进行中，请等待完成")
            return

        self.ai_button.setEnabled(False)
        self.ai_button.setText("AI分析中...")

        # 获取字段配置
        field_config = self.get_field_config_from_gui()
        field_preset = None
        custom_fields = None

        if field_config.get('use_field_config', False):
            field_preset = field_config.get('field_preset')
            custom_fields = field_config.get('custom_field_list')

        # 启动AI分析（使用新的接口）
        success = self.ai_manager.start_ai_analysis_new(
            input_url,
            "full",
            self.on_ai_config_progress,
            self.on_ai_config_result,
            self.on_ai_config_error,
            field_preset,
            custom_fields
        )

        if not success:
            self.ai_button.setEnabled(True)
            self.ai_button.setText("AI智能配置")

    def on_ai_config_result(self, result):
        """AI配置结果处理"""
        self.ai_button.setEnabled(True)
        self.ai_button.setText("AI智能配置")

        if result and result.get('success'):
            # 应用分析结果到GUI
            gui_widgets = {
                'list_container_selector': self.list_container_edit,
                'article_item_selector': self.article_item_edit,
                'title_selectors': self.title_selectors_edit,
                'content_selectors': self.content_selectors_edit,
                'date_selectors': self.date_selectors_edit,
                'source_selectors': self.source_selectors_edit
            }

            success = self.ai_manager.apply_analysis_result_to_gui(result, gui_widgets)

            if success:
                self.log_message("✅ AI智能配置完成")
                show_info_message(self, "成功", f"AI智能配置完成！置信度: {result['final_config']['confidence_score']:.2f}")
            else:
                self.log_message("⚠️ 应用AI分析结果失败")
                show_warning_message(self, "警告", "应用AI分析结果失败")
        else:
            self.log_message("⚠️ AI分析未返回有效结果")
            error_msg = result.get('error', '未知错误') if result else '未知错误'
            show_warning_message(self, "警告", f"AI分析失败: {error_msg}")

    def on_ai_config_error(self, error_message):
        """AI配置错误处理"""
        self.ai_button.setEnabled(True)
        self.ai_button.setText("AI智能配置")
        self.log_message(f"❌ AI配置失败: {error_message}")
        show_error_message(self, "错误", f"AI配置失败: {error_message}")

    def on_ai_config_progress(self, progress_message):
        """AI配置进度处理"""
        self.log_message(f"🤖 {progress_message}")

    # ==================== 配置管理相关方法 ====================

    def load_config(self):
        """加载配置"""
        # 加载三级分类下拉框
        self.load_category_combos()

        # 加载窗口状态（分类和配置组选择）
        self.load_window_state()

        # 加载当前配置组
        current_group = self.config_manager.get_current_group()
        groups = self.config_manager.get_groups()

        if current_group and current_group in groups:
            # 根据当前配置组设置分类选择
            config_data = self.config_manager.get_group(current_group)
            if config_data:
                category_path = config_data.get("category_path", "政府机构/人大系统/地方人大")
                self.set_category_selection(category_path)
                self.config_combo.setCurrentText(current_group)
                self.load_config_to_gui(config_data)
        else:
            # 加载默认配置
            default_config = self.config_manager.get_default_config()
            self.load_config_to_gui(default_config)

        # 更新AI状态
        self.update_ai_status()

    def set_category_selection(self, category_path):
        """根据分类路径设置分类选择（支持3级和4级路径）"""
        try:
            parts = category_path.split("/")
            if len(parts) >= 3:
                parent, sub, child = parts[0], parts[1], parts[2]

                # 设置父级分类
                parent_index = self.parent_category_combo.findText(parent)
                if parent_index >= 0:
                    self.parent_category_combo.setCurrentIndex(parent_index)
                    self.on_parent_category_changed(parent)

                    # 设置次级分类
                    sub_index = self.sub_category_combo.findText(sub)
                    if sub_index >= 0:
                        self.sub_category_combo.setCurrentIndex(sub_index)
                        self.on_sub_category_changed(sub)

                        # 设置子级分类
                        child_index = self.child_category_combo.findText(child)
                        if child_index >= 0:
                            self.child_category_combo.setCurrentIndex(child_index)
                            self.on_child_category_changed(child)
        except Exception as e:
            print(f"设置分类选择失败: {e}")

    def load_config_to_gui(self, config_data):
        """将配置数据加载到GUI控件"""
        # 合并默认配置
        config_data = self.config_manager.merge_config_with_defaults(config_data)

        # 动态翻页配置提前获取
        pagination_config = config_data.get('pagination_config', {})
        # 新增：同步重复item提前结束开关
        self.duplicate_last_item_checkbox.setChecked(pagination_config.get('stop_on_duplicate_last_item', True))

        # 基础配置
        self.input_url_edit.setText(config_data.get('input_url', ''))
        self.base_url_edit.setText(config_data.get('base_url', ''))
        self.max_pages_edit.setText(str(config_data.get('max_pages', '')))
        self.start_page_edit.setText(str(config_data.get('start_page', '1')))  # 新增：初始页数
        self.url_mode_combo.setCurrentText(config_data.get('url_mode', 'absolute'))
        self.page_suffix_edit.setText(config_data.get('page_suffix', 'index_{n}.html'))

        # 选择器配置（删除类型参数）
        self.list_container_edit.setText(config_data.get('list_container_selector', '.main'))
        self.article_item_edit.setText(config_data.get('article_item_selector', '.clearfix.ty_list li a'))
        # 处理选择器配置（支持新旧格式兼容）
        title_selectors = config_data.get('title_selectors', config_data.get('title_selector', ''))
        if isinstance(title_selectors, list):
            title_selectors = ','.join(title_selectors)
        self.title_selectors_edit.setText(title_selectors)

        content_selectors = config_data.get('content_selectors', [])
        if isinstance(content_selectors, list):
            content_selectors = ','.join(content_selectors)
        self.content_selectors_edit.setText(content_selectors)

        date_selectors = config_data.get('date_selectors', config_data.get('date_selector', ''))
        if isinstance(date_selectors, list):
            date_selectors = ','.join(date_selectors)
        self.date_selectors_edit.setText(date_selectors)

        source_selectors = config_data.get('source_selectors', config_data.get('source_selector', ''))
        if isinstance(source_selectors, list):
            source_selectors = ','.join(source_selectors)
        self.source_selectors_edit.setText(source_selectors)

        # 高级配置
        self.crawl_mode_combo.setCurrentText(config_data.get('mode', 'balance'))
        self.collect_links_checkbox.setChecked(config_data.get('collect_links', True))
        self.headless_checkbox.setChecked(config_data.get('headless', True))
        self.disable_js_injection_checkbox.setChecked(config_data.get('disable_js_injection', False))
        self.export_filename_edit.setText(config_data.get('export_filename', ''))
        self.file_format_combo.setCurrentText(config_data.get('file_format', 'CSV'))

        # 设置Excel写入策略
        excel_strategy = config_data.get('excel_write_strategy', 'smart')
        if excel_strategy == 'smart':
            self.excel_mode_combo.setCurrentText("智能批量 (推荐)")
        elif excel_strategy == 'direct':
            self.excel_mode_combo.setCurrentText("直接写入 (兼容)")
        elif excel_strategy == 'hybrid':
            self.excel_mode_combo.setCurrentText("混合策略 (CSV转Excel)")
        elif excel_strategy == 'batch':
            self.excel_mode_combo.setCurrentText("批量写入 (手动)")

        self.classid_edit.setText(config_data.get('classid', ''))
        self.thread_count_spin.setValue(config_data.get('max_workers', 5))
        self.retry_spin.setValue(config_data.get('retry', 2))
        self.interval_spin.setValue(config_data.get('interval', 0))

        # URL缓存配置
        self.skip_pagination_checkbox.setChecked(config_data.get('skip_pagination_if_cached', False))

        # 动态翻页配置
        pagination_config = config_data.get('pagination_config', {})
        self.enable_pagination_checkbox.setChecked(pagination_config.get('enabled', False))
        self.pagination_type_combo.setCurrentText(pagination_config.get('pagination_type', '禁用动态翻页'))
        self.next_button_selector_edit.setText(pagination_config.get('next_button_selector', 'a.next:not(.lose)'))
        self.content_ready_selector_edit.setText(pagination_config.get('content_ready_selector', ''))
        self.wait_after_click_spin.setValue(pagination_config.get('wait_after_click', 2000))
        self.timeout_spin.setValue(pagination_config.get('timeout', 10000))
        self.disabled_check_checkbox.setChecked(pagination_config.get('disabled_check', True))
        self.scroll_container_selector_edit.setText(pagination_config.get('scroll_container_selector', 'body'))
        self.scroll_step_spin.setValue(pagination_config.get('scroll_step', 800))
        self.scroll_delay_spin.setValue(pagination_config.get('scroll_delay', 2000))
        self.load_indicator_selector_edit.setText(pagination_config.get('load_indicator_selector', ''))

        # 手动翻页配置
        self.excel_file_edit.setText(pagination_config.get('excel_file_path', 'manual_pagination/url_templates.xlsx'))
        self.manual_wait_spin.setValue(pagination_config.get('manual_wait_between_pages', 2000))
        self.save_progress_checkbox.setChecked(pagination_config.get('save_progress', True))

        # 触发翻页设置更新
        self.on_pagination_enabled_changed(Qt.Checked if pagination_config.get('enabled', False) else Qt.Unchecked)
        self.on_pagination_type_changed(pagination_config.get('pagination_type', '禁用动态翻页'))

    def get_config_from_gui(self):
        """从GUI控件获取配置数据"""
        filters = getattr(self, 'filters', None)
        if filters is None:
            filters = []
        config_data = {
            'input_url': self.input_url_edit.text().strip(),
            'base_url': self.base_url_edit.text().strip(),
            'max_pages': self.max_pages_edit.text().strip(),
            'start_page': self.start_page_edit.text().strip(),  # 新增：初始页数
            'url_mode': self.url_mode_combo.currentText(),
            'page_suffix': self.page_suffix_edit.text().strip(),

            'list_container_selector': self.list_container_edit.text().strip(),
            'article_item_selector': self.article_item_edit.text().strip(),
            # 选择器配置（使用复数形式，支持多选择器）
            'title_selectors': [s.strip() for s in self.title_selectors_edit.text().split(',') if s.strip()],
            'content_selectors': [s.strip() for s in self.content_selectors_edit.text().split(',') if s.strip()],
            'date_selectors': [s.strip() for s in self.date_selectors_edit.text().split(',') if s.strip()],
            'source_selectors': [s.strip() for s in self.source_selectors_edit.text().split(',') if s.strip()],

            'mode': self.crawl_mode_combo.currentText(),
            'collect_links': self.collect_links_checkbox.isChecked(),
            'headless': self.headless_checkbox.isChecked(),
            'disable_js_injection': self.disable_js_injection_checkbox.isChecked(),
            'export_filename': self.export_filename_edit.text().strip(),
            'file_format': self.file_format_combo.currentText(),
            'excel_write_strategy': self.get_excel_write_strategy(),
            'classid': self.classid_edit.text().strip(),
            'max_workers': self.thread_count_spin.value(),
            'retry': self.retry_spin.value(),
            'interval': self.interval_spin.value(),
            'filters': filters,

            # URL缓存设置
            'skip_pagination_if_cached': self.skip_pagination_checkbox.isChecked(),

            'pagination_config': {
                'enabled': self.enable_pagination_checkbox.isChecked(),
                'pagination_type': self.pagination_type_combo.currentText(),
                'next_button_selector': self.next_button_selector_edit.text().strip(),
                'content_ready_selector': self.content_ready_selector_edit.text().strip(),
                'wait_after_click': self.wait_after_click_spin.value(),
                'timeout': self.timeout_spin.value(),
                'disabled_check': self.disabled_check_checkbox.isChecked(),
                'scroll_container_selector': self.scroll_container_selector_edit.text().strip(),
                'scroll_step': self.scroll_step_spin.value(),
                'scroll_delay': self.scroll_delay_spin.value(),
                'load_indicator_selector': self.load_indicator_selector_edit.text().strip(),
                'stop_on_duplicate_last_item': self.duplicate_last_item_checkbox.isChecked(),  # 新增
                # 手动翻页配置
                'excel_file_path': self.excel_file_edit.text().strip(),
                'manual_wait_between_pages': self.manual_wait_spin.value(),
                'save_progress': self.save_progress_checkbox.isChecked()
            },

            # 模组配置
            'module_config': {
                'enabled': getattr(self, 'module_enabled', None) and self.module_enabled.isChecked(),
                'config_file': getattr(self, 'config_file_path', None) and self.config_file_path.text()
            },

            # 字段配置
            'field_config': self.get_field_config_from_gui()
        }
        return config_data

    def get_field_config_from_gui(self):
        """从GUI获取字段配置"""
        if not FIELD_CONFIG_AVAILABLE:
            return {
                'use_field_config': False,
                'field_preset': '',
                'custom_field_list': []
            }

        try:
            # 检查字段配置开关是否启用
            field_config_enabled = False
            if hasattr(self, 'enable_field_config_checkbox'):
                field_config_enabled = self.enable_field_config_checkbox.isChecked()

            if not field_config_enabled:
                return {
                    'use_field_config': False,
                    'field_preset': '',
                    'custom_field_list': []
                }

            # 获取当前选中的预设
            field_preset = ''
            if hasattr(self, 'field_preset_combo'):
                preset_data = self.field_preset_combo.currentData()
                if preset_data:
                    field_preset = preset_data

            # 获取自定义字段列表
            custom_field_list = []
            if hasattr(self, 'field_checkboxes'):
                for field_name, checkbox in self.field_checkboxes.items():
                    if checkbox.isChecked():
                        custom_field_list.append(field_name)

            # 获取自定义字段（用户自己填写的字段名和值）
            custom_fields = {}
            if hasattr(self, 'custom_fields'):
                custom_fields = self.custom_fields.copy()

            return {
                'use_field_config': True,
                'field_preset': field_preset,
                'custom_field_list': custom_field_list,
                'custom_fields': custom_fields  # 新增自定义字段
            }
        except Exception as e:
            self.log_message(f"获取字段配置失败: {e}")
            return {
                'use_field_config': False,
                'field_preset': '',
                'custom_field_list': []
            }

    def save_config(self):
        """保存当前配置"""
        current_group = self.config_combo.currentText()
        if not current_group:
            show_warning_message(self, "警告", "请先选择或创建一个配置组")
            return

        config_data = self.get_config_from_gui()

        # 验证配置
        errors = self.config_manager.validate_config(config_data)
        if errors:
            error_message = "配置验证失败:\n" + "\n".join(errors)
            show_error_message(self, "配置错误", error_message)
            return

        # 获取当前4级分类路径
        category_path = self.get_current_category_path()

        # 保存配置（传递组名、配置数据和分类路径）
        success = self.config_manager.add_group(current_group, config_data, category_path)
        if success:
            # 更新配置组下拉框显示完整路径
            full_path_name = category_path
            if self.config_combo.currentText() != full_path_name:
                # 刷新配置组列表
                self.refresh_current_category_configs()
                # 选择新的完整路径名称
                index = self.config_combo.findText(full_path_name)
                if index >= 0:
                    self.config_combo.setCurrentIndex(index)

            show_info_message(self, "成功", f"配置已保存\n路径: {full_path_name}")
            self.log_message(f"✅ 配置已保存: {full_path_name}")
        else:
            show_error_message(self, "错误", "保存配置失败")

    def create_new_config(self):
        """创建新配置组"""
        # 检查是否选择了完整的3级分类
        parent = self.parent_category_combo.currentText()
        sub = self.sub_category_combo.currentText()
        child = self.child_category_combo.currentText()

        if not (parent and sub and child):
            show_warning_message(self, "警告", "请先选择完整的3级分类（政府机构/人大系统/具体机构）")
            return

        fourth_level = get_text_input(self, "新建配置", f"请输入第4级分类名称:\n当前路径: {parent}/{sub}/{child}/")
        if fourth_level:
            # 构建完整4级路径
            category_path = f"{parent}/{sub}/{child}/{fourth_level}"

            # 检查是否已存在
            if category_path in self.config_manager.get_groups():
                show_warning_message(self, "警告", f"配置组 '{category_path}' 已存在")
                return

            # 使用当前GUI设置创建新配置
            config_data = self.get_config_from_gui()
            config_data["category_path"] = category_path

            success = self.config_manager.add_group(fourth_level, config_data, category_path)

            if success:
                # 智能刷新：只刷新当前分类的配置组列表
                self.refresh_current_category_configs(parent, sub, child)

                # 选择新创建的配置组（使用完整路径名称）
                self.config_combo.setCurrentText(category_path)
                self.on_config_changed(category_path)

                show_info_message(self, "成功", f"配置组已创建\n路径: {category_path}")
                self.log_message(f"✅ 配置组已创建: {category_path}")
            else:
                show_error_message(self, "错误", "创建配置组失败")

    def copy_config(self):
        """复制当前配置为新配置组"""
        current_group = self.config_combo.currentText()
        if not current_group:
            show_warning_message(self, "警告", "请先选择要复制的配置组")
            return

        name = get_text_input(self, "复制配置", f"请输入新配置组名称:\n(复制自: {current_group})")
        if name:
            # 获取当前4级分类路径
            category_path = self.get_category_path_for_new_config(name)

            # 使用完整4级路径作为配置组名称
            full_path_name = category_path

            if full_path_name in self.config_manager.get_groups():
                show_warning_message(self, "警告", f"配置组 '{full_path_name}' 已存在")
                return

            # 使用当前GUI设置创建新配置
            config_data = self.get_config_from_gui()
            config_data["category_path"] = category_path

            success = self.config_manager.add_group(name, config_data, category_path)

            if success:
                # 刷新当前分类的配置组列表
                self.on_child_category_changed(self.child_category_combo.currentText())
                self.config_combo.setCurrentText(full_path_name)
                show_info_message(self, "成功", f"配置组已创建\n复制自: {current_group}\n路径: {full_path_name}")
                self.log_message(f"✅ 配置组已复制创建: {full_path_name}")
            else:
                show_error_message(self, "错误", "复制配置组失败")

    def edit_config_group(self):
        """编辑配置组信息"""
        current_group = self.config_combo.currentText()
        if not current_group:
            show_warning_message(self, "警告", "请先选择要编辑的配置组")
            return

        try:
            # 保存当前状态
            current_parent = self.parent_category_combo.currentText()
            current_sub = self.sub_category_combo.currentText()
            current_child = self.child_category_combo.currentText()

            dialog = ConfigGroupEditDialog(self, current_group)
            if dialog.exec_() == QDialog.Accepted:
                # 智能刷新：保持分类选择状态，只刷新配置组列表
                self.load_category_combos()

                # 恢复分类选择
                if current_parent:
                    parent_index = self.parent_category_combo.findText(current_parent)
                    if parent_index >= 0:
                        self.parent_category_combo.setCurrentIndex(parent_index)
                        self.on_parent_category_changed(current_parent)

                        if current_sub:
                            sub_index = self.sub_category_combo.findText(current_sub)
                            if sub_index >= 0:
                                self.sub_category_combo.setCurrentIndex(sub_index)
                                self.on_sub_category_changed(current_sub)

                                if current_child:
                                    child_index = self.child_category_combo.findText(current_child)
                                    if child_index >= 0:
                                        self.child_category_combo.setCurrentIndex(child_index)
                                        self.on_child_category_changed(current_child)

                show_info_message(self, "成功", "配置组信息已更新")
        except Exception as e:
            show_error_message(self, "错误", f"编辑配置组失败: {e}")

    def delete_config(self):
        """删除配置组"""
        current_group = self.config_combo.currentText()
        if not current_group:
            show_warning_message(self, "警告", "请先选择要删除的配置组")
            return

        if show_question_message(self, "确认删除", f"确定要删除配置组 '{current_group}' 吗？"):
            try:
                success = self.config_manager.delete_group(current_group)
                if success:
                    # 智能刷新配置组列表
                    self.refresh_current_category_configs()
                    show_info_message(self, "成功", f"配置组 '{current_group}' 已删除")
                    self.log_message(f"✅ 配置组 '{current_group}' 已删除")
                else:
                    show_error_message(self, "错误", "删除配置组失败")
            except Exception as e:
                show_error_message(self, "错误", f"删除配置组失败: {e}")
                self.log_message(f"❌ 删除配置组失败: {e}")

    def open_config_manager(self):
        """打开配置管理对话框"""
        try:
            # 保存当前状态
            current_parent = self.parent_category_combo.currentText()
            current_sub = self.sub_category_combo.currentText()
            current_child = self.child_category_combo.currentText()
            current_config = self.config_combo.currentText()

            dialog = ConfigManagerDialog(self)
            dialog.exec_()

            # 智能刷新：保持分类选择状态
            self.load_category_combos()

            # 恢复分类选择
            if current_parent:
                parent_index = self.parent_category_combo.findText(current_parent)
                if parent_index >= 0:
                    self.parent_category_combo.setCurrentIndex(parent_index)
                    self.on_parent_category_changed(current_parent)

                    if current_sub:
                        sub_index = self.sub_category_combo.findText(current_sub)
                        if sub_index >= 0:
                            self.sub_category_combo.setCurrentIndex(sub_index)
                            self.on_sub_category_changed(current_sub)

                            if current_child:
                                child_index = self.child_category_combo.findText(current_child)
                                if child_index >= 0:
                                    self.child_category_combo.setCurrentIndex(child_index)
                                    self.on_child_category_changed(current_child)

                                    # 尝试恢复配置组选择
                                    if current_config:
                                        config_index = self.config_combo.findText(current_config)
                                        if config_index >= 0:
                                            self.config_combo.setCurrentIndex(config_index)
                                            self.on_config_changed(current_config)

        except Exception as e:
            show_error_message(self, "错误", f"打开配置管理失败: {e}")

    # ==================== 新增：三级分类处理方法 ====================

    def load_category_combos(self):
        """加载三级分类下拉框"""
        try:
            # 清空所有下拉框
            self.parent_category_combo.clear()
            self.sub_category_combo.clear()
            self.child_category_combo.clear()
            self.config_combo.clear()

            # 加载父级分类
            parent_categories = self.config_manager.config_manager.get_parent_categories()
            self.parent_category_combo.addItems(parent_categories)

            # 如果有父级分类，加载第一个的子分类
            if parent_categories:
                self.on_parent_category_changed(parent_categories[0])

        except Exception as e:
            print(f"加载分类下拉框失败: {e}")

    def on_parent_category_changed(self, parent_category):
        """父级分类改变事件"""
        try:
            # 清空次级和子级分类
            self.sub_category_combo.clear()
            self.child_category_combo.clear()
            self.config_combo.clear()

            if not parent_category:
                return

            # 加载次级分类
            sub_categories = self.config_manager.config_manager.get_sub_categories(parent_category)
            self.sub_category_combo.addItems(sub_categories)

            # 如果有次级分类，加载第一个的子分类
            if sub_categories:
                self.on_sub_category_changed(sub_categories[0])

            # 保存窗口状态
            self.save_window_state()

        except Exception as e:
            print(f"父级分类改变处理失败: {e}")

    def on_sub_category_changed(self, sub_category):
        """次级分类改变事件"""
        try:
            # 清空子级分类和配置组
            self.child_category_combo.clear()
            self.config_combo.clear()

            parent_category = self.parent_category_combo.currentText()
            if not parent_category or not sub_category:
                return

            # 加载子级分类
            child_categories = self.config_manager.config_manager.get_child_categories(parent_category, sub_category)
            self.child_category_combo.addItems(child_categories)

            # 如果有子级分类，加载第一个的配置组
            if child_categories:
                self.on_child_category_changed(child_categories[0])

            # 保存窗口状态
            self.save_window_state()

        except Exception as e:
            print(f"次级分类改变处理失败: {e}")

    def on_child_category_changed(self, child_category):
        """子级分类改变事件（在第3级分类下直接显示所有配置组）"""
        try:
            # 清空配置组
            self.config_combo.clear()

            parent_category = self.parent_category_combo.currentText()
            sub_category = self.sub_category_combo.currentText()

            if not parent_category or not sub_category or not child_category:
                return

            # 直接获取第3级分类下的所有配置组
            third_level_path = f"{parent_category}/{sub_category}/{child_category}"
            all_configs = self.get_configs_in_third_level_category(third_level_path)

            if all_configs:
                self.config_combo.addItems(all_configs)
                # 自动选择第一个配置组
                self.config_combo.setCurrentText(all_configs[0])
                self.on_config_changed(all_configs[0])

            # 自动更新文件名
            self.update_export_filename()

            # 保存窗口状态
            self.save_window_state()

        except Exception as e:
            print(f"子级分类改变处理失败: {e}")

    def update_export_filename(self):
        """自动更新导出文件名"""
        try:
            # 如果用户没有手动设置文件名，则自动生成
            current_filename = self.export_filename_edit.text().strip()
            if not current_filename:
                auto_filename = self.generate_filename_from_category_path()
                if auto_filename and auto_filename != "文章数据":
                    self.export_filename_edit.setText(auto_filename)
        except Exception as e:
            print(f"更新导出文件名失败: {e}")

    def get_current_category_path(self):
        """获取当前选择的分类路径（4级结构）"""
        parent = self.parent_category_combo.currentText()
        sub = self.sub_category_combo.currentText()
        child = self.child_category_combo.currentText()

        # 在4级结构中，需要根据当前配置组确定第4级分类
        current_config = self.config_combo.currentText()
        if parent and sub and child and current_config:
            # 根据配置组名称推断第4级分类
            fourth_level = self.infer_fourth_level_from_config(current_config, child)
            return f"{parent}/{sub}/{child}/{fourth_level}"

        return "政府机构/人大系统/北京人大/监督纵横"  # 默认4级路径

    def infer_fourth_level_from_config(self, config_name, third_level):
        """根据配置组名称推断第4级分类"""
        try:
            # 如果配置组名称包含下划线，取后半部分作为第4级分类
            if "_" in config_name:
                return config_name.split("_")[-1]

            # 否则根据第3级分类推断常见的第4级分类
            if "政协" in third_level:
                return "提案工作"
            elif "人大" in third_level:
                return "监督纵横"
            else:
                return "常规工作"
        except:
            return "常规工作"

    def get_category_path_for_new_config(self, config_name):
        """为新配置组获取分类路径（4级结构）"""
        parent = self.parent_category_combo.currentText()
        sub = self.sub_category_combo.currentText()
        child = self.child_category_combo.currentText()

        if parent and sub and child:
            # 根据配置组名称推断第4级分类
            fourth_level = self.infer_fourth_level_from_config(config_name, child)
            return f"{parent}/{sub}/{child}/{fourth_level}"

        # 如果没有选择完整的3级分类，返回默认路径
        return f"政府机构/人大系统/地方人大/监督纵横"

    def refresh_current_category_configs(self, parent=None, sub=None, child=None):
        """智能刷新当前分类的配置组列表，保持分类选择状态"""
        try:
            # 如果没有传入参数，使用当前选择的分类
            if not parent:
                parent = self.parent_category_combo.currentText()
            if not sub:
                sub = self.sub_category_combo.currentText()
            if not child:
                child = self.child_category_combo.currentText()

            # 直接刷新配置组列表，不重新设置分类选择
            if parent and sub and child:
                # 获取第3级分类下的所有配置组
                third_level_path = f"{parent}/{sub}/{child}"
                all_configs = self.get_configs_in_third_level_category(third_level_path)

                # 保存当前选择的配置组
                current_config = self.config_combo.currentText()

                # 清空并重新填充配置组列表
                self.config_combo.clear()
                if all_configs:
                    self.config_combo.addItems(all_configs)

                    # 尝试恢复之前的选择
                    if current_config and current_config in all_configs:
                        self.config_combo.setCurrentText(current_config)
                    else:
                        # 如果之前的选择不存在，选择第一个
                        self.config_combo.setCurrentText(all_configs[0])
                        self.on_config_changed(all_configs[0])

        except Exception as e:
            print(f"刷新分类配置失败: {e}")

    def get_configs_in_third_level_category(self, third_level_path):
        """获取第3级分类下的所有配置组名称"""
        try:
            parts = third_level_path.split("/")
            if len(parts) != 3:
                return []

            parent, sub, child = parts

            # 获取第4级分类
            fourth_categories = self.config_manager.config_manager.get_fourth_categories(parent, sub, child)

            # 收集所有第4级分类下的配置组
            all_configs = []
            for fourth_category in fourth_categories:
                category_path = f"{parent}/{sub}/{child}/{fourth_category}"
                configs = self.config_manager.config_manager.get_configs_by_category_path(category_path)
                all_configs.extend(configs)

            return all_configs
        except Exception as e:
            print(f"获取第3级分类配置失败: {e}")
            return []

    def get_configs_in_third_level_category_for_tree(self, third_level_path):
        """为分类树获取第3级分类下的所有配置组名称"""
        try:
            parts = third_level_path.split("/")
            if len(parts) != 3:
                return []

            parent, sub, child = parts

            # 获取第4级分类
            fourth_categories = self.config_manager.config_manager.get_fourth_categories(parent, sub, child)

            # 收集所有第4级分类下的配置组
            all_configs = []
            for fourth_category in fourth_categories:
                category_path = f"{parent}/{sub}/{child}/{fourth_category}"
                configs = self.config_manager.config_manager.get_configs_by_category_path(category_path)
                all_configs.extend(configs)

            return all_configs
        except Exception as e:
            print(f"获取第3级分类配置失败(树): {e}")
            return []

    def save_window_state(self):
        """保存窗口状态（分类和配置组选择）"""
        try:
            state = {
                "parent_category": self.parent_category_combo.currentText(),
                "sub_category": self.sub_category_combo.currentText(),
                "child_category": self.child_category_combo.currentText(),
                "config_group": self.config_combo.currentText(),
                "export_filename": self.export_filename_edit.text()
            }

            # 保存到配置文件
            state_file = "configs/app/window_state.json"
            os.makedirs(os.path.dirname(state_file), exist_ok=True)
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存窗口状态失败: {e}")

    def load_window_state(self):
        """加载窗口状态（分类和配置组选择）"""
        try:
            state_file = "configs/app/window_state.json"
            if not os.path.exists(state_file):
                return

            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)

            # 恢复分类选择
            parent = state.get("parent_category", "")
            if parent:
                parent_index = self.parent_category_combo.findText(parent)
                if parent_index >= 0:
                    self.parent_category_combo.setCurrentIndex(parent_index)
                    self.on_parent_category_changed(parent)

                    sub = state.get("sub_category", "")
                    if sub:
                        sub_index = self.sub_category_combo.findText(sub)
                        if sub_index >= 0:
                            self.sub_category_combo.setCurrentIndex(sub_index)
                            self.on_sub_category_changed(sub)

                            child = state.get("child_category", "")
                            if child:
                                child_index = self.child_category_combo.findText(child)
                                if child_index >= 0:
                                    self.child_category_combo.setCurrentIndex(child_index)
                                    self.on_child_category_changed(child)

                                    # 恢复配置组选择
                                    config_group = state.get("config_group", "")
                                    if config_group:
                                        config_index = self.config_combo.findText(config_group)
                                        if config_index >= 0:
                                            self.config_combo.setCurrentIndex(config_index)
                                            self.on_config_changed(config_group)

            # 恢复导出文件名
            export_filename = state.get("export_filename", "")
            if export_filename:
                self.export_filename_edit.setText(export_filename)

        except Exception as e:
            print(f"加载窗口状态失败: {e}")

    # ==================== 爬取控制相关方法 ====================

    def start_crawling(self):
        """开始爬取"""
        # 验证配置
        config_data = self.get_config_from_gui()
        errors = ConfigValidator.validate_basic_config(config_data)
        errors.extend(ConfigValidator.validate_selectors(config_data))

        # 验证翻页配置
        if config_data['pagination_config']['enabled']:
            pagination_errors = PaginationConfigHelper.validate_pagination_config(config_data['pagination_config'])
            errors.extend(pagination_errors)

        if errors:
            error_message = "配置验证失败:\n" + "\n".join(errors)
            show_error_message(self, "配置错误", error_message)
            return

        # 准备爬虫配置
        crawler_config = self.config_manager.prepare_crawler_config(config_data)

        # 启动爬取
        success = self.crawler_manager.start_crawling(
            crawler_config,
            self.log_message,
            self.on_crawling_finished,
            self.on_progress_update  # 添加进度回调
        )

        if success:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.log_message("🚀 开始爬取任务...")
        else:
            show_warning_message(self, "警告", "爬取任务启动失败")

    def stop_crawling(self):
        """停止爬取"""
        success = self.crawler_manager.stop_crawling(self.log_message)
        if success:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.progress_bar.setVisible(False)

    def on_progress_update(self, current, total):
        """进度更新处理"""
        if total > 0:
            progress = int((current / total) * 100)
            self.progress_bar.setValue(progress)
            self.progress_bar.setFormat(f"{current}/{total} ({progress}%)")
        else:
            self.progress_bar.setValue(0)
            self.progress_bar.setFormat("准备中...")

    def on_crawling_finished(self, result):
        """爬取完成处理"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)

        # 显示结果
        result_message = format_result_message(result)
        self.log_message(f"🎉 {result_message}")
        show_info_message(self, "爬取完成", result_message)

    # ==================== 其他工具方法 ====================

    def set_filter_rules(self):
        """设置内容过滤规则"""
        from PyQt5.QtWidgets import QInputDialog
        # 获取当前过滤规则
        current_filters = getattr(self, 'filters', None)
        if current_filters is None:
            config_data = self.get_config_from_gui()
            current_filters = config_data.get('filters', [])
        if not isinstance(current_filters, list):
            current_filters = []
        text, ok = QInputDialog.getMultiLineText(
            self,
            "编辑内容过滤规则",
            "每行一个关键词或正则表达式（支持部分正则语法）:",
            '\n'.join(current_filters)
        )
        if ok:
            filters = [line.strip() for line in text.splitlines() if line.strip()]
            self.filters = filters
            config_data = self.get_config_from_gui()
            config_data['filters'] = filters
            show_info_message(self, "成功", f"已设置 {len(filters)} 条内容过滤规则。")

    def browse_excel_file(self):
        """浏览Excel文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Excel文件",
            "manual_pagination/",
            "Excel文件 (*.xlsx *.xls)"
        )
        if file_path:
            self.excel_file_edit.setText(file_path)

    def create_excel_template(self):
        """创建Excel模板"""
        try:
            import os
            from manual_pagination.manual_pagination_handler import ManualPaginationHandler

            # 确保目录存在
            os.makedirs("manual_pagination", exist_ok=True)

            # 创建临时处理器来生成模板
            handler = ManualPaginationHandler(None)

            # 获取保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "保存Excel模板",
                "manual_pagination/url_templates.xlsx",
                "Excel文件 (*.xlsx)"
            )

            if file_path:
                # 创建模板
                success = handler.create_template_excel(file_path)
                if success:
                    self.excel_file_edit.setText(file_path)
                    show_info_message(self, "成功", f"Excel模板已创建: {file_path}")
                else:
                    show_error_message(self, "错误", "创建Excel模板失败")

        except Exception as e:
            show_error_message(self, "错误", f"创建Excel模板失败: {e}")

    def edit_excel_file(self):
        """编辑Excel文件"""
        excel_path = self.excel_file_edit.text().strip()
        if not excel_path:
            show_warning_message(self, "警告", "请先选择Excel文件")
            return

        if not os.path.exists(excel_path):
            show_warning_message(self, "警告", "Excel文件不存在，请先创建模板")
            return

        try:
            import subprocess
            import platform

            # 根据操作系统打开Excel文件
            if platform.system() == "Windows":
                os.startfile(excel_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", excel_path])
            else:  # Linux
                subprocess.run(["xdg-open", excel_path])

            show_info_message(self, "提示", "Excel文件已在默认程序中打开\n请编辑完成后保存文件")

        except Exception as e:
            show_error_message(self, "错误", f"打开Excel文件失败: {e}")

    def test_pagination_settings(self):
        """测试翻页设置"""
        # 这里可以实现翻页设置测试功能
        show_info_message(self, "提示", "翻页设置测试功能将在后续版本中实现")

    # 字段配置相关方法
    def on_field_config_enabled_changed(self, state):
        """字段配置开关状态改变"""
        enabled = state == Qt.Checked

        # 启用/禁用字段配置区域
        if hasattr(self, 'field_config_area'):
            self.field_config_area.setEnabled(enabled)

        # 启用/禁用自定义字段选择组
        if hasattr(self, 'custom_fields_group'):
            self.custom_fields_group.setEnabled(enabled)

        # 启用/禁用字段预览组
        if hasattr(self, 'field_preview_group'):
            self.field_preview_group.setEnabled(enabled)

        # 启用/禁用字段操作组
        if hasattr(self, 'field_actions_group'):
            self.field_actions_group.setEnabled(enabled)

        # 更新状态指示器
        if hasattr(self, 'field_config_status_label'):
            if enabled:
                self.field_config_status_label.setText("🔓 字段配置已启用")
                self.field_config_status_label.setStyleSheet("color: green; font-size: 12px;")
            else:
                self.field_config_status_label.setText("🔒 使用默认字段")
                self.field_config_status_label.setStyleSheet("color: #666; font-size: 12px;")

        # 记录日志
        status = "启用" if enabled else "禁用"
        self.log_message(f"字段配置功能已{status}")

    def initialize_field_config_state(self):
        """初始化字段配置状态"""
        try:
            # 确保字段配置开关的初始状态正确应用
            if hasattr(self, 'enable_field_config_checkbox'):
                initial_state = self.enable_field_config_checkbox.isChecked()
                # 手动触发一次状态更新
                from PyQt5.QtCore import Qt
                state = Qt.Checked if initial_state else Qt.Unchecked
                self.on_field_config_enabled_changed(state)



            self.log_message("字段配置状态初始化完成")

        except Exception as e:
            self.log_message(f"初始化字段配置状态失败: {e}")







    def on_preset_changed(self):
        """预设选择改变时的处理"""
        if not FIELD_CONFIG_AVAILABLE:
            return

        try:
            current_data = self.field_preset_combo.currentData()
            if current_data:
                presets = get_field_presets()
                if current_data in presets:
                    field_list = presets[current_data]
                    description = f"包含字段: {', '.join(field_list)}"
                    self.preset_description.setText(description)

                    # 更新复选框状态
                    self.update_checkboxes_from_preset(field_list)
        except Exception as e:
            self.log_message(f"更新预设说明失败: {e}")

    def update_checkboxes_from_preset(self, field_list):
        """根据预设更新复选框状态"""
        # 检查字段复选框是否存在
        if not hasattr(self, 'field_checkboxes'):
            return

        # 先清空所有选择
        for checkbox in self.field_checkboxes.values():
            checkbox.setChecked(False)

        # 选中预设中的字段
        for field_name in field_list:
            if field_name in self.field_checkboxes:
                self.field_checkboxes[field_name].setChecked(True)

        # 更新预览
        self.update_field_preview()

    def on_field_selection_changed(self):
        """字段选择改变时的处理"""
        self.update_field_preview()

    def update_field_preview(self):
        """更新字段预览"""
        # 检查必要的控件是否存在
        if not hasattr(self, 'current_fields_text') or not hasattr(self, 'field_count_label'):
            return

        selected_fields = []
        if hasattr(self, 'field_checkboxes'):
            for field_name, checkbox in self.field_checkboxes.items():
                if checkbox.isChecked():
                    selected_fields.append(field_name)

        if selected_fields:
            self.current_fields_text.setText(", ".join(selected_fields))
            self.field_count_label.setText(f"字段数量: {len(selected_fields)}")
        else:
            self.current_fields_text.setText("未选择任何字段")
            self.field_count_label.setText("字段数量: 0")

    def apply_field_preset(self):
        """应用字段预设"""
        if not FIELD_CONFIG_AVAILABLE:
            show_warning_message(self, "警告", "字段配置功能不可用")
            return

        try:
            current_data = self.field_preset_combo.currentData()
            if current_data:
                success = apply_field_preset(current_data)
                if success:
                    self.log_message(f"已应用字段预设: {current_data}")
                    show_info_message(self, "成功", f"已应用字段预设: {current_data}")
                else:
                    show_warning_message(self, "失败", f"应用字段预设失败: {current_data}")
            else:
                show_warning_message(self, "警告", "请选择一个预设")
        except Exception as e:
            self.log_message(f"应用字段预设失败: {e}")
            show_warning_message(self, "错误", f"应用字段预设失败: {e}")

    def apply_custom_fields(self):
        """应用自定义字段"""
        if not FIELD_CONFIG_AVAILABLE:
            show_warning_message(self, "警告", "字段配置功能不可用")
            return

        try:
            selected_fields = []
            for field_name, checkbox in self.field_checkboxes.items():
                if checkbox.isChecked():
                    selected_fields.append(field_name)

            if not selected_fields:
                show_warning_message(self, "警告", "请至少选择一个字段")
                return

            success = apply_custom_field_list(selected_fields)
            if success:
                self.log_message(f"已应用自定义字段: {selected_fields}")
                show_info_message(self, "成功", f"已应用 {len(selected_fields)} 个自定义字段")
            else:
                show_warning_message(self, "失败", "应用自定义字段失败")
        except Exception as e:
            self.log_message(f"应用自定义字段失败: {e}")
            show_warning_message(self, "错误", f"应用自定义字段失败: {e}")

    def select_all_fields(self):
        """选择所有字段"""
        for checkbox in self.field_checkboxes.values():
            checkbox.setChecked(True)
        self.update_field_preview()

    def clear_all_fields(self):
        """清空所有字段选择"""
        for checkbox in self.field_checkboxes.values():
            checkbox.setChecked(False)
        self.update_field_preview()

    def select_basic_fields(self):
        """选择基础字段"""
        if not FIELD_CONFIG_AVAILABLE:
            return

        try:
            presets = get_field_presets()
            basic_fields = presets.get("basic", [])
            self.update_checkboxes_from_preset(basic_fields)
        except Exception as e:
            self.log_message(f"选择基础字段失败: {e}")

    def refresh_field_list(self):
        """刷新字段列表"""
        self.load_available_fields()
        self.log_message("已刷新字段列表")

    def reset_to_default_fields(self):
        """重置为默认字段"""
        if not FIELD_CONFIG_AVAILABLE:
            show_warning_message(self, "警告", "字段配置功能不可用")
            return

        try:
            success = apply_field_preset("basic")
            if success:
                self.select_basic_fields()
                self.log_message("已重置为默认字段配置")
                show_info_message(self, "成功", "已重置为默认字段配置")
            else:
                show_warning_message(self, "失败", "重置字段配置失败")
        except Exception as e:
            self.log_message(f"重置字段配置失败: {e}")
            show_warning_message(self, "错误", f"重置字段配置失败: {e}")

    def export_field_config(self):
        """导出字段配置"""
        try:
            selected_fields = []
            for field_name, checkbox in self.field_checkboxes.items():
                if checkbox.isChecked():
                    selected_fields.append(field_name)

            if not selected_fields:
                show_warning_message(self, "警告", "没有选中的字段可以导出")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出字段配置", "field_config.json", "JSON文件 (*.json)"
            )

            if file_path:
                import json
                config_data = {
                    "selected_fields": selected_fields,
                    "export_time": time.strftime('%Y-%m-%d %H:%M:%S'),
                    "description": f"包含 {len(selected_fields)} 个字段的配置"
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, ensure_ascii=False, indent=2)

                self.log_message(f"字段配置已导出到: {file_path}")
                show_info_message(self, "成功", f"字段配置已导出到: {file_path}")

        except Exception as e:
            self.log_message(f"导出字段配置失败: {e}")
            show_warning_message(self, "错误", f"导出字段配置失败: {e}")

    def import_field_config(self):
        """导入字段配置"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "导入字段配置", "", "JSON文件 (*.json)"
            )

            if file_path:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                selected_fields = config_data.get("selected_fields", [])
                if not selected_fields:
                    show_warning_message(self, "警告", "配置文件中没有有效的字段配置")
                    return

                # 更新复选框状态
                self.update_checkboxes_from_preset(selected_fields)

                self.log_message(f"已导入字段配置: {len(selected_fields)} 个字段")
                show_info_message(self, "成功", f"已导入 {len(selected_fields)} 个字段的配置")

        except Exception as e:
            self.log_message(f"导入字段配置失败: {e}")
            show_warning_message(self, "错误", f"导入字段配置失败: {e}")

    def open_custom_selector_dialog(self):
        """打开自定义选择器对话框"""
        try:
            dialog = CustomSelectorDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                # 刷新字段列表以反映更改
                self.refresh_field_list()
                self.log_message("字段选择器配置已更新")
                show_info_message(self, "成功", "字段选择器配置已更新")
        except Exception as e:
            self.log_message(f"打开自定义选择器对话框失败: {e}")
            show_warning_message(self, "错误", f"打开自定义选择器对话框失败: {e}")

    def open_custom_field_dialog(self):
        """打开自定义字段对话框"""
        try:
            self.log_message("正在创建自定义字段对话框...")
            dialog = CustomFieldDialog(self)
            self.log_message("自定义字段对话框创建成功")

            result = dialog.exec_()
            self.log_message(f"对话框执行结果: {result}")

            if result == QDialog.Accepted:
                # 获取自定义字段
                self.log_message("正在获取自定义字段...")
                custom_fields = dialog.get_custom_fields()
                self.log_message(f"获取到的自定义字段: {custom_fields}")

                if custom_fields:
                    # 保存自定义字段到配置
                    self.save_custom_fields(custom_fields)
                    self.log_message(f"已添加 {len(custom_fields)} 个自定义字段")
                    show_info_message(self, "成功", f"已添加 {len(custom_fields)} 个自定义字段")
                else:
                    self.log_message("未添加任何自定义字段")
            else:
                self.log_message("用户取消了自定义字段对话框")
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.log_message(f"打开自定义字段对话框失败: {e}")
            self.log_message(f"错误详情: {error_details}")
            show_warning_message(self, "错误", f"打开自定义字段对话框失败: {e}")

    def save_custom_fields(self, custom_fields):
        """保存自定义字段到配置"""
        try:
            # 将自定义字段保存到实例变量中
            if not hasattr(self, 'custom_fields'):
                self.custom_fields = {}

            for field_name, field_value in custom_fields.items():
                self.custom_fields[field_name] = field_value
                self.log_message(f"保存自定义字段: {field_name} = {field_value}")

            # 更新字段预览
            self.update_field_preview()

        except Exception as e:
            self.log_message(f"保存自定义字段失败: {e}")

    def test_basic_selectors(self):
        """测试基础配置中的选择器"""
        try:
            # 显示URL选择对话框
            test_url = self.show_url_selection_dialog()
            if not test_url:
                return

            # 收集基础选择器配置
            basic_selectors = {}

            # 标题选择器
            title_selectors = self.title_selectors_edit.text().strip()
            if title_selectors:
                basic_selectors['title'] = [s.strip() for s in title_selectors.split(',') if s.strip()]

            # 内容选择器
            content_selectors = self.content_selectors_edit.text().strip()
            if content_selectors:
                basic_selectors['content'] = [s.strip() for s in content_selectors.split(',') if s.strip()]

            # 日期选择器
            date_selectors = self.date_selectors_edit.text().strip()
            if date_selectors:
                basic_selectors['dateget'] = [s.strip() for s in date_selectors.split(',') if s.strip()]

            # 来源选择器
            source_selectors = self.source_selectors_edit.text().strip()
            if source_selectors:
                basic_selectors['source'] = [s.strip() for s in source_selectors.split(',') if s.strip()]

            if not basic_selectors:
                show_warning_message(self, "警告", "没有配置任何选择器")
                return

            # 打开测试对话框
            dialog = SelectorTestDialog(self, test_url, basic_selectors)
            dialog.exec_()

        except Exception as e:
            self.log_message(f"测试基础选择器失败: {e}")
            show_warning_message(self, "错误", f"测试基础选择器失败: {e}")

    def get_cached_urls(self, limit=20):
        """获取缓存的URL列表"""
        cached_urls = []
        try:
            import os
            import csv

            url_cache_dir = "url_cache"
            if os.path.exists(url_cache_dir):
                # 遍历所有缓存文件
                for root, dirs, files in os.walk(url_cache_dir):
                    for file in files:
                        if file.endswith('_collected_urls.csv'):
                            file_path = os.path.join(root, file)
                            try:
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    reader = csv.DictReader(f)
                                    for row in reader:
                                        url = row.get('url', '').strip()
                                        if url and url not in cached_urls:
                                            cached_urls.append(url)
                                            if len(cached_urls) >= limit:
                                                break
                                    if len(cached_urls) >= limit:
                                        break
                            except Exception as e:
                                self.log_message(f"读取缓存文件失败 {file_path}: {e}")
                                continue
                    if len(cached_urls) >= limit:
                        break
        except Exception as e:
            self.log_message(f"获取缓存URL失败: {e}")

        return cached_urls

    def show_url_selection_dialog(self):
        """显示URL选择对话框"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QLineEdit, QPushButton, QButtonGroup, QRadioButton

        dialog = QDialog(self)
        dialog.setWindowTitle("选择测试URL")
        dialog.setModal(True)
        dialog.resize(600, 300)

        layout = QVBoxLayout()

        # 说明文字
        info_label = QLabel("请选择要测试的URL:")
        info_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # URL选择方式
        url_type_group = QButtonGroup()

        # 使用输入URL
        input_radio = QRadioButton("使用输入URL")
        input_radio.setChecked(True)
        url_type_group.addButton(input_radio, 0)
        layout.addWidget(input_radio)

        input_url_layout = QHBoxLayout()
        input_url_layout.addWidget(QLabel("URL:"))
        input_url_edit = QLineEdit()
        input_url_edit.setText(self.input_url_edit.text().strip())
        input_url_edit.setPlaceholderText("请输入要测试的URL")
        input_url_layout.addWidget(input_url_edit)
        layout.addLayout(input_url_layout)

        # 使用缓存URL
        cache_radio = QRadioButton("使用缓存URL")
        url_type_group.addButton(cache_radio, 1)
        layout.addWidget(cache_radio)

        cache_url_layout = QHBoxLayout()
        cache_url_layout.addWidget(QLabel("缓存URL:"))
        cache_url_combo = QComboBox()
        cache_url_combo.setEditable(False)

        # 加载缓存URL
        cached_urls = self.get_cached_urls(50)  # 获取最多50个缓存URL
        if cached_urls:
            cache_url_combo.addItems(cached_urls)
        else:
            cache_url_combo.addItem("暂无缓存URL")
            cache_radio.setEnabled(False)

        cache_url_layout.addWidget(cache_url_combo)
        layout.addLayout(cache_url_layout)

        # 按钮
        button_layout = QHBoxLayout()

        ok_button = QPushButton("确定")
        cancel_button = QPushButton("取消")

        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        # 连接信号
        def on_ok():
            dialog.accept()

        def on_cancel():
            dialog.reject()

        ok_button.clicked.connect(on_ok)
        cancel_button.clicked.connect(on_cancel)

        # 显示对话框
        if dialog.exec_() == QDialog.Accepted:
            if input_radio.isChecked():
                url = input_url_edit.text().strip()
                if not url:
                    show_warning_message(self, "警告", "请输入要测试的URL")
                    return None
                return url
            else:
                if cached_urls:
                    return cache_url_combo.currentText()
                else:
                    show_warning_message(self, "警告", "没有可用的缓存URL")
                    return None

        return None

    def test_selected_field_selectors(self):
        """测试选中字段的选择器"""
        try:
            # 显示URL选择对话框
            test_url = self.show_url_selection_dialog()
            if not test_url:
                return

            # 获取当前选中的字段
            selected_fields = []
            if hasattr(self, 'field_checkboxes'):
                for field_name, checkbox in self.field_checkboxes.items():
                    if checkbox.isChecked():
                        selected_fields.append(field_name)

            if not selected_fields:
                show_warning_message(self, "警告", "请先选择要测试的字段")
                return

            # 获取选中字段的选择器配置
            field_selectors = {}
            if FIELD_CONFIG_AVAILABLE:
                try:
                    manager = get_field_config_manager()
                    all_fields = manager.get_available_fields()

                    for field_name in selected_fields:
                        if field_name in all_fields:
                            selectors = all_fields[field_name].get('selectors', [])
                            if selectors:  # 只包含有选择器的字段
                                field_selectors[field_name] = selectors
                except Exception as e:
                    self.log_message(f"获取字段选择器配置失败: {e}")
                    show_warning_message(self, "错误", f"获取字段选择器配置失败: {e}")
                    return

            if not field_selectors:
                show_warning_message(self, "警告", "选中的字段中没有配置选择器的字段")
                return

            # 打开测试对话框
            dialog = SelectorTestDialog(self, test_url, field_selectors)
            dialog.exec_()

        except Exception as e:
            self.log_message(f"测试字段选择器失败: {e}")
            show_warning_message(self, "错误", f"测试字段选择器失败: {e}")







    def open_llm_config(self):
        """打开LLM配置对话框"""
        dialog = LLMConfigDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.update_ai_status()
            show_info_message(self, "成功", "LLM配置已保存")

    def update_ai_status(self):
        """更新AI状态显示"""
        if self.ai_manager.check_llm_config():
            self.ai_status_label.setText("已配置")
            self.ai_status_label.setStyleSheet("color: green;")
            self.ai_analyze_button.setEnabled(True)
        else:
            self.ai_status_label.setText("未配置")
            self.ai_status_label.setStyleSheet("color: red;")
            self.ai_analyze_button.setEnabled(False)

    def start_ai_analysis(self):
        """启动AI分析"""
        url = self.input_url_edit.text().strip()
        if not url:
            show_warning_message(self, "警告", "请先输入要分析的URL")
            return

        # 检查是否已有模组配置
        existing_module = self.ai_manager.check_module_exists(url)
        if existing_module:
            self.module_check_label.setText(f"域名已存在模组配置: {existing_module}")
            self.module_check_label.setStyleSheet("color: orange;")
            show_info_message(self, "提示", f"该域名已存在模组配置: {existing_module}\n无需重复分析")
            return
        else:
            self.module_check_label.setText("域名检查通过，可以进行AI分析")
            self.module_check_label.setStyleSheet("color: green;")

        # 禁用按钮
        self.ai_analyze_button.setEnabled(False)
        self.ai_progress_label.setText("正在启动AI分析...")

        # 获取字段配置
        field_config = self.get_field_config_from_gui()
        field_preset = None
        custom_fields = None

        if field_config.get('use_field_config', False):
            field_preset = field_config.get('field_preset')
            custom_fields = field_config.get('custom_field_list')

        # 启动AI分析
        self.ai_manager.start_ai_analysis_new(
            url=url,
            analysis_type="full",
            progress_callback=self.on_ai_progress,
            finished_callback=self.on_ai_finished,
            error_callback=self.on_ai_error,
            field_preset=field_preset,
            custom_fields=custom_fields
        )

    def on_ai_progress(self, message):
        """AI分析进度回调"""
        self.ai_progress_label.setText(message)

    def on_ai_finished(self, result):
        """AI分析完成回调"""
        self.ai_analyze_button.setEnabled(True)

        if result.get('success'):
            self.ai_progress_label.setText("AI分析完成！")

            # 应用分析结果到GUI
            gui_widgets = {
                'list_container_selector': self.list_container_edit,
                'article_item_selector': self.article_item_edit,
                'title_selectors': self.title_selectors_edit,
                'content_selectors': self.content_selectors_edit,
                'date_selectors': self.date_selectors_edit,
                'source_selectors': self.source_selectors_edit
            }

            success = self.ai_manager.apply_analysis_result_to_gui(result, gui_widgets)

            if success:
                # 询问是否保存为配置组
                reply = show_question_message(
                    self, "保存配置",
                    f"AI分析成功！置信度: {result['final_config']['confidence_score']:.2f}\n是否保存为新的配置组？"
                )

                if reply:
                    fourth_level_name = get_text_input(self, "配置名称", "请输入第4级分类名称:")
                    if fourth_level_name:
                        if self.ai_manager.save_analysis_result(result, fourth_level_name):
                            # 构建完整路径用于显示
                            full_path = f"政府机构/人大系统/AI分析/{fourth_level_name}"

                            # 获取域名用于显示
                            from urllib.parse import urlparse
                            domain = urlparse(result.get('list_url', '')).netloc

                            show_info_message(self, "成功",
                                f"配置已保存\n"
                                f"路径: {full_path}\n"
                                f"选择器已保存到txt文件\n"
                                f"文件命名: {full_path.replace('/', '_')}_{domain.replace('.', '_')}_时间戳.txt")

                            # 智能刷新配置组列表
                            self.refresh_current_category_configs()
                        else:
                            show_error_message(self, "错误", "保存配置失败")

                show_info_message(self, "成功", "AI分析结果已应用到表单")
            else:
                show_error_message(self, "错误", "应用AI分析结果失败")
        else:
            self.ai_progress_label.setText("AI分析失败")
            show_error_message(self, "错误", f"AI分析失败: {result.get('error', '未知错误')}")

    def on_ai_error(self, error_message):
        """AI分析错误回调"""
        self.ai_analyze_button.setEnabled(True)
        self.ai_progress_label.setText("AI分析失败")
        show_error_message(self, "错误", f"AI分析失败: {error_message}")

    def log_message(self, message):
        """添加日志消息"""
        # 如果日志组件还没有创建，则只打印到控制台
        if not hasattr(self, 'log_text') or self.log_text is None:
            print(f"[GUI] {message}")
            return

        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def create_module_config(self):
        """创建模组配置"""
        # 模组库管理组
        self.create_module_library_group()

        # 模组设置组
        self.create_module_settings_group()

        # 失败URL处理组
        self.create_failed_url_group()

    def create_module_library_group(self):
        """创建模组库管理组"""
        group = QGroupBox("模组库管理")
        layout = QVBoxLayout()

        # 模组库开关
        self.module_enabled = QCheckBox("启用模组配置系统")
        self.module_enabled.setChecked(True)
        self.module_enabled.setToolTip("启用后将根据URL自动选择合适的爬虫配置")
        layout.addWidget(self.module_enabled)

        # 模组列表和管理按钮
        module_list_layout = QHBoxLayout()

        # 左侧：模组列表
        module_list_left = QVBoxLayout()
        module_list_left.addWidget(QLabel("可用模组:"))

        self.module_list = QComboBox()
        self.module_list.setMinimumWidth(200)
        self.load_module_list()
        module_list_left.addWidget(self.module_list)

        # 模组信息显示
        self.module_info = QTextEdit()
        self.module_info.setMaximumHeight(100)
        self.module_info.setPlaceholderText("选择模组查看详细信息...")
        module_list_left.addWidget(self.module_info)

        module_list_layout.addLayout(module_list_left)

        # 右侧：管理按钮（使用简单垂直布局）
        button_layout = QVBoxLayout()
        button_layout.setSpacing(10)

        # 第一行：刷新和添加（水平布局）
        row1_layout = QHBoxLayout()
        self.refresh_modules_btn = QPushButton("刷新模组")
        self.refresh_modules_btn.clicked.connect(self.refresh_module_list)
        self.refresh_modules_btn.setMinimumWidth(100)
        row1_layout.addWidget(self.refresh_modules_btn)

        self.add_module_btn = QPushButton("添加模组")
        self.add_module_btn.clicked.connect(self.add_new_module)
        self.add_module_btn.setMinimumWidth(100)
        row1_layout.addWidget(self.add_module_btn)
        button_layout.addLayout(row1_layout)

        # 第二行：从当前配置创建
        self.create_from_current_btn = QPushButton("从当前配置创建")
        self.create_from_current_btn.clicked.connect(self.create_module_from_current)
        self.create_from_current_btn.setToolTip("根据当前GUI配置参数创建新模组")
        self.create_from_current_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        self.create_from_current_btn.setMinimumWidth(210)
        button_layout.addWidget(self.create_from_current_btn)

        # 第三行：编辑和删除（水平布局）
        row3_layout = QHBoxLayout()
        self.edit_module_btn = QPushButton("编辑模组")
        self.edit_module_btn.clicked.connect(self.edit_current_module)
        self.edit_module_btn.setMinimumWidth(100)
        row3_layout.addWidget(self.edit_module_btn)

        self.delete_module_btn = QPushButton("删除模组")
        self.delete_module_btn.clicked.connect(self.delete_current_module)
        self.delete_module_btn.setMinimumWidth(100)
        row3_layout.addWidget(self.delete_module_btn)
        button_layout.addLayout(row3_layout)

        # 第四行：测试URL匹配
        self.test_url_btn = QPushButton("测试URL匹配")
        self.test_url_btn.clicked.connect(self.test_url_matching)
        self.test_url_btn.setMinimumWidth(210)
        button_layout.addWidget(self.test_url_btn)

        # 添加弹性空间
        button_layout.addStretch()

        module_list_layout.addLayout(button_layout)

        layout.addLayout(module_list_layout)

        # 连接信号
        self.module_list.currentTextChanged.connect(self.on_module_selection_changed)

        group.setLayout(layout)
        self.module_layout.addWidget(group)

    def create_module_settings_group(self):
        """创建模组设置组"""
        group = QGroupBox("模组设置")
        layout = QGridLayout()

        # 模组状态显示
        layout.addWidget(QLabel("模组状态:"), 0, 0)
        self.module_status_label = QLabel("未启用")
        self.module_status_label.setStyleSheet("color: #666;")
        layout.addWidget(self.module_status_label, 0, 1)

        # 匹配优先级
        layout.addWidget(QLabel("匹配优先级:"), 1, 0)
        self.priority_info = QLabel("自动根据模组顺序确定")
        self.priority_info.setStyleSheet("color: #666;")
        layout.addWidget(self.priority_info, 1, 1)

        # 配置文件路径
        layout.addWidget(QLabel("配置文件:"), 2, 0)
        config_file_layout = QHBoxLayout()
        self.config_file_path = QLineEdit("configs/modules/module_configs.json")
        self.config_file_path.setReadOnly(True)
        config_file_layout.addWidget(self.config_file_path)

        self.open_config_btn = QPushButton("打开配置文件")
        self.open_config_btn.clicked.connect(self.open_config_file)
        config_file_layout.addWidget(self.open_config_btn)

        layout.addLayout(config_file_layout, 2, 1)

        group.setLayout(layout)
        self.module_layout.addWidget(group)

    def create_failed_url_group(self):
        """创建失败URL处理组"""
        group = QGroupBox("失败URL处理")
        layout = QGridLayout()

        # 失败文件选择
        layout.addWidget(QLabel("失败文件:"), 0, 0)
        failed_file_layout = QHBoxLayout()
        self.failed_file_path = QLineEdit()
        self.failed_file_path.setPlaceholderText("选择失败的CSV文件...")
        failed_file_layout.addWidget(self.failed_file_path)

        self.browse_failed_btn = QPushButton("浏览")
        self.browse_failed_btn.clicked.connect(self.browse_failed_file)
        failed_file_layout.addWidget(self.browse_failed_btn)

        layout.addLayout(failed_file_layout, 0, 1)

        # 另存为文件选择
        layout.addWidget(QLabel("另存为文件:"), 1, 0)
        save_file_layout = QHBoxLayout()
        self.save_file_path = QLineEdit()
        self.save_file_path.setPlaceholderText("选择保存文件...")
        save_file_layout.addWidget(self.save_file_path)

        self.browse_save_file_btn = QPushButton("另存为")
        self.browse_save_file_btn.clicked.connect(self.browse_save_file)
        save_file_layout.addWidget(self.browse_save_file_btn)

        layout.addLayout(save_file_layout, 1, 1)

        # 重试文件命名
        layout.addWidget(QLabel("重试文件名:"), 2, 0)
        self.retry_filename = QLineEdit()
        self.retry_filename.setPlaceholderText("重试结果文件名（可选）")
        self.retry_filename.setToolTip("留空则使用默认命名")
        layout.addWidget(self.retry_filename, 2, 1)

        # 重试设置（合并为一行）
        retry_settings_layout = QHBoxLayout()

        retry_settings_layout.addWidget(QLabel("重试次数:"))
        self.retry_count = QSpinBox()
        self.retry_count.setRange(1, 10)
        self.retry_count.setValue(3)
        self.retry_count.setMaximumWidth(80)
        retry_settings_layout.addWidget(self.retry_count)

        retry_settings_layout.addWidget(QLabel("重试间隔(秒):"))
        self.retry_interval = QDoubleSpinBox()
        self.retry_interval.setRange(0.1, 10.0)
        self.retry_interval.setValue(1.0)
        self.retry_interval.setSingleStep(0.1)
        self.retry_interval.setMaximumWidth(80)
        retry_settings_layout.addWidget(self.retry_interval)

        retry_settings_layout.addWidget(QLabel("并发数:"))
        self.retry_workers = QSpinBox()
        self.retry_workers.setRange(1, 20)
        self.retry_workers.setValue(5)
        self.retry_workers.setMaximumWidth(80)
        retry_settings_layout.addWidget(self.retry_workers)

        retry_settings_layout.addStretch()

        retry_widget = QWidget()
        retry_widget.setLayout(retry_settings_layout)
        layout.addWidget(retry_widget, 3, 0, 1, 2)

        # 处理按钮
        button_layout = QHBoxLayout()
        self.process_failed_btn = QPushButton("处理失败URL")
        self.process_failed_btn.clicked.connect(self.process_failed_urls)
        button_layout.addWidget(self.process_failed_btn)

        self.stop_failed_btn = QPushButton("停止处理")
        self.stop_failed_btn.clicked.connect(self.stop_failed_processing)
        self.stop_failed_btn.setEnabled(False)
        self.stop_failed_btn.setStyleSheet("QPushButton { background-color: #ff6b6b; color: white; }")
        button_layout.addWidget(self.stop_failed_btn)

        button_widget = QWidget()
        button_widget.setLayout(button_layout)
        layout.addWidget(button_widget, 4, 0, 1, 2)

        group.setLayout(layout)
        self.module_layout.addWidget(group)

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log_message("📝 日志已清空")

    # 模组配置相关方法
    def load_module_list(self):
        """加载模组列表"""
        try:
            import modules
            module_list = modules.list_modules()

            # 检查控件是否存在
            if hasattr(self, 'module_list'):
                self.module_list.clear()
                for module in module_list:
                    self.module_list.addItem(module)

            # 更新模组状态显示
            if hasattr(self, 'module_status_label'):
                if module_list:
                    self.module_status_label.setText(f"已加载 {len(module_list)} 个模组")
                    self.module_status_label.setStyleSheet("color: green;")
                else:
                    self.module_status_label.setText("无可用模组")
                    self.module_status_label.setStyleSheet("color: orange;")

            # 连接选择改变事件
            if hasattr(self, 'module_list') and hasattr(self.module_list, 'currentTextChanged'):
                self.module_list.currentTextChanged.disconnect()  # 先断开之前的连接
                self.module_list.currentTextChanged.connect(self.on_module_selection_changed)

        except ImportError:
            if hasattr(self, 'log_message'):
                self.log_message("⚠️ 模组管理器不可用")
        except Exception as e:
            if hasattr(self, 'log_message'):
                self.log_message(f"❌ 加载模组列表失败: {e}")
            else:
                print(f"❌ 加载模组列表失败: {e}")

    def on_module_selection_changed(self, module_name):
        """模组选择改变事件处理"""
        if not module_name:
            return

        try:
            import modules
            module_info = modules.get_module_info(module_name)

            if module_info and hasattr(self, 'log_message'):
                config = module_info.get('config', {})
                domain_patterns = module_info.get('domain_patterns', [])

                # 显示模组信息
                info_text = f"📋 模组: {module_name}\n"
                info_text += f"   描述: {module_info.get('description', 'N/A')}\n"
                info_text += f"   域名模式: {', '.join(domain_patterns) if domain_patterns else '无'}\n"
                info_text += f"   配置项: {len(config)} 个\n"
                info_text += f"   文件格式: 使用全局配置"

                self.log_message(info_text)

        except Exception as e:
            if hasattr(self, 'log_message'):
                self.log_message(f"❌ 获取模组信息失败: {e}")

    def refresh_module_list(self):
        """刷新模组列表"""
        try:
            from modules.manager import module_manager
            module_manager.load_modules()
            self.load_module_list()
            self.log_message("✅ 模组列表已刷新")
        except Exception as e:
            self.log_message(f"❌ 刷新模组列表失败: {e}")

    def on_module_selection_changed(self, module_name):
        """模组选择改变时的处理"""
        if not module_name:
            return

        try:
            from modules.manager import module_manager
            module_info = module_manager.get_module_info(module_name)

            if module_info:
                info_text = f"模组名称: {module_info.get('name', module_name)}\n"
                info_text += f"描述: {module_info.get('description', '无描述')}\n"
                info_text += f"域名模式: {', '.join(module_info.get('domain_patterns', []))}\n"
                info_text += f"URL模式: {', '.join(module_info.get('url_patterns', []))}"

                config = module_info.get('config', {})
                if config:
                    info_text += f"\n爬取模式: {config.get('mode', 'balance')}"
                    info_text += f"\n重试次数: {config.get('retry', 2)}"

                self.module_info.setText(info_text)
            else:
                self.module_info.setText("无法获取模组信息")

        except Exception as e:
            self.module_info.setText(f"获取模组信息失败: {e}")

    def add_new_module(self):
        """添加新模组"""
        dialog = ModuleEditDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_module_list()

    def create_module_from_current(self):
        """从当前配置创建模组"""
        try:
            # 获取当前GUI配置
            current_config = self.get_config_from_gui()

            # 创建模组配置对话框，并预填充当前配置
            dialog = ModuleEditDialog(self)
            dialog.load_from_gui_config(current_config)

            if dialog.exec_() == QDialog.Accepted:
                self.refresh_module_list()
                self.log_message("✅ 已从当前配置创建新模组")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"创建模组失败: {e}")
            self.log_message(f"❌ 创建模组失败: {e}")

    def edit_current_module(self):
        """编辑当前模组"""
        current_module = self.module_list.currentText()
        if not current_module:
            QMessageBox.warning(self, "警告", "请先选择要编辑的模组")
            return

        dialog = ModuleEditDialog(self, current_module)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_module_list()

    def delete_current_module(self):
        """删除当前模组"""
        current_module = self.module_list.currentText()
        if not current_module:
            QMessageBox.warning(self, "警告", "请先选择要删除的模组")
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除模组 '{current_module}' 吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                from modules.manager import module_manager
                if module_manager.delete_module(current_module):
                    module_manager.save_modules()
                    self.refresh_module_list()
                    self.log_message(f"✅ 已删除模组: {current_module}")
                else:
                    self.log_message(f"❌ 删除模组失败: {current_module}")
            except Exception as e:
                self.log_message(f"❌ 删除模组时出错: {e}")

    def test_url_matching(self):
        """测试URL匹配"""
        dialog = UrlTestDialog(self)
        dialog.exec_()

    def open_config_file(self):
        """打开配置文件"""
        import os
        import platform
        config_file = self.config_file_path.text()
        if os.path.exists(config_file):
            try:
                system = platform.system()
                if system == "Windows":
                    os.startfile(config_file)
                elif system == "Darwin":  # macOS
                    os.system(f"open '{config_file}'")
                else:  # Linux
                    os.system(f"xdg-open '{config_file}'")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"无法打开配置文件: {e}")
        else:
            QMessageBox.warning(self, "警告", f"配置文件不存在: {config_file}")



    def browse_failed_file(self):
        """浏览失败文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择失败文件", "articles",
            "所有支持的文件 (*.csv *.xlsx);;CSV文件 (*.csv);;Excel文件 (*.xlsx);;所有文件 (*.*)"
        )
        if file_path:
            self.failed_file_path.setText(file_path)

    def browse_save_file(self):
        """另存为文件"""
        # 从配置文件读取默认文件名
        default_filename = self.get_default_export_filename()

        if default_filename:
            default_name = f"articles/{default_filename}_重试结果"
        else:
            default_name = "articles/重试结果"

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "另存为文件",
            default_name,
            "CSV文件 (*.csv);;Excel文件 (*.xlsx);;所有文件 (*.*)"
        )
        if file_path:
            self.save_file_path.setText(file_path)

    def browse_export_file(self):
        """浏览导出文件"""
        current_format = self.file_format_combo.currentText()

        # 从配置文件读取默认文件名
        default_filename = self.get_default_export_filename()

        if current_format == "Excel":
            filter_str = "Excel文件 (*.xlsx);;CSV文件 (*.csv);;所有文件 (*.*)"
            if default_filename:
                default_name = f"articles/{default_filename}.xlsx"
            else:
                default_name = "articles/文章数据.xlsx"
        else:
            filter_str = "CSV文件 (*.csv);;Excel文件 (*.xlsx);;所有文件 (*.*)"
            if default_filename:
                default_name = f"articles/{default_filename}.csv"
            else:
                default_name = "articles/文章数据.csv"

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择导出文件",
            default_name,
            filter_str
        )
        if file_path:
            self.export_filename_edit.setText(file_path)

            # 根据选择的文件类型自动设置文件格式
            if file_path.lower().endswith('.xlsx'):
                self.file_format_combo.setCurrentText("Excel")
            elif file_path.lower().endswith('.csv'):
                self.file_format_combo.setCurrentText("CSV")

    def get_default_export_filename(self):
        """从配置文件获取默认导出文件名，自动生成"3层_4层文件名"格式"""
        try:
            # 首先尝试从当前输入框获取
            current_filename = self.export_filename_edit.text().strip()
            if current_filename:
                filename_only = os.path.basename(current_filename)
                filename_without_ext = os.path.splitext(filename_only)[0]
                return filename_without_ext

            # 获取当前配置组的数据
            current_group = self.config_combo.currentText()
            if current_group:
                config_data = self.config_manager.get_group(current_group)
                if config_data:
                    export_filename = config_data.get('export_filename', '')
                    if export_filename:
                        # 提取文件名（去除路径）
                        filename_only = os.path.basename(export_filename)
                        # 去除扩展名，避免重复后缀
                        filename_without_ext = os.path.splitext(filename_only)[0]
                        return filename_without_ext

            # 如果没有配置，根据当前4级分类路径自动生成文件名
            return self.generate_filename_from_category_path()

        except Exception as e:
            self.log_message(f"获取默认文件名失败: {e}")
            return ""

    def generate_filename_from_category_path(self):
        """根据当前4级分类路径生成文件名：3层分类_配置组格式"""
        try:
            parent = self.parent_category_combo.currentText()
            sub = self.sub_category_combo.currentText()
            third = self.child_category_combo.currentText()
            current_config = self.config_combo.currentText()

            if parent and sub and third and current_config:
                # 生成"3层分类_配置组"格式的文件名
                filename = f"{third}_{current_config}"
                return filename

            # 如果没有完整的分类信息，使用配置组名称
            if current_config:
                return current_config

            return "文章数据"
        except Exception as e:
            print(f"生成文件名失败: {e}")
            return "文章数据"

    def stop_failed_processing(self):
        """停止失败URL处理"""
        self.failed_processing_stop_flag = True
        self.log_message("🛑 正在停止失败URL处理...")

        # 禁用停止按钮，启用处理按钮
        self.stop_failed_btn.setEnabled(False)
        self.process_failed_btn.setEnabled(True)
        self.process_failed_btn.setText("处理失败URL")

        # 隐藏进度条
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setVisible(False)

    def process_failed_urls(self):
        """处理失败URL"""
        failed_file = self.failed_file_path.text().strip()
        if not failed_file:
            QMessageBox.warning(self, "警告", "请先选择失败文件")
            return

        if not os.path.exists(failed_file):
            QMessageBox.warning(self, "警告", f"文件不存在: {failed_file}")
            return

        # 重置停止标志
        self.failed_processing_stop_flag = False

        # 更新按钮状态
        self.process_failed_btn.setEnabled(False)
        self.process_failed_btn.setText("处理中...")
        self.stop_failed_btn.setEnabled(True)

        # 显示进度条
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

        # 检查模组系统是否启用
        if not self.module_enabled.isChecked():
            reply = QMessageBox.question(
                self, "确认",
                "模组配置系统未启用，将使用默认配置处理。是否继续？",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply != QMessageBox.Yes:
                return

        try:
            from core.failed_url_processor import process_failed_csv

            # 获取保存文件路径
            save_file = self.save_file_path.text().strip()
            if save_file:
                # 使用指定的文件路径
                save_dir = os.path.dirname(save_file)
                retry_filename = os.path.basename(save_file)
                # 去掉扩展名
                if retry_filename.endswith('.csv') or retry_filename.endswith('.xlsx'):
                    retry_filename = os.path.splitext(retry_filename)[0]
            else:
                # 使用默认设置
                save_dir = "articles"
                retry_filename = self.retry_filename.text().strip() or None

            # 获取参数
            retry_count = self.retry_count.value()
            retry_interval = self.retry_interval.value()
            max_workers = self.retry_workers.value()

            # 确定文件格式
            file_format = "CSV"
            if save_file and save_file.lower().endswith('.xlsx'):
                file_format = "Excel"

            self.log_message(f"🔄 开始处理失败URL文件: {failed_file}")
            self.log_message(f"💾 保存文件: {save_file or '默认位置'}")
            self.log_message(f"📁 保存目录: {save_dir}")
            self.log_message(f"📝 文件名: {retry_filename or '自动生成'}")
            self.log_message(f"📄 文件格式: {file_format}")
            self.log_message(f"🔁 重试设置: {retry_count}次, 间隔{retry_interval}秒, {max_workers}个线程")

            # 在新线程中处理，避免阻塞UI
            import threading

            def progress_callback(current, total):
                """进度回调"""
                if hasattr(self, 'progress_bar'):
                    progress = int((current / total) * 100)
                    QTimer.singleShot(0, lambda: self.progress_bar.setValue(progress))

            def log_callback(message):
                """日志回调"""
                QTimer.singleShot(0, lambda: self.log_message(message))

            def stop_check():
                """停止检查"""
                return self.failed_processing_stop_flag

            def process_thread():
                try:
                    # 使用异步方式处理失败URL
                    import asyncio
                    from core.failed_url_processor import process_failed_csv_via_crawler

                    async def async_process():
                        return await process_failed_csv_via_crawler(
                            failed_csv_path=failed_file,
                            save_dir=save_dir,
                            export_filename=retry_filename,
                            file_format=file_format,
                            classid="",
                            retry=retry_count,
                            interval=retry_interval,
                            max_workers=max_workers,
                            progress_callback=progress_callback,
                            log_callback=log_callback,
                            stop_flag=stop_check,
                            mode="balance"  # 使用平衡模式
                        )

                    # 在新的事件循环中运行异步函数
                    result = asyncio.run(async_process())

                    # 在主线程中更新UI
                    QTimer.singleShot(0, lambda: self.on_failed_url_processed(result))

                except Exception as e:
                    import traceback
                    error_msg = f"❌ 处理失败URL时出错: {e}\n{traceback.format_exc()}"
                    QTimer.singleShot(0, lambda: self.log_message(error_msg))

            thread = threading.Thread(target=process_thread)
            thread.daemon = True
            thread.start()

        except ImportError:
            QMessageBox.warning(self, "错误", "失败URL处理器不可用，请检查相关模块")
        except Exception as e:
            self.log_message(f"❌ 启动失败URL处理时出错: {e}")

    def on_failed_url_processed(self, result):
        """失败URL处理完成回调"""
        # 重置按钮状态
        self.stop_failed_btn.setEnabled(False)
        self.process_failed_btn.setEnabled(True)
        self.process_failed_btn.setText("处理失败URL")
        self.failed_processing_stop_flag = False

        # 隐藏进度条
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setVisible(False)

        total = result.get('total', 0)
        success = result.get('success', 0)
        failed = result.get('failed', 0)
        stopped = result.get('stopped', False)
        success_rate = (success / total * 100) if total > 0 else 0

        if stopped:
            self.log_message(f"🛑 失败URL处理已停止!")
            self.log_message(f"📊 已处理: {success + failed}/{total}, 成功: {success}, 失败: {failed}")
        else:
            self.log_message(f"✅ 失败URL处理完成!")
            self.log_message(f"📊 总计: {total}, 成功: {success}, 失败: {failed}")

        self.log_message(f"📈 成功率: {success_rate:.1f}%")

        QMessageBox.information(
            self, "处理完成",
            f"失败URL处理完成!\n\n"
            f"总计: {total}\n"
            f"成功: {success}\n"
            f"失败: {failed}\n"
            f"成功率: {success_rate:.1f}%"
        )
















class ModuleEditDialog(QDialog):
    """模组编辑对话框"""

    def __init__(self, parent=None, module_name=None):
        super().__init__(parent)
        self.module_name = module_name
        self.is_edit_mode = module_name is not None

        self.setWindowTitle("编辑模组" if self.is_edit_mode else "添加模组")
        self.setModal(True)
        self.resize(600, 500)

        self.setup_ui()

        if self.is_edit_mode:
            self.load_module_data()

    def load_from_gui_config(self, gui_config):
        """从GUI配置加载数据"""
        try:
            # 获取当前配置组名称作为模组名称
            current_config_group = getattr(self.parent(), 'config_combo', None)
            if current_config_group and hasattr(current_config_group, 'currentText'):
                config_group_name = current_config_group.currentText()
                if config_group_name and config_group_name != 'default':
                    self.name_edit.setText(config_group_name)
                    self.description_edit.setText(f"从配置组 '{config_group_name}' 创建的模组")
                else:
                    # 如果没有配置组名称，使用域名
                    self._set_name_from_domain(gui_config)
            else:
                self._set_name_from_domain(gui_config)

            # 尝试从input_url提取域名和生成智能正则表达式
            input_url = gui_config.get('input_url', '')
            if input_url:
                try:
                    domain_suggestion, url_pattern_suggestion = self._generate_smart_patterns(input_url)

                    # 设置域名模式
                    self.domain_patterns_edit.setPlaceholderText(f"建议域名: {domain_suggestion}")
                    self.domain_patterns_edit.setText(domain_suggestion)

                    # 设置智能URL模式
                    self.url_patterns_edit.setPlaceholderText(f"智能模式: {url_pattern_suggestion}")
                    self.url_patterns_edit.setText(url_pattern_suggestion)
                except Exception as e:
                    # 如果解析失败，使用默认提示
                    self.domain_patterns_edit.setPlaceholderText("请输入域名模式，例如：news.sina.com.cn")
                    self.url_patterns_edit.setPlaceholderText("请输入URL模式，例如：.*news\\.sina\\.com\\.cn.*")
            else:
                # URL匹配（需要用户输入）
                self.domain_patterns_edit.setPlaceholderText("请输入域名模式，例如：news.sina.com.cn")
                self.url_patterns_edit.setPlaceholderText("请输入URL模式，例如：.*news\\.sina\\.com\\.cn.*")

            # 选择器配置（从GUI获取）
            # 处理多选择器格式 - 将列表转换为逗号分隔的字符串
            content_selectors = gui_config.get('content_selectors', [])
            title_selectors = gui_config.get('title_selectors', [])
            date_selectors = gui_config.get('date_selectors', [])
            source_selectors = gui_config.get('source_selectors', [])

            # 转换为字符串格式
            if isinstance(content_selectors, list):
                content_selectors = ', '.join(content_selectors)
            if isinstance(title_selectors, list):
                title_selectors = ', '.join(title_selectors)
            if isinstance(date_selectors, list):
                date_selectors = ', '.join(date_selectors)
            if isinstance(source_selectors, list):
                source_selectors = ', '.join(source_selectors)

            self.content_selectors_edit.setText(content_selectors)
            self.title_selectors_edit.setText(title_selectors)
            self.date_selectors_edit.setText(date_selectors)
            self.source_selectors_edit.setText(source_selectors)

            # 选择器类型（仅保留内容类型，其他类型参数已删除）
            self.content_type_combo.setCurrentText(gui_config.get('content_type', 'CSS'))

            # 爬取设置
            self.mode_combo.setCurrentText(gui_config.get('mode', 'balance'))
            self.collect_links_check.setChecked(gui_config.get('collect_links', True))
            self.retry_spin.setValue(gui_config.get('retry', 2))
            self.interval_spin.setValue(gui_config.get('interval', 0))
            self.max_workers_spin.setValue(gui_config.get('max_workers', 5))

            # 浏览器设置
            self.headless_check.setChecked(gui_config.get('headless', True))

            # 页面加载策略
            page_load_strategy = gui_config.get('page_load_strategy', 'eager')
            if hasattr(self, 'page_load_strategy_combo'):
                self.page_load_strategy_combo.setCurrentText(page_load_strategy)

            # 文件设置 - 模组配置不再包含文件格式，使用全局配置
            # self.file_format_combo.setCurrentText(gui_config.get('file_format', 'CSV'))
            # 文件格式将使用全局配置

            # 提示用户
            QMessageBox.information(
                self,
                "配置已加载",
                "当前GUI配置已加载到模组编辑器中。\n\n"
                "请填写：\n"
                "• 模组名称和描述\n"
                "• 域名模式和URL模式\n\n"
                "其他配置已自动填充。"
            )

        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载GUI配置失败: {e}")

    def _set_name_from_domain(self, gui_config):
        """从域名设置模组名称"""
        input_url = gui_config.get('input_url', '')
        if input_url:
            try:
                from urllib.parse import urlparse
                parsed = urlparse(input_url)
                if parsed.netloc:
                    # 使用域名作为名称
                    domain_name = parsed.netloc.replace('www.', '').replace('.com', '').replace('.cn', '').replace('.org', '')
                    self.name_edit.setText(domain_name)
                    self.description_edit.setText(f"从域名 '{parsed.netloc}' 创建的模组")
            except:
                pass

    def _generate_smart_patterns(self, input_url):
        """生成智能的域名和URL模式"""
        from urllib.parse import urlparse
        import re

        parsed = urlparse(input_url)
        domain = parsed.netloc
        path = parsed.path

        # 域名模式
        domain_pattern = domain

        # 智能URL模式生成
        if path and path != '/':
            # 分析路径结构
            path_parts = [part for part in path.split('/') if part]

            # 检测数字模式（如 n8347, 123456 等）
            processed_parts = []
            for part in path_parts:
                if re.match(r'^n\d+$', part):
                    # n数字模式，替换为通用模式
                    processed_parts.append(r'n\d+')
                elif re.match(r'^\d+$', part):
                    # 纯数字模式，替换为通用模式
                    processed_parts.append(r'\d+')
                elif re.match(r'^[a-zA-Z]+\d+$', part):
                    # 字母+数字模式，提取字母部分
                    letter_part = re.match(r'^([a-zA-Z]+)', part).group(1)
                    processed_parts.append(f'{letter_part}' + r'\d+')
                elif re.match(r'^[a-zA-Z0-9]*[a-zA-Z]+\d+[a-zA-Z0-9]*$', part):
                    # 复杂的字母数字组合，如 u1ai263916
                    # 提取模式并生成通用匹配
                    if 'ai' in part and re.search(r'\d+', part):
                        # 特殊处理类似 u1ai263916 的模式
                        processed_parts.append('[a-zA-Z0-9]+')
                    else:
                        processed_parts.append(re.escape(part))
                else:
                    # 普通路径部分，保持不变
                    processed_parts.append(re.escape(part))

            # 构建URL模式
            if processed_parts:
                path_pattern = '/' + '/'.join(processed_parts)
                # 检查是否以/结尾，如果是则添加可选的文件名匹配
                if input_url.endswith('/'):
                    url_pattern = f".*{re.escape(domain)}{path_pattern}/.*"
                else:
                    # 如果不以/结尾，可能是文件，添加可选的文件名模式
                    url_pattern = f".*{re.escape(domain)}{path_pattern}(?:/[^/]*)?.*"
            else:
                url_pattern = f".*{re.escape(domain)}.*"
        else:
            # 没有路径，使用通用域名模式
            url_pattern = f".*{re.escape(domain)}.*"

        return domain_pattern, url_pattern

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout()

        # 基本信息
        basic_group = QGroupBox("基本信息")
        basic_layout = QGridLayout()

        basic_layout.addWidget(QLabel("模组名称:"), 0, 0)
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入模组名称")
        basic_layout.addWidget(self.name_edit, 0, 1)

        basic_layout.addWidget(QLabel("描述:"), 1, 0)
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("输入模组描述")
        basic_layout.addWidget(self.description_edit, 1, 1)

        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)

        # 匹配规则
        match_group = QGroupBox("匹配规则")
        match_layout = QGridLayout()

        match_layout.addWidget(QLabel("域名模式:"), 0, 0)
        self.domain_patterns_edit = QLineEdit()
        self.domain_patterns_edit.setPlaceholderText("用逗号分隔，如: mp.weixin.qq.com, *.gov.cn")
        match_layout.addWidget(self.domain_patterns_edit, 0, 1)

        match_layout.addWidget(QLabel("URL模式:"), 1, 0)
        self.url_patterns_edit = QLineEdit()
        self.url_patterns_edit.setPlaceholderText("正则表达式，用逗号分隔")
        match_layout.addWidget(self.url_patterns_edit, 1, 1)

        match_group.setLayout(match_layout)
        layout.addWidget(match_group)

        # 选择器配置
        selector_group = QGroupBox("选择器配置")
        selector_layout = QGridLayout()

        selector_layout.addWidget(QLabel("标题选择器:"), 0, 0)
        self.title_selectors_edit = QLineEdit()
        self.title_selectors_edit.setPlaceholderText("用逗号分隔，如: h1, .title, #article-title")
        selector_layout.addWidget(self.title_selectors_edit, 0, 1)

        # 删除标题类型选择器（已统一处理）

        selector_layout.addWidget(QLabel("内容选择器:"), 1, 0)
        self.content_selectors_edit = QLineEdit()
        self.content_selectors_edit.setPlaceholderText("用逗号分隔，如: .content, #article-content")
        selector_layout.addWidget(self.content_selectors_edit, 1, 1)

        selector_layout.addWidget(QLabel("内容类型:"), 1, 2)
        self.content_type_combo = QComboBox()
        self.content_type_combo.addItems(["CSS", "XPath"])
        selector_layout.addWidget(self.content_type_combo, 1, 3)

        selector_layout.addWidget(QLabel("日期选择器:"), 2, 0)
        self.date_selectors_edit = QLineEdit()
        self.date_selectors_edit.setPlaceholderText("用逗号分隔，如: .date, .publish-time")
        selector_layout.addWidget(self.date_selectors_edit, 2, 1)

        # 删除日期类型选择器（已统一处理）

        selector_layout.addWidget(QLabel("来源选择器:"), 3, 0)
        self.source_selectors_edit = QLineEdit()
        self.source_selectors_edit.setPlaceholderText("用逗号分隔，如: .source, .author")
        selector_layout.addWidget(self.source_selectors_edit, 3, 1)

        # 删除来源类型选择器（已统一处理）

        selector_group.setLayout(selector_layout)
        layout.addWidget(selector_group)

        # 爬取设置
        settings_group = QGroupBox("爬取设置")
        settings_layout = QGridLayout()

        settings_layout.addWidget(QLabel("爬取模式:"), 0, 0)
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["balance", "safe", "fast"])
        settings_layout.addWidget(self.mode_combo, 0, 1)

        settings_layout.addWidget(QLabel("重试次数:"), 1, 0)
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(1, 10)
        self.retry_spin.setValue(2)
        settings_layout.addWidget(self.retry_spin, 1, 1)

        settings_layout.addWidget(QLabel("间隔时间(秒):"), 2, 0)
        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(0.1, 10.0)
        self.interval_spin.setValue(0.5)
        self.interval_spin.setSingleStep(0.1)
        settings_layout.addWidget(self.interval_spin, 2, 1)

        settings_layout.addWidget(QLabel("最大工作线程:"), 3, 0)
        self.max_workers_spin = QSpinBox()
        self.max_workers_spin.setRange(1, 20)
        self.max_workers_spin.setValue(5)
        settings_layout.addWidget(self.max_workers_spin, 3, 1)

        settings_layout.addWidget(QLabel("收集链接:"), 4, 0)
        self.collect_links_check = QCheckBox()
        self.collect_links_check.setChecked(True)
        settings_layout.addWidget(self.collect_links_check, 4, 1)

        # 文件格式说明（不再在模组中设置）
        file_format_note = QLabel("文件格式: 使用全局配置")
        file_format_note.setStyleSheet("color: #666; font-style: italic;")
        settings_layout.addWidget(file_format_note, 5, 0, 1, 2)

        settings_layout.addWidget(QLabel("无头模式:"), 6, 0)
        self.headless_check = QCheckBox()
        self.headless_check.setChecked(True)
        settings_layout.addWidget(self.headless_check, 6, 1)

        settings_layout.addWidget(QLabel("页面加载策略:"), 7, 0)
        self.page_load_strategy_combo = QComboBox()
        self.page_load_strategy_combo.addItems(["eager", "normal", "none"])
        settings_layout.addWidget(self.page_load_strategy_combo, 7, 1)

        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_module)
        button_layout.addWidget(self.save_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def load_module_data(self):
        """加载模组数据"""
        try:
            import modules
            module_info = modules.get_module_info(self.module_name)

            if module_info:
                self.name_edit.setText(module_info.get('name', ''))
                self.name_edit.setReadOnly(True)  # 编辑模式下不允许修改名称

                self.description_edit.setText(module_info.get('description', ''))

                domain_patterns = module_info.get('domain_patterns', [])
                self.domain_patterns_edit.setText(', '.join(domain_patterns))

                url_patterns = module_info.get('url_patterns', [])
                self.url_patterns_edit.setText(', '.join(url_patterns))

                config = module_info.get('config', {})
                if config:
                    title_selectors = config.get('title_selectors', [])
                    self.title_selectors_edit.setText(', '.join(title_selectors))

                    content_selectors = config.get('content_selectors', [])
                    self.content_selectors_edit.setText(', '.join(content_selectors))

                    date_selectors = config.get('date_selectors', [])
                    self.date_selectors_edit.setText(', '.join(date_selectors))

                    source_selectors = config.get('source_selectors', [])
                    self.source_selectors_edit.setText(', '.join(source_selectors))

                    mode = config.get('mode', 'balance')
                    index = self.mode_combo.findText(mode)
                    if index >= 0:
                        self.mode_combo.setCurrentIndex(index)

                    self.retry_spin.setValue(config.get('retry', 2))
                    self.interval_spin.setValue(config.get('interval', 0.5))
                    self.max_workers_spin.setValue(config.get('max_workers', 5))

                    # 加载新增字段
                    self.headless_check.setChecked(config.get('headless', True))

                    page_load_strategy = config.get('page_load_strategy', 'eager')
                    index = self.page_load_strategy_combo.findText(page_load_strategy)
                    if index >= 0:
                        self.page_load_strategy_combo.setCurrentIndex(index)

                    # 文件格式不再在模组配置中设置，使用全局配置
                    # file_format = config.get('file_format', 'CSV')
                    # 模组配置中不再包含文件格式设置

        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载模组数据失败: {e}")

    def save_module(self):
        """保存模组"""
        # 验证输入
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "错误", "模组名称不能为空")
            return

        # 构建模组配置
        module_config = {
            "name": name,
            "description": self.description_edit.text().strip() or "从当前配置创建的模组",
            "domain_patterns": [p.strip() for p in self.domain_patterns_edit.text().split(',') if p.strip()],
            "url_patterns": [p.strip() for p in self.url_patterns_edit.text().split(',') if p.strip()],
            "config": {
                "title_selectors": [s.strip() for s in self.title_selectors_edit.text().split(',') if s.strip()],
                "content_selectors": [s.strip() for s in self.content_selectors_edit.text().split(',') if s.strip()],
                "content_type": self.content_type_combo.currentText(),
                "date_selectors": [s.strip() for s in self.date_selectors_edit.text().split(',') if s.strip()],
                "source_selectors": [s.strip() for s in self.source_selectors_edit.text().split(',') if s.strip()],
                "mode": self.mode_combo.currentText(),
                "collect_links": self.collect_links_check.isChecked(),
                "retry": self.retry_spin.value(),
                "interval": self.interval_spin.value(),
                "max_workers": self.max_workers_spin.value(),
                "headless": getattr(self, 'headless_check', None) and self.headless_check.isChecked(),
                "page_load_strategy": getattr(self, 'page_load_strategy_combo', None) and self.page_load_strategy_combo.currentText() or "eager"
                # 注意：不再保存file_format，使用全局配置
            }
        }

        try:
            import modules

            if self.is_edit_mode:
                success = modules.update_module(name, module_config)
            else:
                success = modules.add_module(name, module_config)

            if success:
                modules.save_modules()
                QMessageBox.information(self, "成功", f"模组 '{name}' 已保存")
                self.accept()
            else:
                QMessageBox.warning(self, "错误", f"保存模组 '{name}' 失败")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存模组时出错: {e}")


class UrlTestDialog(QDialog):
    """URL测试对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("URL匹配测试")
        self.setModal(True)
        self.resize(500, 400)

        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout()

        # URL输入
        url_group = QGroupBox("测试URL")
        url_layout = QVBoxLayout()

        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("输入要测试的URL")
        url_layout.addWidget(self.url_edit)

        self.test_btn = QPushButton("测试匹配")
        self.test_btn.clicked.connect(self.test_url)
        url_layout.addWidget(self.test_btn)

        url_group.setLayout(url_layout)
        layout.addWidget(url_group)

        # 结果显示
        result_group = QGroupBox("匹配结果")
        result_layout = QVBoxLayout()

        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)

        result_group.setLayout(result_layout)
        layout.addWidget(result_group)

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

        self.setLayout(layout)

    def test_url(self):
        """测试URL匹配"""
        url = self.url_edit.text().strip()
        if not url:
            QMessageBox.warning(self, "警告", "请输入URL")
            return

        try:
            from modules.manager import match_module_for_url, get_config_for_url

            # 匹配模组
            module_name = match_module_for_url(url)

            result_text = f"测试URL: {url}\n"
            result_text += "=" * 50 + "\n\n"

            if module_name:
                result_text += f"匹配模组: {module_name}\n\n"

                # 获取配置
                config = get_config_for_url(url)
                if config:
                    result_text += "配置详情:\n"
                    result_text += "-" * 30 + "\n"
                    result_text += f"标题选择器: {config.get('title_selectors', [])}\n"
                    result_text += f"内容选择器: {config.get('content_selectors', [])}\n"
                    result_text += f"日期选择器: {config.get('date_selectors', [])}\n"
                    result_text += f"来源选择器: {config.get('source_selectors', [])}\n"
                    result_text += f"爬取模式: {config.get('mode', 'balance')}\n"
                    result_text += f"重试次数: {config.get('retry', 2)}\n"
                    result_text += f"间隔时间: {config.get('interval', 0.5)}秒\n"
                else:
                    result_text += "未获取到配置信息\n"
            else:
                result_text += "未匹配到任何模组\n"

            self.result_text.setText(result_text)

        except ImportError:
            QMessageBox.warning(self, "错误", "模组管理器不可用")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"测试URL时出错: {e}")



def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("HILLSUN's文章智能采集器")
    app.setApplicationVersion("0.2.4")
    app.setOrganizationName("HILLSUN")

    # 创建并显示主窗口
    window = CrawlerGUI()
    window.show()

    # 启动应用
    sys.exit(app.exec_())


def show_question_message(parent, title, message):
    """显示询问对话框"""
    from PyQt5.QtWidgets import QMessageBox
    reply = QMessageBox.question(parent, title, message,
                                QMessageBox.Yes | QMessageBox.No,
                                QMessageBox.No)
    return reply == QMessageBox.Yes


def get_text_input(parent, title, prompt, default_text=""):
    """获取文本输入"""
    from PyQt5.QtWidgets import QInputDialog
    text, ok = QInputDialog.getText(parent, title, prompt, text=default_text)
    return text if ok else None


class CustomFieldDialog(QDialog):
    """自定义字段对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("自定义字段")
        self.setModal(True)
        self.resize(500, 400)

        # 存储字段行
        self.field_rows = []

        try:
            self.setup_ui()
        except Exception as e:
            import traceback
            print(f"CustomFieldDialog初始化失败: {e}")
            print(f"错误详情: {traceback.format_exc()}")
            raise

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout()

        # 说明文本
        info_label = QLabel("""
<b>📝 自定义字段说明：</b><br>
• <b>字段名</b>：自定义的字段名称（如：author, category, tags等）<br>
• <b>字段值</b>：该字段的固定值（如：管理员, 新闻, 重要等）<br>
• 这些字段会在爬取时自动添加到每条数据中<br>
• 类似于classid字段的使用方式，为每条记录添加固定的元数据<br>
• 支持中文字段名，建议使用英文字段名以便数据处理
        """)
        info_label.setStyleSheet("""
            color: #444;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            font-size: 13px;
            line-height: 1.4;
        """)
        layout.addWidget(info_label)

        # 字段列表区域
        self.fields_group = QGroupBox("自定义字段列表")
        self.fields_layout = QVBoxLayout()

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(200)

        scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout()
        scroll_widget.setLayout(self.scroll_layout)
        scroll_area.setWidget(scroll_widget)

        self.fields_layout.addWidget(scroll_area)

        # 添加字段按钮
        add_button = QPushButton("+ 添加字段")
        add_button.clicked.connect(self.add_field_row)
        self.fields_layout.addWidget(add_button)

        self.fields_group.setLayout(self.fields_layout)
        layout.addWidget(self.fields_group)

        # 预设字段快速添加
        presets_group = QGroupBox("🚀 常用字段快速添加")
        presets_layout = QVBoxLayout()

        # 说明文字
        preset_info = QLabel("点击下方按钮快速添加常用字段：")
        preset_info.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 5px;")
        presets_layout.addWidget(preset_info)

        preset_buttons_layout = QHBoxLayout()

        # 常用字段预设
        presets = [
            ("👤 作者", "author", "管理员"),
            ("📂 分类", "category", "新闻"),
            ("🏷️ 标签", "tags", "重要"),
            ("🏢 来源", "source_type", "官方"),
            ("✅ 状态", "status", "已发布")
        ]

        for display_name, field_name, default_value in presets:
            btn = QPushButton(f"{display_name}")
            btn.setToolTip(f"添加字段: {field_name} = {default_value}")
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #0056b3;
                }
                QPushButton:pressed {
                    background-color: #004085;
                }
            """)
            btn.clicked.connect(lambda checked=False, fn=field_name, dv=default_value: self.add_preset_field(fn, dv))
            preset_buttons_layout.addWidget(btn)

        presets_layout.addLayout(preset_buttons_layout)
        presets_group.setLayout(presets_layout)
        layout.addWidget(presets_group)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 清空所有
        clear_button = QPushButton("🗑️ 清空所有")
        clear_button.setToolTip("清空所有字段行")
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        clear_button.clicked.connect(self.clear_all_fields)
        button_layout.addWidget(clear_button)

        button_layout.addStretch()

        # 确定和取消按钮
        ok_button = QPushButton("✅ 确定")
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 20px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)

        cancel_button = QPushButton("❌ 取消")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 20px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

        self.setLayout(layout)

        # 添加一个默认的字段行
        self.add_field_row()

    def add_field_row(self, field_name="", field_value=""):
        """添加字段行"""
        try:
            row_widget = QWidget()
            row_layout = QHBoxLayout()
            row_layout.setContentsMargins(0, 0, 0, 0)

            # 字段名输入
            name_label = QLabel("字段名:")
            name_label.setMinimumWidth(60)
            name_input = QLineEdit()
            name_input.setPlaceholderText("如: author, category, tags")
            name_input.setText(str(field_name) if field_name else "")
            name_input.setMaxLength(50)  # 限制字段名长度

            # 添加字段名验证
            def validate_field_name():
                text = name_input.text().strip()
                if text and not text.replace('_', '').replace('-', '').isalnum():
                    name_input.setStyleSheet("border: 2px solid #dc3545; background-color: #fff5f5;")
                    name_input.setToolTip("字段名只能包含字母、数字、下划线和连字符")
                else:
                    name_input.setStyleSheet("")
                    name_input.setToolTip("")

            name_input.textChanged.connect(validate_field_name)

            # 字段值输入
            value_label = QLabel("字段值:")
            value_label.setMinimumWidth(60)
            value_input = QLineEdit()
            value_input.setPlaceholderText("如: 管理员, 新闻, 重要")
            value_input.setText(str(field_value) if field_value else "")
            value_input.setMaxLength(200)  # 限制字段值长度

            # 删除按钮
            delete_button = QPushButton("🗑️")
            delete_button.setMaximumWidth(40)
            delete_button.setMaximumHeight(30)
            delete_button.setToolTip("删除此字段")
            delete_button.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
                QPushButton:pressed {
                    background-color: #bd2130;
                }
            """)
            delete_button.clicked.connect(lambda: self.remove_field_row(row_widget))

            # 布局设置
            row_layout.addWidget(name_label)
            row_layout.addWidget(name_input, 2)  # 字段名占2份
            row_layout.addWidget(value_label)
            row_layout.addWidget(value_input, 3)  # 字段值占3份
            row_layout.addWidget(delete_button)

            row_widget.setLayout(row_layout)

            # 设置行样式
            row_widget.setStyleSheet("""
                QWidget {
                    background-color: #ffffff;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    margin: 2px 0;
                    padding: 5px;
                }
                QWidget:hover {
                    border-color: #007bff;
                    background-color: #f8f9fa;
                }
            """)

            # 存储字段行信息
            field_row = {
                'widget': row_widget,
                'name_input': name_input,
                'value_input': value_input
            }
            self.field_rows.append(field_row)

            # 添加到布局
            self.scroll_layout.addWidget(row_widget)

        except Exception as e:
            import traceback
            print(f"添加字段行失败: {e}")
            print(f"错误详情: {traceback.format_exc()}")
            raise

    def add_preset_field(self, field_name, field_value):
        """添加预设字段"""
        self.add_field_row(field_name, field_value)

    def remove_field_row(self, row_widget):
        """删除字段行"""
        # 从列表中移除
        self.field_rows = [row for row in self.field_rows if row['widget'] != row_widget]

        # 从布局中移除
        self.scroll_layout.removeWidget(row_widget)
        row_widget.deleteLater()

    def clear_all_fields(self):
        """清空所有字段"""
        for field_row in self.field_rows[:]:  # 使用切片复制列表
            self.remove_field_row(field_row['widget'])

        # 添加一个空的字段行
        self.add_field_row()

    def get_custom_fields(self):
        """获取自定义字段"""
        custom_fields = {}
        invalid_fields = []

        for i, field_row in enumerate(self.field_rows):
            field_name = field_row['name_input'].text().strip()
            field_value = field_row['value_input'].text().strip()

            if field_name and field_value:
                # 验证字段名格式
                if not field_name.replace('_', '').replace('-', '').isalnum():
                    invalid_fields.append(f"第{i+1}行: 字段名 '{field_name}' 格式无效")
                    continue

                # 检查重复字段名
                if field_name in custom_fields:
                    invalid_fields.append(f"第{i+1}行: 字段名 '{field_name}' 重复")
                    continue

                custom_fields[field_name] = field_value
            elif field_name or field_value:
                # 只填写了一个字段的情况
                invalid_fields.append(f"第{i+1}行: 字段名和字段值都必须填写")

        # 如果有无效字段，显示警告
        if invalid_fields:
            warning_msg = "发现以下问题：\n" + "\n".join(invalid_fields)
            show_warning_message(self, "字段验证失败", warning_msg)
            return {}

        return custom_fields


class ConfigGroupEditDialog(QDialog):
    """配置组编辑对话框"""

    def __init__(self, parent=None, config_group_name=None):
        super().__init__(parent)
        self.parent_window = parent
        self.config_group_name = config_group_name
        # 修正config_manager的获取方式
        if hasattr(parent, 'config_manager'):
            if hasattr(parent.config_manager, 'config_manager'):
                self.config_manager = parent.config_manager.config_manager
            else:
                self.config_manager = parent.config_manager
        else:
            self.config_manager = None
        self.init_ui()
        self.load_config_data()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle(f"编辑配置组 - {self.config_group_name}")
        self.setModal(True)
        self.resize(500, 400)

        layout = QVBoxLayout()

        # 基本信息组
        info_group = QGroupBox("基本信息")
        info_layout = QFormLayout()

        # 第4级分类名称（从完整路径中提取）
        fourth_level_name = self.config_group_name.split("/")[-1] if "/" in self.config_group_name else self.config_group_name
        self.name_edit = QLineEdit()
        self.name_edit.setText(fourth_level_name)
        info_layout.addRow("第4级分类名称:", self.name_edit)

        # 三级分类选择
        category_widget = QWidget()
        category_layout = QHBoxLayout()
        category_layout.setContentsMargins(0, 0, 0, 0)

        self.parent_combo = QComboBox()
        self.sub_combo = QComboBox()
        self.child_combo = QComboBox()

        category_layout.addWidget(self.parent_combo)
        category_layout.addWidget(self.sub_combo)
        category_layout.addWidget(self.child_combo)
        category_widget.setLayout(category_layout)

        info_layout.addRow("分类路径:", category_widget)

        # 描述
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("可选：配置组描述信息")
        info_layout.addRow("描述:", self.description_edit)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # 统计信息组
        stats_group = QGroupBox("统计信息")
        stats_layout = QFormLayout()

        self.url_label = QLabel()
        self.created_label = QLabel()
        self.updated_label = QLabel()
        self.cache_label = QLabel()

        stats_layout.addRow("输入URL:", self.url_label)
        stats_layout.addRow("创建时间:", self.created_label)
        stats_layout.addRow("更新时间:", self.updated_label)
        stats_layout.addRow("缓存状态:", self.cache_label)

        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_changes)
        button_layout.addWidget(self.save_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

        # 连接信号
        self.parent_combo.currentTextChanged.connect(self.on_parent_changed)
        self.sub_combo.currentTextChanged.connect(self.on_sub_changed)

    def load_config_data(self):
        """加载配置数据"""
        if not self.config_manager:
            show_error_message(self, "错误", "配置管理器未初始化")
            return

        if not self.config_group_name:
            show_error_message(self, "错误", "配置组名称为空")
            return

        try:
            # 加载三级分类
            self.load_categories()

            # 加载配置组数据
            print(f"正在加载配置组: {self.config_group_name}")
            config_data = self.config_manager.get_group(self.config_group_name)

            if config_data:
                print(f"成功加载配置数据: {config_data.keys()}")

                # 设置分类路径
                category_path = config_data.get("category_path", "政府机构/人大系统/北京人大/监督纵横")
                self.set_category_path(category_path)

                # 设置统计信息
                self.url_label.setText(config_data.get("input_url", "未设置"))

                # 处理时间信息
                created_at = config_data.get("created_at", "未知")
                updated_at = config_data.get("updated_at", "未知")

                self.created_label.setText(created_at)
                self.updated_label.setText(updated_at)

                # 处理缓存信息
                cache_info = config_data.get("cache", {})
                if cache_info:
                    last_update = cache_info.get("last_update")
                    if last_update:
                        self.cache_label.setText(f"已缓存 ({last_update})")
                    else:
                        self.cache_label.setText("有缓存数据")
                else:
                    self.cache_label.setText("无缓存")
            else:
                print(f"未找到配置组: {self.config_group_name}")
                # 检查所有可用的配置组
                all_groups = self.config_manager.get_groups()
                print(f"所有可用配置组: {all_groups}")
                show_error_message(self, "错误", f"找不到配置组 '{self.config_group_name}'")

        except Exception as e:
            print(f"加载配置数据异常: {e}")
            import traceback
            traceback.print_exc()
            show_error_message(self, "错误", f"加载配置数据失败: {e}")

    def load_categories(self):
        """加载三级分类数据"""
        try:
            # 加载父级分类
            parent_categories = self.config_manager.get_parent_categories()
            self.parent_combo.addItems(parent_categories)

        except Exception as e:
            print(f"加载分类失败: {e}")

    def on_parent_changed(self, parent_category):
        """父级分类改变"""
        self.sub_combo.clear()
        self.child_combo.clear()

        if not parent_category:
            return

        try:
            sub_categories = self.config_manager.get_sub_categories(parent_category)
            self.sub_combo.addItems(sub_categories)
        except Exception as e:
            print(f"加载次级分类失败: {e}")

    def on_sub_changed(self, sub_category):
        """次级分类改变"""
        self.child_combo.clear()

        parent_category = self.parent_combo.currentText()
        if not parent_category or not sub_category:
            return

        try:
            child_categories = self.config_manager.get_child_categories(parent_category, sub_category)
            self.child_combo.addItems(child_categories)
        except Exception as e:
            print(f"加载子级分类失败: {e}")

    def set_category_path(self, category_path):
        """设置分类路径（支持4级结构）"""
        try:
            parts = category_path.split("/")
            if len(parts) >= 3:
                parent, sub, child = parts[0], parts[1], parts[2]

                # 设置父级分类
                parent_index = self.parent_combo.findText(parent)
                if parent_index >= 0:
                    self.parent_combo.setCurrentIndex(parent_index)
                    self.on_parent_changed(parent)

                    # 设置次级分类
                    sub_index = self.sub_combo.findText(sub)
                    if sub_index >= 0:
                        self.sub_combo.setCurrentIndex(sub_index)
                        self.on_sub_changed(sub)

                        # 设置子级分类
                        child_index = self.child_combo.findText(child)
                        if child_index >= 0:
                            self.child_combo.setCurrentIndex(child_index)
                        else:
                            print(f"未找到子级分类: {child}")
                    else:
                        print(f"未找到次级分类: {sub}")
                else:
                    print(f"未找到父级分类: {parent}")
            else:
                print(f"分类路径格式错误: {category_path}")
        except Exception as e:
            print(f"设置分类路径失败: {e}")

    def infer_fourth_level(self, config_name, third_level):
        """根据配置组名称和第3级分类推断第4级分类"""
        try:
            # 如果配置组名称包含下划线，取后半部分作为第4级分类
            if "_" in config_name:
                return config_name.split("_")[-1]

            # 否则根据第3级分类推断常见的第4级分类
            if "政协" in third_level:
                return "提案工作"
            elif "人大" in third_level:
                return "监督纵横"
            else:
                return "常规工作"
        except:
            return "常规工作"

    def save_changes(self):
        """保存更改"""
        try:
            new_fourth_level = self.name_edit.text().strip()
            if not new_fourth_level:
                show_warning_message(self, "警告", "第4级分类名称不能为空")
                return

            # 构建新的分类路径
            parent = self.parent_combo.currentText()
            sub = self.sub_combo.currentText()
            child = self.child_combo.currentText()

            if not (parent and sub and child):
                show_warning_message(self, "警告", "请选择完整的三级分类路径")
                return

            new_category_path = f"{parent}/{sub}/{child}/{new_fourth_level}"

            # 获取原配置数据
            old_config = self.config_manager.get_group(self.config_group_name)
            if not old_config:
                show_error_message(self, "错误", "找不到原配置数据")
                return

            # 检查新路径是否已存在
            if new_category_path != self.config_group_name and new_category_path in self.config_manager.get_groups():
                show_warning_message(self, "警告", f"配置组 '{new_category_path}' 已存在")
                return

            # 如果路径改变，需要删除旧配置，创建新配置
            if new_category_path != self.config_group_name:
                # 删除旧配置
                self.config_manager.delete_group(self.config_group_name)

                # 创建新配置（使用完整路径作为配置组名称）
                old_config["category_path"] = new_category_path
                success = self.config_manager.config_manager.add_group(new_fourth_level, old_config, new_category_path)

                if success:
                    self.config_group_name = new_category_path
                else:
                    show_error_message(self, "错误", "重命名配置组失败")
                    return
            else:
                # 路径没有改变，只更新分类路径信息
                success = self.config_manager.move_config_to_category_path(self.config_group_name, new_category_path)
                if not success:
                    show_error_message(self, "错误", "更新配置组失败")
                    return

            self.accept()

        except Exception as e:
            show_error_message(self, "错误", f"保存更改失败: {e}")


class ConfigManagerDialog(QDialog):
    """三级分类配置管理对话框 - 完整的分类管理功能"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.config_manager = parent.config_manager.config_manager if hasattr(parent, 'config_manager') else None
        self.current_item = None

        self.setWindowTitle("三级分类配置管理器")
        self.setModal(True)
        self.resize(1000, 700)

        self.init_ui()
        self.load_data()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 顶部工具栏
        toolbar_layout = QHBoxLayout()

        # 新建按钮组
        self.add_parent_btn = QPushButton("➕ 新建1级分类")
        self.add_parent_btn.clicked.connect(self.add_parent_category)
        self.add_parent_btn.setToolTip("添加新的父级分类（如：政府机构、新闻媒体）")
        toolbar_layout.addWidget(self.add_parent_btn)

        self.add_sub_btn = QPushButton("➕ 新建2级分类")
        self.add_sub_btn.clicked.connect(self.add_sub_category)
        self.add_sub_btn.setToolTip("在选中的1级分类下添加2级分类")
        self.add_sub_btn.setEnabled(False)
        toolbar_layout.addWidget(self.add_sub_btn)

        self.add_child_btn = QPushButton("➕ 新建3级分类")
        self.add_child_btn.clicked.connect(self.add_child_category)
        self.add_child_btn.setToolTip("在选中的2级分类下添加3级分类")
        self.add_child_btn.setEnabled(False)
        toolbar_layout.addWidget(self.add_child_btn)

        toolbar_layout.addStretch()

        # 操作按钮组
        self.edit_btn = QPushButton("✏️ 编辑")
        self.edit_btn.clicked.connect(self.edit_selected)
        self.edit_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton("🗑️ 删除")
        self.delete_btn.clicked.connect(self.delete_selected)
        self.delete_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_btn)

        self.move_btn = QPushButton("🔄 移动")
        self.move_btn.clicked.connect(self.move_selected)
        self.move_btn.setEnabled(False)
        toolbar_layout.addWidget(self.move_btn)

        layout.addLayout(toolbar_layout)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)

        # 左侧：分类和配置组树
        left_widget = self.create_tree_widget()
        splitter.addWidget(left_widget)

        # 右侧：详细信息和操作
        right_widget = self.create_detail_widget()
        splitter.addWidget(right_widget)

        splitter.setSizes([500, 500])
        layout.addWidget(splitter)

        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.clicked.connect(self.load_data)
        button_layout.addWidget(refresh_btn)

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def create_tree_widget(self):
        """创建树形控件"""
        widget = QGroupBox("三级分类结构")
        layout = QVBoxLayout()

        # 搜索框
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入配置组名称或分类名称...")
        self.search_edit.textChanged.connect(self.filter_tree)
        search_layout.addWidget(self.search_edit)
        layout.addLayout(search_layout)

        # 树形控件
        self.tree = QTreeWidget()
        self.tree.setHeaderLabels(["名称", "类型", "数量/状态", "路径"])
        self.tree.itemClicked.connect(self.on_item_clicked)
        self.tree.itemSelectionChanged.connect(self.on_selection_changed)
        self.tree.itemExpanded.connect(self.on_item_expanded)
        self.tree.itemCollapsed.connect(self.on_item_collapsed)
        self.tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree.customContextMenuRequested.connect(self.show_context_menu)

        # 设置列宽
        self.tree.setColumnWidth(0, 200)
        self.tree.setColumnWidth(1, 100)
        self.tree.setColumnWidth(2, 100)
        self.tree.setColumnWidth(3, 200)

        layout.addWidget(self.tree)
        widget.setLayout(layout)
        return widget

    def create_detail_widget(self):
        """创建详细信息控件"""
        widget = QGroupBox("详细信息与操作")
        layout = QVBoxLayout()

        # 信息显示区域
        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        self.detail_text.setMaximumHeight(300)
        layout.addWidget(self.detail_text)

        # 快速操作区域
        operations_group = QGroupBox("快速操作")
        operations_layout = QVBoxLayout()

        # 配置组操作
        config_ops_layout = QHBoxLayout()
        self.move_config_btn = QPushButton("移动配置组")
        self.move_config_btn.clicked.connect(self.move_config_to_category)
        self.move_config_btn.setEnabled(False)
        config_ops_layout.addWidget(self.move_config_btn)

        self.clear_cache_btn = QPushButton("清空缓存")
        self.clear_cache_btn.clicked.connect(self.clear_cache_for_selected)
        self.clear_cache_btn.setEnabled(False)
        config_ops_layout.addWidget(self.clear_cache_btn)

        operations_layout.addLayout(config_ops_layout)

        # 分类操作
        category_ops_layout = QHBoxLayout()
        self.rename_btn = QPushButton("重命名")
        self.rename_btn.clicked.connect(self.rename_selected)
        self.rename_btn.setEnabled(False)
        category_ops_layout.addWidget(self.rename_btn)

        self.merge_btn = QPushButton("合并分类")
        self.merge_btn.clicked.connect(self.merge_categories)
        self.merge_btn.setEnabled(False)
        category_ops_layout.addWidget(self.merge_btn)

        operations_layout.addLayout(category_ops_layout)

        operations_group.setLayout(operations_layout)
        layout.addWidget(operations_group)

        # 统计信息
        stats_group = QGroupBox("统计信息")
        stats_layout = QVBoxLayout()
        self.stats_label = QLabel("请选择一个项目查看统计信息")
        stats_layout.addWidget(self.stats_label)
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        widget.setLayout(layout)
        return widget

    def load_data(self):
        """加载三级分类数据"""
        if not self.config_manager:
            self.detail_text.setText("配置管理器未初始化")
            return

        self.tree.clear()

        try:
            # 加载三级分类和配置组
            categories = self.config_manager.get_categories()
            groups = self.config_manager.config.get("groups", {})

            total_categories = 0
            total_configs = 0

            for parent_name, parent_info in categories.items():
                # 创建父级分类节点
                parent_item = QTreeWidgetItem(self.tree)
                parent_item.setText(0, parent_name)
                parent_item.setText(1, "1级分类")
                parent_item.setText(3, parent_name)

                subcategories = parent_info.get("subcategories", {})
                parent_config_count = 0

                # 遍历次级分类
                for sub_name, sub_info in subcategories.items():
                    sub_item = QTreeWidgetItem(parent_item)
                    sub_item.setText(0, sub_name)
                    sub_item.setText(1, "2级分类")
                    sub_item.setText(3, f"{parent_name}/{sub_name}")

                    child_subcategories = sub_info.get("subcategories", {})
                    sub_config_count = 0

                    # 遍历子级分类（第3级）
                    for child_name, child_info in child_subcategories.items():
                        child_item = QTreeWidgetItem(sub_item)
                        child_item.setText(0, child_name)
                        child_item.setText(1, "3级分类")

                        # 获取第3级分类下的所有配置组（通过第4级分类）
                        third_level_path = f"{parent_name}/{sub_name}/{child_name}"
                        all_configs_in_third_level = self.get_configs_in_third_level_category_for_tree(third_level_path)

                        child_item.setText(2, f"{len(all_configs_in_third_level)} 个配置")
                        child_item.setText(3, third_level_path)

                        child_item.setData(0, Qt.UserRole, {
                            "type": "child_category",
                            "name": child_name,
                            "parent": sub_name,
                            "grandparent": parent_name,
                            "path": third_level_path,
                            "data": child_info
                        })

                        # 添加配置组节点
                        for config_name in all_configs_in_third_level:
                            if config_name in groups:
                                config_item = QTreeWidgetItem(child_item)
                                config_item.setText(0, config_name)
                                config_item.setText(1, "配置组")
                                config_item.setText(3, third_level_path)

                                # 显示缓存状态
                                config_data = groups[config_name]
                                cache = config_data.get("cache", {})
                                if cache and cache.get("last_update"):
                                    config_item.setText(2, "已缓存")
                                else:
                                    config_item.setText(2, "无缓存")

                                config_item.setData(0, Qt.UserRole, {
                                    "type": "config",
                                    "name": config_name,
                                    "path": third_level_path,
                                    "data": config_data
                                })

                        sub_config_count += len(all_configs_in_third_level)
                        total_configs += len(all_configs_in_third_level)
                        child_item.setExpanded(False)  # 默认收起
                        total_categories += 1

                    sub_item.setText(2, f"{sub_config_count} 个配置")
                    sub_item.setData(0, Qt.UserRole, {
                        "type": "sub_category",
                        "name": sub_name,
                        "parent": parent_name,
                        "path": f"{parent_name}/{sub_name}",
                        "data": sub_info
                    })
                    parent_config_count += sub_config_count
                    sub_item.setExpanded(False)  # 默认收起

                parent_item.setText(2, f"{parent_config_count} 个配置")
                parent_item.setData(0, Qt.UserRole, {
                    "type": "parent_category",
                    "name": parent_name,
                    "path": parent_name,
                    "data": parent_info
                })
                parent_item.setExpanded(False)  # 默认收起

            # 更新统计信息
            self.stats_label.setText(f"总计: {len(categories)} 个1级分类, {total_categories} 个3级分类, {total_configs} 个配置组")

            # 恢复树状态
            self.load_tree_state()

        except Exception as e:
            self.detail_text.setText(f"加载数据失败: {e}")
            import traceback
            traceback.print_exc()

    def get_configs_in_third_level_category_for_tree(self, third_level_path):
        """为分类树获取第3级分类下的所有配置组名称"""
        try:
            parts = third_level_path.split("/")
            if len(parts) != 3:
                return []

            parent, sub, child = parts

            # 获取第4级分类
            fourth_categories = self.config_manager.get_fourth_categories(parent, sub, child)

            # 收集所有第4级分类下的配置组
            all_configs = []
            for fourth_category in fourth_categories:
                category_path = f"{parent}/{sub}/{child}/{fourth_category}"
                configs = self.config_manager.get_configs_by_category_path(category_path)
                all_configs.extend(configs)

            return all_configs
        except Exception as e:
            print(f"获取第3级分类配置失败(对话框): {e}")
            return []

    def save_tree_state(self):
        """保存树展开状态"""
        try:
            expanded_items = []

            def collect_expanded_items(item):
                if item.isExpanded():
                    data = item.data(0, Qt.UserRole)
                    if data:
                        expanded_items.append(data.get("path", ""))

                for i in range(item.childCount()):
                    collect_expanded_items(item.child(i))

            # 收集所有展开的项目
            for i in range(self.tree.topLevelItemCount()):
                collect_expanded_items(self.tree.topLevelItem(i))

            # 保存到文件
            state_file = "configs/app/tree_state.json"
            os.makedirs(os.path.dirname(state_file), exist_ok=True)
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump({"expanded_items": expanded_items}, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"保存树状态失败: {e}")

    def load_tree_state(self):
        """加载树展开状态"""
        try:
            state_file = "configs/app/tree_state.json"
            if not os.path.exists(state_file):
                return

            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)

            expanded_paths = set(state.get("expanded_items", []))

            def restore_expanded_state(item):
                data = item.data(0, Qt.UserRole)
                if data and data.get("path", "") in expanded_paths:
                    item.setExpanded(True)

                for i in range(item.childCount()):
                    restore_expanded_state(item.child(i))

            # 恢复所有项目的展开状态
            for i in range(self.tree.topLevelItemCount()):
                restore_expanded_state(self.tree.topLevelItem(i))

        except Exception as e:
            print(f"加载树状态失败: {e}")

    def on_item_expanded(self, item):
        """项目展开事件"""
        self.save_tree_state()

    def on_item_collapsed(self, item):
        """项目收起事件"""
        self.save_tree_state()

    def on_item_clicked(self, item, column):
        """项目点击事件"""
        self.current_item = item
        data = item.data(0, Qt.UserRole)
        if not data:
            return

        item_type = data["type"]
        if item_type in ["parent_category", "sub_category", "child_category"]:
            self.show_category_detail(data)
        elif item_type == "config":
            self.show_config_detail(data)

    def on_selection_changed(self):
        """选择改变事件"""
        selected_items = self.tree.selectedItems()
        if not selected_items:
            self.update_button_states(None)
            return

        item = selected_items[0]
        data = item.data(0, Qt.UserRole)
        self.update_button_states(data)

    def update_button_states(self, data):
        """更新按钮状态"""
        if not data:
            # 没有选择项目
            self.add_sub_btn.setEnabled(False)
            self.add_child_btn.setEnabled(False)
            self.edit_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
            self.move_btn.setEnabled(False)
            self.move_config_btn.setEnabled(False)
            self.clear_cache_btn.setEnabled(False)
            self.rename_btn.setEnabled(False)
            self.merge_btn.setEnabled(False)
            return

        item_type = data["type"]

        # 新建按钮
        self.add_sub_btn.setEnabled(item_type == "parent_category")
        self.add_child_btn.setEnabled(item_type == "sub_category")

        # 通用操作按钮
        self.edit_btn.setEnabled(True)
        self.delete_btn.setEnabled(item_type != "parent_category" or len(self.config_manager.get_parent_categories()) > 1)
        self.rename_btn.setEnabled(item_type in ["parent_category", "sub_category", "child_category"])

        # 移动按钮
        self.move_btn.setEnabled(item_type in ["sub_category", "child_category", "config"])
        self.move_config_btn.setEnabled(item_type == "config")

        # 配置组特有操作
        self.clear_cache_btn.setEnabled(item_type == "config")

        # 分类合并
        self.merge_btn.setEnabled(item_type in ["sub_category", "child_category"])

    def show_category_detail(self, data):
        """显示分类详细信息"""
        item_type = data["type"]
        name = data["name"]
        path = data["path"]
        category_data = data["data"]

        if item_type == "parent_category":
            level = "1级分类（父级）"
        elif item_type == "sub_category":
            level = "2级分类（次级）"
        else:
            level = "3级分类（子级）"

        info_text = f"📁 {level}\n"
        info_text += f"名称: {name}\n"
        info_text += f"路径: {path}\n"
        info_text += f"描述: {category_data.get('description', '无描述')}\n"

        if item_type == "parent_category":
            subcategories = category_data.get('subcategories', {})
            info_text += f"包含2级分类: {len(subcategories)} 个\n"
            for sub_name in subcategories.keys():
                info_text += f"  • {sub_name}\n"

        elif item_type == "sub_category":
            child_subcategories = category_data.get('subcategories', {})
            info_text += f"包含3级分类: {len(child_subcategories)} 个\n"
            for child_name in child_subcategories.keys():
                info_text += f"  • {child_name}\n"

        elif item_type == "child_category":
            configs = category_data.get('configs', [])
            info_text += f"包含配置组: {len(configs)} 个\n"
            for config_name in configs:
                info_text += f"  • {config_name}\n"

        info_text += f"\n创建时间: {category_data.get('created_at', '未知')}\n"
        info_text += f"更新时间: {category_data.get('updated_at', '未知')}\n"

        self.detail_text.setText(info_text)

    def show_config_detail(self, data):
        """显示配置组详细信息"""
        config_name = data["name"]
        config_data = data["data"]
        path = data["path"]

        info_text = f"⚙️ 配置组详细信息\n"
        info_text += f"名称: {config_name}\n"
        info_text += f"所属分类: {path}\n"
        info_text += f"输入URL: {config_data.get('input_url', '未设置')}\n"
        info_text += f"爬取模式: {config_data.get('mode', 'balance')}\n"
        info_text += f"最大页数: {config_data.get('max_pages', '未设置')}\n"
        info_text += f"重试次数: {config_data.get('retry', 2)}\n"
        info_text += f"间隔时间: {config_data.get('interval', 0)} 秒\n\n"

        # 显示选择器信息
        info_text += "选择器配置:\n"
        title_selectors = config_data.get('title_selectors', [])
        if title_selectors:
            info_text += f"  标题选择器: {len(title_selectors)} 个\n"

        content_selectors = config_data.get('content_selectors', [])
        if content_selectors:
            info_text += f"  内容选择器: {len(content_selectors)} 个\n"

        # 显示缓存信息
        cache = config_data.get('cache', {})
        if cache:
            info_text += "\n📊 缓存信息:\n"
            info_text += f"  最后更新: {cache.get('last_update', '从未更新')}\n"
            info_text += f"  文章总数: {cache.get('total_articles', 0)}\n"
            info_text += f"  成功率: {cache.get('success_rate', 0.0):.1%}\n"
            info_text += f"  创建时间: {cache.get('created_at', '未知')}\n"

            last_urls = cache.get('last_urls', [])
            if last_urls:
                info_text += f"\n最近 {len(last_urls)} 条URL:\n"
                for i, url_info in enumerate(last_urls[:5], 1):  # 只显示前5条
                    title = url_info.get('title', '无标题')[:30]  # 限制标题长度
                    info_text += f"  {i}. {title}...\n"
        else:
            info_text += "\n📊 缓存信息: 无缓存数据\n"

        self.detail_text.setText(info_text)

    def add_parent_category(self):
        """添加1级分类"""
        try:
            name = get_text_input(self, "新建1级分类", "请输入1级分类名称:")
            if not name:
                return

            description = get_text_input(self, "分类描述", f"请输入'{name}'的描述（可选）:")

            # 检查是否已存在
            categories = self.config_manager.get_categories()
            if name in categories:
                show_warning_message(self, "警告", f"1级分类 '{name}' 已存在")
                return

            # 添加新的1级分类
            success = self.config_manager.add_category(name, description or f"{name}分类")
            if success:
                show_info_message(self, "成功", f"1级分类 '{name}' 已添加")
                self.load_data()
            else:
                show_error_message(self, "失败", "添加1级分类失败")

        except Exception as e:
            show_error_message(self, "错误", f"添加1级分类失败: {e}")

    def add_sub_category(self):
        """添加2级分类"""
        if not self.current_item:
            show_warning_message(self, "警告", "请先选择一个1级分类")
            return

        data = self.current_item.data(0, Qt.UserRole)
        if not data or data["type"] != "parent_category":
            show_warning_message(self, "警告", "请选择一个1级分类来添加2级分类")
            return

        try:
            parent_name = data["name"]
            name = get_text_input(self, "新建2级分类", f"在'{parent_name}'下添加2级分类:")
            if not name:
                return

            description = get_text_input(self, "分类描述", f"请输入'{name}'的描述（可选）:")

            # 检查是否已存在
            parent_data = data["data"]
            subcategories = parent_data.get("subcategories", {})
            if name in subcategories:
                show_warning_message(self, "警告", f"2级分类 '{name}' 已存在")
                return

            # 添加新的2级分类
            success = self.config_manager.add_three_level_category(parent_name, name, "默认子分类", description or f"{name}分类")
            if success:
                show_info_message(self, "成功", f"2级分类 '{name}' 已添加到 '{parent_name}' 下")
                self.load_data()
            else:
                show_error_message(self, "失败", "添加2级分类失败")

        except Exception as e:
            show_error_message(self, "错误", f"添加2级分类失败: {e}")

    def add_child_category(self):
        """添加3级分类"""
        if not self.current_item:
            show_warning_message(self, "警告", "请先选择一个2级分类")
            return

        data = self.current_item.data(0, Qt.UserRole)
        if not data or data["type"] != "sub_category":
            show_warning_message(self, "警告", "请选择一个2级分类来添加3级分类")
            return

        try:
            parent_name = data["parent"]
            sub_name = data["name"]
            name = get_text_input(self, "新建3级分类", f"在'{parent_name}/{sub_name}'下添加3级分类:")
            if not name:
                return

            description = get_text_input(self, "分类描述", f"请输入'{name}'的描述（可选）:")

            # 检查是否已存在
            sub_data = data["data"]
            child_subcategories = sub_data.get("subcategories", {})
            if name in child_subcategories:
                show_warning_message(self, "警告", f"3级分类 '{name}' 已存在")
                return

            # 添加新的3级分类
            success = self.config_manager.add_three_level_category(parent_name, sub_name, name, description or f"{name}分类")
            if success:
                show_info_message(self, "成功", f"3级分类 '{name}' 已添加到 '{parent_name}/{sub_name}' 下")
                self.load_data()
            else:
                show_error_message(self, "失败", "添加3级分类失败")

        except Exception as e:
            show_error_message(self, "错误", f"添加3级分类失败: {e}")

    def edit_selected(self):
        """编辑选中项目"""
        if not self.current_item:
            show_warning_message(self, "警告", "请先选择一个项目")
            return

        data = self.current_item.data(0, Qt.UserRole)
        if not data:
            return

        item_type = data["type"]
        if item_type in ["parent_category", "sub_category", "child_category"]:
            self.edit_category(data)
        elif item_type == "config":
            self.edit_config(data)

    def delete_selected(self):
        """删除选中项目"""
        if not self.current_item:
            show_warning_message(self, "警告", "请先选择一个项目")
            return

        data = self.current_item.data(0, Qt.UserRole)
        if not data:
            return

        item_type = data["type"]
        name = data["name"]

        # 确认删除
        if item_type == "parent_category":
            msg = f"确定要删除1级分类 '{name}' 吗？\n这将删除其下所有2级、3级分类和配置组！"
        elif item_type == "sub_category":
            msg = f"确定要删除2级分类 '{name}' 吗？\n这将删除其下所有3级分类和配置组！"
        elif item_type == "child_category":
            msg = f"确定要删除3级分类 '{name}' 吗？\n这将删除其下所有配置组！"
        else:
            msg = f"确定要删除配置组 '{name}' 吗？"

        reply = QMessageBox.question(self, "确认删除", msg,
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        if reply != QMessageBox.Yes:
            return

        try:
            if item_type in ["parent_category", "sub_category", "child_category"]:
                success = self.delete_category(data)
            else:
                success = self.delete_config(data)

            if success:
                show_info_message(self, "成功", f"已删除 '{name}'")
                self.load_data()
            else:
                show_error_message(self, "失败", f"删除 '{name}' 失败")

        except Exception as e:
            show_error_message(self, "错误", f"删除失败: {e}")

    def move_selected(self):
        """移动选中项目"""
        if not self.current_item:
            show_warning_message(self, "警告", "请先选择一个项目")
            return

        data = self.current_item.data(0, Qt.UserRole)
        if not data:
            return

        item_type = data["type"]
        if item_type == "config":
            self.move_config_to_category()
        elif item_type in ["sub_category", "child_category"]:
            self.move_category(data)
        else:
            show_warning_message(self, "警告", "1级分类不能移动")

    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.tree.itemAt(position)
        if not item:
            return

        menu = QMenu(self)
        data = item.data(0, Qt.UserRole)

        if not data:
            return

        item_type = data["type"]

        if item_type == "parent_category":
            menu.addAction("📝 编辑1级分类", lambda: self.edit_category(data))
            menu.addAction("➕ 添加2级分类", self.add_sub_category)
            menu.addSeparator()
            menu.addAction("🗑️ 删除1级分类", lambda: self.delete_category(data))

        elif item_type == "sub_category":
            menu.addAction("📝 编辑2级分类", lambda: self.edit_category(data))
            menu.addAction("➕ 添加3级分类", self.add_child_category)
            menu.addSeparator()
            menu.addAction("🔄 移动2级分类", lambda: self.move_category(data))
            menu.addAction("🗑️ 删除2级分类", lambda: self.delete_category(data))

        elif item_type == "child_category":
            menu.addAction("📝 编辑3级分类", lambda: self.edit_category(data))
            menu.addSeparator()
            menu.addAction("🔄 移动3级分类", lambda: self.move_category(data))
            menu.addAction("🗑️ 删除3级分类", lambda: self.delete_category(data))

        elif item_type == "config":
            menu.addAction("📝 编辑配置组", lambda: self.edit_config(data))
            menu.addSeparator()
            menu.addAction("🔄 移动配置组", self.move_config_to_category)
            menu.addAction("🧹 清空缓存", lambda: self.clear_config_cache(data))
            menu.addAction("🗑️ 删除配置组", lambda: self.delete_config(data))

        menu.exec_(self.tree.mapToGlobal(position))

    def delete_category(self, data):
        """删除分类"""
        try:
            item_type = data["type"]
            name = data["name"]

            if item_type == "parent_category":
                success = self.config_manager.delete_parent_category(name)
            elif item_type == "sub_category":
                parent_name = data["parent"]
                success = self.config_manager.delete_sub_category(parent_name, name)
            elif item_type == "child_category":
                parent_name = data["grandparent"]
                sub_name = data["parent"]
                success = self.config_manager.delete_child_category(parent_name, sub_name, name)
            else:
                return False

            return success

        except Exception as e:
            print(f"删除分类失败: {e}")
            return False

    def delete_config(self, data):
        """删除配置组"""
        try:
            config_name = data["name"]
            success = self.config_manager.delete_group(config_name)
            return success
        except Exception as e:
            print(f"删除配置组失败: {e}")
            return False

    def edit_category(self, data):
        """编辑分类"""
        try:
            item_type = data["type"]
            name = data["name"]

            # 获取新名称
            new_name = get_text_input(self, f"编辑{item_type.replace('_', '')}名称", f"请输入新的名称:", name)
            if not new_name or new_name == name:
                return

            # 获取新描述
            current_desc = data["data"].get("description", "")
            new_desc = get_text_input(self, "编辑描述", f"请输入新的描述:", current_desc)

            # 执行重命名
            if item_type == "parent_category":
                success = self.config_manager.rename_parent_category(name, new_name, new_desc)
            elif item_type == "sub_category":
                parent_name = data["parent"]
                success = self.config_manager.rename_sub_category(parent_name, name, new_name, new_desc)
            elif item_type == "child_category":
                parent_name = data["grandparent"]
                sub_name = data["parent"]
                success = self.config_manager.rename_child_category(parent_name, sub_name, name, new_name, new_desc)
            else:
                return

            if success:
                show_info_message(self, "成功", f"已重命名为 '{new_name}'")
                self.load_data()
            else:
                show_error_message(self, "失败", "重命名失败")

        except Exception as e:
            show_error_message(self, "错误", f"编辑分类失败: {e}")

    def edit_config(self, data):
        """编辑配置组"""
        try:
            config_name = data["name"]
            # 打开配置组编辑对话框
            dialog = ConfigGroupEditDialog(self.parent_window, config_name)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()
        except Exception as e:
            show_error_message(self, "错误", f"编辑配置组失败: {e}")

    def move_category(self, data):
        """移动分类"""
        try:
            item_type = data["type"]
            name = data["name"]

            if item_type == "sub_category":
                # 移动2级分类到其他1级分类下
                parent_categories = self.config_manager.get_parent_categories()
                current_parent = data["parent"]
                available_parents = [p for p in parent_categories if p != current_parent]

                if not available_parents:
                    show_warning_message(self, "警告", "没有其他1级分类可以移动到")
                    return

                from PyQt5.QtWidgets import QInputDialog
                new_parent, ok = QInputDialog.getItem(
                    self, "移动2级分类", f"选择 '{name}' 的新1级分类:",
                    available_parents, 0, False
                )

                if ok and new_parent:
                    success = self.config_manager.move_sub_category(current_parent, name, new_parent)
                    if success:
                        show_info_message(self, "成功", f"已移动到 '{new_parent}' 下")
                        self.load_data()
                    else:
                        show_error_message(self, "失败", "移动失败")

            elif item_type == "child_category":
                # 移动3级分类到其他2级分类下
                # 这里可以实现更复杂的移动逻辑
                show_info_message(self, "提示", "3级分类移动功能正在开发中")

        except Exception as e:
            show_error_message(self, "错误", f"移动分类失败: {e}")

    def clear_config_cache(self, data):
        """清空配置组缓存"""
        try:
            config_name = data["name"]
            reply = QMessageBox.question(
                self, "确认清空", f"确定要清空配置组 '{config_name}' 的缓存吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                if self.config_manager.clear_config_cache(config_name):
                    show_info_message(self, "成功", f"配置组 '{config_name}' 的缓存已清空")
                    self.load_data()
                else:
                    show_error_message(self, "失败", "清空缓存失败")
        except Exception as e:
            show_error_message(self, "错误", f"清空缓存失败: {e}")

    def clear_cache_for_selected(self):
        """清空选中配置组的缓存"""
        if not self.current_item:
            show_warning_message(self, "警告", "请先选择一个配置组")
            return

        data = self.current_item.data(0, Qt.UserRole)
        if not data or data["type"] != "config":
            show_warning_message(self, "警告", "请选择一个配置组")
            return

        self.clear_config_cache(data)

    def move_config_to_category(self):
        """移动配置组到其他分类"""
        if not self.current_item:
            show_warning_message(self, "警告", "请先选择一个配置组")
            return

        data = self.current_item.data(0, Qt.UserRole)
        if not data or data["type"] != "config":
            show_warning_message(self, "警告", "请选择一个配置组")
            return

        try:
            config_name = data["name"]

            # 创建三级分类选择对话框
            dialog = CategorySelectionDialog(self, "移动配置组", f"选择 '{config_name}' 的目标分类:")
            if dialog.exec_() == QDialog.Accepted:
                target_path = dialog.get_selected_path()
                if target_path:
                    success = self.config_manager.move_config_to_category_path(config_name, target_path)
                    if success:
                        show_info_message(self, "成功", f"配置组 '{config_name}' 已移动到 '{target_path}'")
                        self.load_data()
                    else:
                        show_error_message(self, "失败", "移动配置组失败")
        except Exception as e:
            show_error_message(self, "错误", f"移动配置组失败: {e}")

    def rename_selected(self):
        """重命名选中项目"""
        self.edit_selected()

    def merge_categories(self):
        """合并分类"""
        show_info_message(self, "提示", "分类合并功能正在开发中")

    def filter_tree(self, text):
        """过滤树形控件"""
        # 简单的搜索功能，可以后续完善
        if not text:
            # 显示所有项目
            for i in range(self.tree.topLevelItemCount()):
                self.tree.topLevelItem(i).setHidden(False)
        else:
            # 隐藏不匹配的项目
            text = text.lower()
            for i in range(self.tree.topLevelItemCount()):
                item = self.tree.topLevelItem(i)
                self.filter_item(item, text)

    def filter_item(self, item, text):
        """递归过滤项目"""
        item_text = item.text(0).lower()
        has_visible_child = False

        # 检查子项目
        for i in range(item.childCount()):
            child = item.child(i)
            if self.filter_item(child, text):
                has_visible_child = True

        # 如果项目名称包含搜索文本或有可见的子项目，则显示
        should_show = text in item_text or has_visible_child
        item.setHidden(not should_show)

        return should_show


class CategorySelectionDialog(QDialog):
    """三级分类选择对话框"""

    def __init__(self, parent=None, title="选择分类", message="请选择目标分类:"):
        super().__init__(parent)
        self.parent_window = parent
        self.config_manager = parent.config_manager if hasattr(parent, 'config_manager') else None
        self.selected_path = None

        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(400, 300)

        self.init_ui(message)
        self.load_categories()

    def init_ui(self, message):
        """初始化界面"""
        layout = QVBoxLayout()

        # 消息标签
        layout.addWidget(QLabel(message))

        # 四级分类选择
        category_layout = QFormLayout()

        self.parent_combo = QComboBox()
        self.parent_combo.currentTextChanged.connect(self.on_parent_changed)
        category_layout.addRow("1级分类:", self.parent_combo)

        self.sub_combo = QComboBox()
        self.sub_combo.currentTextChanged.connect(self.on_sub_changed)
        category_layout.addRow("2级分类:", self.sub_combo)

        self.third_combo = QComboBox()
        self.third_combo.currentTextChanged.connect(self.on_third_changed)
        category_layout.addRow("3级分类:", self.third_combo)

        self.fourth_combo = QComboBox()
        category_layout.addRow("4级分类:", self.fourth_combo)

        layout.addLayout(category_layout)

        # 当前选择显示
        self.path_label = QLabel("当前选择: 未选择")
        self.path_label.setStyleSheet("color: blue; font-weight: bold;")
        layout.addWidget(self.path_label)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept_selection)
        button_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

        # 连接信号更新路径显示
        self.parent_combo.currentTextChanged.connect(self.update_path_display)
        self.sub_combo.currentTextChanged.connect(self.update_path_display)
        self.third_combo.currentTextChanged.connect(self.update_path_display)
        self.fourth_combo.currentTextChanged.connect(self.update_path_display)

    def load_categories(self):
        """加载分类数据"""
        if not self.config_manager:
            return

        try:
            parent_categories = self.config_manager.get_parent_categories()
            self.parent_combo.addItems(parent_categories)
        except Exception as e:
            print(f"加载分类失败: {e}")

    def on_parent_changed(self, parent_category):
        """父级分类改变"""
        self.sub_combo.clear()
        self.third_combo.clear()
        self.fourth_combo.clear()

        if not parent_category:
            return

        try:
            sub_categories = self.config_manager.get_sub_categories(parent_category)
            self.sub_combo.addItems(sub_categories)
        except Exception as e:
            print(f"加载次级分类失败: {e}")

    def on_sub_changed(self, sub_category):
        """次级分类改变"""
        self.third_combo.clear()
        self.fourth_combo.clear()

        parent_category = self.parent_combo.currentText()
        if not parent_category or not sub_category:
            return

        try:
            third_categories = self.config_manager.get_child_categories(parent_category, sub_category)
            self.third_combo.addItems(third_categories)
        except Exception as e:
            print(f"加载第三级分类失败: {e}")

    def on_third_changed(self, third_category):
        """第三级分类改变"""
        self.fourth_combo.clear()

        parent_category = self.parent_combo.currentText()
        sub_category = self.sub_combo.currentText()
        if not parent_category or not sub_category or not third_category:
            return

        try:
            fourth_categories = self.config_manager.get_fourth_categories(parent_category, sub_category, third_category)
            self.fourth_combo.addItems(fourth_categories)
        except Exception as e:
            print(f"加载第四级分类失败: {e}")

    def update_path_display(self):
        """更新路径显示"""
        parent = self.parent_combo.currentText()
        sub = self.sub_combo.currentText()
        third = self.third_combo.currentText()
        fourth = self.fourth_combo.currentText()

        if parent and sub and third and fourth:
            path = f"{parent}/{sub}/{third}/{fourth}"
            self.path_label.setText(f"当前选择: {path}")
            self.selected_path = path
        else:
            self.path_label.setText("当前选择: 未完整选择")
            self.selected_path = None

    def accept_selection(self):
        """确认选择"""
        if not self.selected_path:
            show_warning_message(self, "警告", "请选择完整的四级分类路径")
            return

        self.accept()

    def get_selected_path(self):
        """获取选择的路径"""
        return self.selected_path


if __name__ == "__main__":
    main()
