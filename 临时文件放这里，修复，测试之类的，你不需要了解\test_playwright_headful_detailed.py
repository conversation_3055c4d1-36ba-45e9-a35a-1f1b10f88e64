#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Playwright有头模式详细测试翻页功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright

async def test_playwright_headful_detailed():
    """使用Playwright有头模式详细测试"""
    print("🧪 使用Playwright有头模式详细测试翻页功能")
    print("=" * 60)
    
    async with async_playwright() as p:
        # 启动有头浏览器
        browser = await p.chromium.launch(
            headless=False,  # 确保是有头模式
            slow_mo=1000,    # 慢速操作，方便观察
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        page = await context.new_page()
        
        try:
            url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
            print(f"📋 访问网站: {url}")
            
            # 访问页面
            await page.goto(url, timeout=60000)
            
            # 等待网络空闲
            await page.wait_for_load_state('networkidle', timeout=30000)
            
            print("✅ 页面加载完成")
            print(f"📋 页面标题: {await page.title()}")
            print(f"📋 当前URL: {page.url}")
            
            # 等待更长时间，确保所有动态内容都加载
            print("⏳ 等待10秒，确保页面完全加载...")
            await page.wait_for_timeout(10000)
            
            # 滚动到页面底部
            print("📜 滚动到页面底部...")
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(3000)
            
            # 截图保存当前状态
            await page.screenshot(path="current_page.png", full_page=True)
            print("📸 已保存完整页面截图: current_page.png")
            
            # 获取页面HTML内容
            html_content = await page.content()
            
            # 检查页面是否包含翻页相关内容
            pagination_keywords = ["下一页", "第1页", "第2页", "/12页", "确定"]
            found_keywords = []
            
            for keyword in pagination_keywords:
                if keyword in html_content:
                    found_keywords.append(keyword)
            
            print(f"\n🔍 页面中找到的翻页关键词: {found_keywords}")
            
            if not found_keywords:
                print("❌ 页面中没有找到翻页相关内容")
                print("💡 这可能意味着:")
                print("   1. 页面还没有完全加载")
                print("   2. 内容是通过AJAX动态加载的")
                print("   3. 需要特定的操作才能显示翻页")
                
                # 尝试等待更长时间
                print("⏳ 再等待10秒...")
                await page.wait_for_timeout(10000)
                
                # 重新检查
                html_content = await page.content()
                found_keywords = []
                for keyword in pagination_keywords:
                    if keyword in html_content:
                        found_keywords.append(keyword)
                
                print(f"🔍 重新检查后找到的关键词: {found_keywords}")
            
            # 查找所有可能的翻页元素
            print("\n🔍 查找所有可能的翻页元素...")
            
            # 1. 查找所有input元素
            input_elements = await page.query_selector_all("input")
            print(f"  📋 找到 {len(input_elements)} 个input元素:")
            
            for i, elem in enumerate(input_elements):
                try:
                    input_type = await elem.get_attribute('type')
                    input_value = await elem.get_attribute('value')
                    input_name = await elem.get_attribute('name')
                    is_visible = await elem.is_visible()
                    is_enabled = await elem.is_enabled()
                    
                    print(f"    [{i+1}] type: '{input_type}', value: '{input_value}', name: '{input_name}'")
                    print(f"        可见: {is_visible}, 可用: {is_enabled}")
                    
                    # 如果是可能的翻页按钮
                    if input_value and any(keyword in str(input_value) for keyword in ['下一页', 'next', '确定', '>']):
                        print(f"        🎯 这可能是翻页按钮！")
                        
                        if is_visible and is_enabled:
                            print(f"        🎯 尝试点击...")
                            
                            # 滚动到元素位置
                            await elem.scroll_into_view_if_needed()
                            await page.wait_for_timeout(1000)
                            
                            # 获取点击前的URL和页面内容
                            current_url = page.url
                            current_content = await page.content()
                            
                            print(f"        📋 点击前URL: {current_url}")
                            
                            try:
                                # 点击元素
                                await elem.click()
                                print("        ✅ 点击成功")
                                
                                # 等待页面变化
                                await page.wait_for_timeout(5000)
                                
                                # 检查变化
                                new_url = page.url
                                new_content = await page.content()
                                
                                print(f"        📋 点击后URL: {new_url}")
                                
                                if new_url != current_url:
                                    print("        🎉 URL发生变化，翻页可能成功！")
                                elif new_content != current_content:
                                    print("        🎉 页面内容发生变化，AJAX翻页可能成功！")
                                else:
                                    print("        ⚠️ 没有检测到明显变化")
                                
                                # 检查是否有第2页的迹象
                                if "第2页" in new_content or "2/" in new_content:
                                    print("        🎉 确认翻页成功！")
                                    
                                    # 截图保存翻页后的状态
                                    await page.screenshot(path="after_pagination.png", full_page=True)
                                    print("        📸 已保存翻页后截图: after_pagination.png")
                                    
                                    return True
                                
                            except Exception as click_error:
                                print(f"        ❌ 点击失败: {click_error}")
                        else:
                            print(f"        ⚠️ 按钮不可见或不可用")
                    
                    print()
                    
                except Exception as e:
                    print(f"    [{i+1}] 获取元素信息失败: {e}")
            
            # 2. 查找所有链接
            print("\n🔍 查找所有链接...")
            link_elements = await page.query_selector_all("a")
            print(f"  📋 找到 {len(link_elements)} 个链接元素")
            
            pagination_links = []
            for elem in link_elements:
                try:
                    text = await elem.text_content()
                    href = await elem.get_attribute('href')
                    
                    if text and any(keyword in text.lower() for keyword in ['下一页', 'next', '>', '更多']):
                        pagination_links.append({
                            'element': elem,
                            'text': text.strip(),
                            'href': href
                        })
                except Exception:
                    continue
            
            if pagination_links:
                print(f"  ✅ 找到 {len(pagination_links)} 个可能的翻页链接:")
                for i, link in enumerate(pagination_links):
                    print(f"    [{i+1}] 文本: '{link['text']}', href: '{link['href']}'")
            else:
                print("  ❌ 未找到翻页链接")
            
            print("\n📊 测试完成")
            
            # 保持浏览器打开，方便手动检查
            print("\n⏳ 保持浏览器打开30秒，请手动检查页面...")
            print("💡 您可以在浏览器中手动尝试翻页操作")
            await page.wait_for_timeout(30000)
            
            return False
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            return False
            
        finally:
            await context.close()
            await browser.close()

if __name__ == "__main__":
    success = asyncio.run(test_playwright_headful_detailed())
    if success:
        print("🎉 翻页测试成功！")
    else:
        print("❌ 翻页测试失败，但您可以查看截图了解页面状态")
