#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Playwright优化分析和建议
分析当前失败率高的原因并提供优化方案
"""

import asyncio
from playwright.async_api import async_playwright
import time
import random

class PlaywrightOptimizer:
    """Playwright优化器"""
    
    def __init__(self):
        self.optimization_configs = {
            "basic": {
                "name": "基础配置",
                "description": "当前使用的基础配置",
                "config": {
                    "headless": True,
                    "timeout": 30000,
                    "wait_until": "domcontentloaded",
                    "wait_for_load_state": "networkidle",
                    "wait_for_load_state_timeout": 10000
                }
            },
            "enhanced": {
                "name": "增强配置",
                "description": "优化的反爬虫配置",
                "config": {
                    "headless": True,
                    "timeout": 60000,
                    "wait_until": "load",
                    "wait_for_load_state": "networkidle",
                    "wait_for_load_state_timeout": 30000,
                    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "viewport": {"width": 1920, "height": 1080},
                    "extra_http_headers": {
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                        "Accept-Encoding": "gzip, deflate, br",
                        "DNT": "1",
                        "Connection": "keep-alive",
                        "Upgrade-Insecure-Requests": "1",
                    }
                }
            },
            "stealth": {
                "name": "隐身配置",
                "description": "最强反爬虫检测配置",
                "config": {
                    "headless": True,
                    "timeout": 90000,
                    "wait_until": "networkidle",
                    "wait_for_load_state": "networkidle",
                    "wait_for_load_state_timeout": 45000,
                    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "viewport": {"width": 1920, "height": 1080},
                    "java_script_enabled": True,
                    "bypass_csp": True,
                    "ignore_https_errors": True,
                    "extra_http_headers": {
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                        "Accept-Encoding": "gzip, deflate, br",
                        "Cache-Control": "no-cache",
                        "Pragma": "no-cache",
                        "Sec-Fetch-Dest": "document",
                        "Sec-Fetch-Mode": "navigate",
                        "Sec-Fetch-Site": "none",
                        "Sec-Fetch-User": "?1",
                        "Upgrade-Insecure-Requests": "1",
                    },
                    "args": [
                        "--no-sandbox",
                        "--disable-setuid-sandbox",
                        "--disable-dev-shm-usage",
                        "--disable-accelerated-2d-canvas",
                        "--no-first-run",
                        "--no-zygote",
                        "--disable-gpu",
                        "--disable-background-timer-throttling",
                        "--disable-backgrounding-occluded-windows",
                        "--disable-renderer-backgrounding",
                        "--disable-features=TranslateUI",
                        "--disable-ipc-flooding-protection",
                        "--disable-web-security",
                        "--disable-features=VizDisplayCompositor"
                    ]
                }
            }
        }
    
    def analyze_failure_reasons(self):
        """分析失败原因"""
        print("="*60)
        print("Playwright失败率高的可能原因分析")
        print("="*60)
        
        reasons = [
            {
                "原因": "超时设置过短",
                "描述": "当前30秒超时对于复杂页面可能不够",
                "影响": "页面加载未完成就超时失败",
                "优化": "增加超时时间到60-90秒"
            },
            {
                "原因": "等待策略不当",
                "描述": "domcontentloaded可能不足以等待动态内容",
                "影响": "JavaScript渲染的内容未加载完成",
                "优化": "使用networkidle或load策略"
            },
            {
                "原因": "反爬虫检测",
                "描述": "网站检测到自动化工具特征",
                "影响": "被识别为机器人，返回空页面或验证码",
                "优化": "使用更真实的浏览器配置和请求头"
            },
            {
                "原因": "网络不稳定",
                "描述": "网络连接问题导致请求失败",
                "影响": "间歇性连接失败",
                "优化": "增加重试次数和间隔"
            },
            {
                "原因": "选择器失效",
                "描述": "网站更新导致CSS选择器失效",
                "影响": "无法找到目标元素",
                "优化": "使用多选择器备选方案"
            },
            {
                "原因": "JavaScript依赖",
                "描述": "内容完全依赖JavaScript渲染",
                "影响": "静态爬取无法获取内容",
                "优化": "确保JavaScript执行完成"
            }
        ]
        
        for i, reason in enumerate(reasons, 1):
            print(f"{i}. {reason['原因']}")
            print(f"   描述: {reason['描述']}")
            print(f"   影响: {reason['影响']}")
            print(f"   优化: {reason['优化']}")
            print()
    
    def get_optimization_recommendations(self):
        """获取优化建议"""
        print("="*60)
        print("Playwright优化建议")
        print("="*60)
        
        recommendations = [
            {
                "类别": "超时和等待策略",
                "建议": [
                    "将页面超时从30秒增加到60-90秒",
                    "使用networkidle等待策略确保网络请求完成",
                    "添加额外的等待时间处理慢速网站",
                    "对不同类型网站使用不同的等待策略"
                ]
            },
            {
                "类别": "反爬虫绕过",
                "建议": [
                    "使用真实的User-Agent字符串",
                    "设置完整的HTTP请求头",
                    "模拟真实的浏览器行为",
                    "添加随机延迟避免被检测"
                ]
            },
            {
                "类别": "错误处理和重试",
                "建议": [
                    "增加重试次数到3-5次",
                    "使用指数退避重试策略",
                    "对不同错误类型使用不同处理方式",
                    "记录详细的失败原因"
                ]
            },
            {
                "类别": "选择器优化",
                "建议": [
                    "为每个网站配置多个备选选择器",
                    "使用更稳定的选择器（避免依赖class名）",
                    "添加选择器有效性检查",
                    "定期更新选择器配置"
                ]
            },
            {
                "类别": "性能优化",
                "建议": [
                    "禁用不必要的资源加载（图片、CSS等）",
                    "使用浏览器缓存",
                    "并发控制避免过载",
                    "监控内存使用情况"
                ]
            }
        ]
        
        for rec in recommendations:
            print(f"📋 {rec['类别']}:")
            for suggestion in rec['建议']:
                print(f"   • {suggestion}")
            print()
    
    async def test_configuration(self, config_name, test_url):
        """测试配置效果"""
        config = self.optimization_configs[config_name]["config"]
        
        try:
            async with async_playwright() as p:
                # 启动浏览器
                launch_args = {
                    "headless": config.get("headless", True),
                }
                
                if "args" in config:
                    launch_args["args"] = config["args"]
                
                browser = await p.chromium.launch(**launch_args)
                
                # 创建上下文
                context_args = {}
                if "user_agent" in config:
                    context_args["user_agent"] = config["user_agent"]
                if "viewport" in config:
                    context_args["viewport"] = config["viewport"]
                if "extra_http_headers" in config:
                    context_args["extra_http_headers"] = config["extra_http_headers"]
                if "java_script_enabled" in config:
                    context_args["java_script_enabled"] = config["java_script_enabled"]
                if "bypass_csp" in config:
                    context_args["bypass_csp"] = config["bypass_csp"]
                if "ignore_https_errors" in config:
                    context_args["ignore_https_errors"] = config["ignore_https_errors"]
                
                context = await browser.new_context(**context_args)
                page = await context.new_page()
                
                # 访问页面
                start_time = time.time()
                await page.goto(
                    test_url, 
                    timeout=config.get("timeout", 30000),
                    wait_until=config.get("wait_until", "domcontentloaded")
                )
                
                # 等待加载完成
                if "wait_for_load_state" in config:
                    await page.wait_for_load_state(
                        config["wait_for_load_state"],
                        timeout=config.get("wait_for_load_state_timeout", 30000)
                    )
                
                load_time = time.time() - start_time
                
                # 获取页面信息
                title = await page.title()
                content_length = len(await page.content())
                
                await browser.close()
                
                return {
                    "success": True,
                    "load_time": load_time,
                    "title": title,
                    "content_length": content_length
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def generate_optimized_config(self):
        """生成优化后的配置"""
        print("="*60)
        print("推荐的优化配置")
        print("="*60)
        
        optimized_config = """
# 优化后的Playwright配置

async def launch_browser_optimized(
    p,
    headless: bool = True,
    browser_type: str = "chromium",
    slow_mo: int = 0,
    proxy: Optional[Dict] = None,
    viewport: Dict[str, int] = {"width": 1920, "height": 1080},
    user_agent: Optional[str] = None,
    **launch_kwargs
):
    \"\"\"优化的浏览器启动函数\"\"\"
    
    # 默认User-Agent
    if not user_agent:
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    # 启动参数
    launch_args = {
        "headless": headless,
        "slow_mo": slow_mo,
        "args": [
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-dev-shm-usage",
            "--disable-accelerated-2d-canvas",
            "--no-first-run",
            "--no-zygote",
            "--disable-gpu",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI",
            "--disable-ipc-flooding-protection"
        ]
    }
    
    if proxy:
        launch_args["proxy"] = proxy
    
    browser = await getattr(p, browser_type).launch(**launch_args)
    
    # 上下文配置
    context = await browser.new_context(
        viewport=viewport,
        user_agent=user_agent,
        extra_http_headers={
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
        },
        java_script_enabled=True,
        bypass_csp=True,
        ignore_https_errors=True
    )
    
    page = await context.new_page()
    
    return browser, context, page

# 优化的页面访问函数
async def visit_page_optimized(page, url, max_retries=3):
    \"\"\"优化的页面访问函数\"\"\"
    
    for attempt in range(max_retries):
        try:
            # 随机延迟
            if attempt > 0:
                delay = random.uniform(1, 3) * (attempt + 1)
                await asyncio.sleep(delay)
            
            # 访问页面
            await page.goto(
                url, 
                timeout=90000,  # 增加超时时间
                wait_until="networkidle"  # 等待网络空闲
            )
            
            # 额外等待确保内容加载
            await page.wait_for_load_state("networkidle", timeout=45000)
            
            # 检查页面是否正常加载
            title = await page.title()
            if not title or "error" in title.lower() or "404" in title:
                raise Exception(f"页面加载异常: {title}")
            
            return True
            
        except Exception as e:
            print(f"第 {attempt + 1} 次尝试失败: {e}")
            if attempt == max_retries - 1:
                raise e
    
    return False
"""
        
        print(optimized_config)


def main():
    """主函数"""
    optimizer = PlaywrightOptimizer()
    
    # 分析失败原因
    optimizer.analyze_failure_reasons()
    
    # 提供优化建议
    optimizer.get_optimization_recommendations()
    
    # 生成优化配置
    optimizer.generate_optimized_config()
    
    print("="*60)
    print("总结")
    print("="*60)
    print("主要优化方向:")
    print("1. 增加超时时间和改进等待策略")
    print("2. 添加反爬虫绕过机制")
    print("3. 改进错误处理和重试逻辑")
    print("4. 优化选择器配置")
    print("5. 添加随机延迟和真实浏览器行为模拟")
    print()
    print("建议立即实施:")
    print("• 将超时时间从30秒增加到60-90秒")
    print("• 使用networkidle等待策略")
    print("• 添加完整的HTTP请求头")
    print("• 增加重试次数到3-5次")
    print("• 为微信公众号等特殊网站配置专门的处理策略")


if __name__ == "__main__":
    main()
