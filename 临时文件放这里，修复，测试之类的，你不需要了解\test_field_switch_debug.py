#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试字段配置开关问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_field_switch_debug():
    """调试字段配置开关"""
    print("=" * 60)
    print("调试字段配置开关")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 检查开关控件
        if not hasattr(window, 'enable_field_config_checkbox'):
            print("❌ 字段配置开关不存在")
            return False
        
        print(f"📋 开关初始状态: {window.enable_field_config_checkbox.isChecked()}")
        
        # 检查受控制的组件
        controlled_components = [
            ('field_config_area', '字段配置区域'),
            ('custom_fields_group', '自定义字段选择组'),
            ('field_preview_group', '字段预览组'),
            ('field_actions_group', '字段操作组')
        ]
        
        print("\n🔍 检查受控制的组件:")
        for attr_name, display_name in controlled_components:
            if hasattr(window, attr_name):
                component = getattr(window, attr_name)
                initial_state = component.isEnabled()
                print(f"   {display_name}: 存在, 初始状态: {'启用' if initial_state else '禁用'}")
            else:
                print(f"   {display_name}: ❌ 不存在")
        
        # 测试开关功能
        print("\n🧪 测试开关启用...")
        window.enable_field_config_checkbox.setChecked(True)
        
        print("   开关状态变更后的组件状态:")
        for attr_name, display_name in controlled_components:
            if hasattr(window, attr_name):
                component = getattr(window, attr_name)
                enabled_state = component.isEnabled()
                print(f"   {display_name}: {'✅ 启用' if enabled_state else '❌ 仍禁用'}")
        
        # 测试开关禁用
        print("\n🧪 测试开关禁用...")
        window.enable_field_config_checkbox.setChecked(False)
        
        print("   开关禁用后的组件状态:")
        for attr_name, display_name in controlled_components:
            if hasattr(window, attr_name):
                component = getattr(window, attr_name)
                disabled_state = not component.isEnabled()
                print(f"   {display_name}: {'✅ 禁用' if disabled_state else '❌ 仍启用'}")
        
        # 检查状态指示器
        if hasattr(window, 'field_config_status_label'):
            status_text = window.field_config_status_label.text()
            print(f"\n📊 状态指示器: {status_text}")
        
        # 检查字段复选框
        if hasattr(window, 'field_checkboxes'):
            checkbox_count = len(window.field_checkboxes)
            print(f"\n📝 字段复选框数量: {checkbox_count}")
            
            # 测试几个字段复选框的状态
            if checkbox_count > 0:
                print("   前几个字段复选框状态:")
                for i, (field_name, checkbox) in enumerate(list(window.field_checkboxes.items())[:3]):
                    is_enabled = checkbox.isEnabled()
                    print(f"   {field_name}: {'✅ 可用' if is_enabled else '❌ 禁用'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_enable():
    """手动测试启用功能"""
    print("=" * 60)
    print("手动测试启用功能")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 手动调用开关事件处理方法
        print("\n🔧 手动调用开关启用方法...")
        if hasattr(window, 'on_field_config_enabled_changed'):
            window.on_field_config_enabled_changed(Qt.Checked)
            print("   ✅ 开关启用方法调用成功")
            
            # 检查组件状态
            components_to_check = [
                ('field_config_area', '字段配置区域'),
                ('custom_fields_group', '自定义字段选择组'),
                ('field_preview_group', '字段预览组'),
                ('field_actions_group', '字段操作组')
            ]
            
            enabled_count = 0
            for attr_name, display_name in components_to_check:
                if hasattr(window, attr_name):
                    component = getattr(window, attr_name)
                    is_enabled = component.isEnabled()
                    print(f"   {display_name}: {'✅ 启用' if is_enabled else '❌ 禁用'}")
                    if is_enabled:
                        enabled_count += 1
                else:
                    print(f"   {display_name}: ⚠️ 不存在")
            
            print(f"\n📊 启用组件数量: {enabled_count}/{len(components_to_check)}")
            
            # 测试禁用
            print("\n🔧 手动调用开关禁用方法...")
            window.on_field_config_enabled_changed(Qt.Unchecked)
            print("   ✅ 开关禁用方法调用成功")
            
            disabled_count = 0
            for attr_name, display_name in components_to_check:
                if hasattr(window, attr_name):
                    component = getattr(window, attr_name)
                    is_disabled = not component.isEnabled()
                    print(f"   {display_name}: {'✅ 禁用' if is_disabled else '❌ 仍启用'}")
                    if is_disabled:
                        disabled_count += 1
                else:
                    print(f"   {display_name}: ⚠️ 不存在")
            
            print(f"\n📊 禁用组件数量: {disabled_count}/{len(components_to_check)}")
            
            return enabled_count > 0 and disabled_count > 0
        else:
            print("❌ 开关事件处理方法不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始调试字段配置开关问题")
    print()
    
    tests = [
        ("字段开关调试测试", test_field_switch_debug),
        ("手动启用功能测试", test_manual_enable),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("调试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed < total:
        print("\n🔧 可能的问题:")
        print("   1. 组件创建顺序问题")
        print("   2. 事件连接问题")
        print("   3. 组件引用问题")
        print("   4. 初始状态设置问题")

if __name__ == "__main__":
    main()
