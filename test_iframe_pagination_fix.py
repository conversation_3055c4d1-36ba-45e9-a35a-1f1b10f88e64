#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的iframe翻页支持
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from core.pagination_utils import PaginationUtils

async def test_iframe_pagination_context():
    """测试iframe翻页上下文获取"""
    print("🧪 测试iframe翻页上下文获取...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(test_url, wait_until='networkidle')
            print(f"✅ 成功访问页面")
            
            # 测试获取翻页上下文
            pagination_context = await PaginationUtils.get_pagination_context(page)
            
            if pagination_context == page:
                print(f"📄 翻页上下文: 主页面")
            else:
                print(f"🖼️ 翻页上下文: iframe")
                
                # 获取iframe信息
                iframes = await page.query_selector_all('iframe')
                for iframe in iframes:
                    frame = await iframe.content_frame()
                    if frame == pagination_context:
                        src = await iframe.get_attribute('src')
                        print(f"   iframe源: {src}")
                        break
            
            # 测试在正确上下文中查找翻页元素
            print(f"\n🔍 在正确上下文中查找翻页元素...")
            next_elements = await pagination_context.query_selector_all('a:has-text("下一页")')
            print(f"   找到 {len(next_elements)} 个'下一页'元素")
            
            goto_elements = await pagination_context.query_selector_all('a[href*="javascript:goto"]')
            print(f"   找到 {len(goto_elements)} 个'goto'元素")
            
            if next_elements:
                first_elem = next_elements[0]
                text = await first_elem.text_content()
                href = await first_elem.get_attribute('href')
                print(f"   第一个下一页元素: 文本='{text}', href='{href}'")
                
        except Exception as e:
            print(f"❌ 测试出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

async def test_iframe_pagination_analysis():
    """测试iframe翻页结构分析"""
    print("\n🧪 测试iframe翻页结构分析...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(test_url, wait_until='networkidle')
            
            # 测试翻页结构分析
            pagination_info = await PaginationUtils.analyze_pagination_structure(page, 1)
            
            print(f"📊 翻页结构分析结果:")
            print(f"   - 下一页链接: {len(pagination_info.get('nextPageLinks', []))} 个")
            for link in pagination_info.get('nextPageLinks', []):
                print(f"     文本: '{link['text']}', 禁用: {link['disabled']}")
            
            print(f"   - 页码链接: {len(pagination_info.get('pageNumbers', []))} 个")
            for page_info in pagination_info.get('pageNumbers', []):
                print(f"     页码: {page_info['pageNum']}")
            
            print(f"   - JavaScript函数: {pagination_info.get('jsFunction', 'None')}")
            print(f"   - 总页数: {pagination_info.get('totalPages', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ 测试出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

async def test_iframe_smart_pagination():
    """测试iframe智能翻页"""
    print("\n🧪 测试iframe智能翻页...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(test_url, wait_until='networkidle')
            print(f"✅ 成功访问页面")
            
            # 记录初始URL
            initial_url = page.url
            print(f"   初始URL: {initial_url}")
            
            # 测试智能翻页
            print(f"\n🚀 测试智能翻页: 第1页 → 第2页")
            success = await PaginationUtils.smart_pagination(
                page=page,
                current_page=1,
                include_jsp_selectors=True,
                wait_after_click=3000
            )
            
            if success:
                print(f"✅ 智能翻页成功！")
                
                # 检查URL是否变化
                current_url = page.url
                print(f"   当前URL: {current_url}")
                
                if current_url != initial_url:
                    print(f"   ✅ URL已变化，翻页成功")
                else:
                    print(f"   ⚠️ URL未变化，可能是iframe内翻页")
                
                # 再次测试翻页
                print(f"\n🚀 测试第二次翻页: 第2页 → 第3页")
                success_2 = await PaginationUtils.smart_pagination(
                    page=page,
                    current_page=2,
                    include_jsp_selectors=True,
                    wait_after_click=3000
                )
                
                if success_2:
                    print(f"✅ 第二次翻页也成功！")
                else:
                    print(f"❌ 第二次翻页失败")
                
            else:
                print(f"❌ 智能翻页失败")
                
        except Exception as e:
            print(f"❌ 测试出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

async def test_iframe_javascript_pagination():
    """测试iframe中的JavaScript翻页"""
    print("\n🧪 测试iframe中的JavaScript翻页...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(test_url, wait_until='networkidle')
            
            # 测试JavaScript翻页
            print(f"🔧 测试JavaScript翻页: goto(2)")
            success = await PaginationUtils.javascript_pagination(
                page=page,
                current_page=1,
                js_function='goto'
            )
            
            if success:
                print(f"✅ JavaScript翻页成功！")
                await page.wait_for_timeout(2000)
                
                # 测试第二次JavaScript翻页
                print(f"🔧 测试第二次JavaScript翻页: goto(3)")
                success_2 = await PaginationUtils.javascript_pagination(
                    page=page,
                    current_page=2,
                    js_function='goto'
                )
                
                if success_2:
                    print(f"✅ 第二次JavaScript翻页也成功！")
                else:
                    print(f"❌ 第二次JavaScript翻页失败")
                    
            else:
                print(f"❌ JavaScript翻页失败")
                
        except Exception as e:
            print(f"❌ 测试出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

async def main():
    """主测试函数"""
    print("🚀 开始iframe翻页修复测试...")
    
    # 运行所有测试
    await test_iframe_pagination_context()
    await test_iframe_pagination_analysis()
    await test_iframe_smart_pagination()
    await test_iframe_javascript_pagination()
    
    print("\n🎯 所有测试完成！")
    print("\n📋 修复总结:")
    print("✅ 添加了iframe翻页上下文检测")
    print("✅ 统一翻页工具现在支持iframe")
    print("✅ 智能翻页可以在iframe中工作")
    print("✅ JavaScript翻页支持iframe上下文")

if __name__ == "__main__":
    asyncio.run(main())
