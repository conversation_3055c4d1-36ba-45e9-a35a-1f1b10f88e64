#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Selenium Firefox驱动测试翻页功能（简化版，不自动下载驱动）
"""

import time
import sys
import os

def test_selenium_firefox_simple():
    """使用Selenium Firefox驱动测试翻页功能（简化版）"""
    print("🧪 使用Selenium Firefox驱动测试翻页功能（简化版）")
    print("=" * 60)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.firefox.options import Options
        
        print("✅ Selenium导入成功")
        
        # 配置Firefox选项
        firefox_options = Options()
        # 有头模式 - 不添加headless参数
        
        print("🚀 启动Firefox浏览器...")
        
        # 直接使用系统中的Firefox驱动
        try:
            driver = webdriver.Firefox(options=firefox_options)
        except Exception as e:
            print(f"❌ 无法启动Firefox: {e}")
            print("💡 请确保已安装Firefox浏览器和geckodriver")
            print("💡 或者尝试手动指定geckodriver路径")
            return False
        
        print("✅ Firefox浏览器启动成功")
        
        try:
            # 设置窗口大小
            driver.set_window_size(1920, 1080)
            
            url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
            print(f"📋 访问网站: {url}")
            
            driver.get(url)
            print("⏳ 等待页面加载...")
            time.sleep(10)  # 等待更长时间
            
            print("✅ 页面加载完成")
            print(f"📋 页面标题: {driver.title}")
            print(f"📋 当前URL: {driver.current_url}")
            
            # 滚动到页面底部，确保所有内容都加载
            print("📜 滚动到页面底部...")
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(5)
            
            # 检查页面源码中是否包含翻页相关内容
            print("\n🔍 检查页面源码...")
            page_source = driver.page_source
            
            pagination_keywords = ["下一页", "第1页", "第2页", "/12页", "确定", "1/12", "第1/12页"]
            found_in_source = []
            
            for keyword in pagination_keywords:
                if keyword in page_source:
                    found_in_source.append(keyword)
            
            if found_in_source:
                print(f"  ✅ 页面源码中找到翻页关键词: {found_in_source}")
                
                # 如果找到了翻页关键词，说明页面确实有翻页功能
                print("  🎉 确认页面有翻页功能！")
                
                # 查找具体的翻页按钮
                print("\n🔍 查找具体的翻页按钮...")
                
                # 查找所有input按钮
                input_buttons = driver.find_elements(By.TAG_NAME, "input")
                print(f"  📋 找到 {len(input_buttons)} 个input元素")
                
                for i, elem in enumerate(input_buttons):
                    try:
                        input_type = elem.get_attribute('type')
                        input_value = elem.get_attribute('value')
                        input_name = elem.get_attribute('name')
                        is_displayed = elem.is_displayed()
                        is_enabled = elem.is_enabled()
                        
                        print(f"    [{i+1}] type: '{input_type}', value: '{input_value}', name: '{input_name}', 可见: {is_displayed}, 可用: {is_enabled}")
                        
                        # 如果是翻页相关的按钮
                        if input_value and "下一页" in str(input_value):
                            print(f"        🎯 找到'下一页'按钮！")
                            
                            if is_displayed and is_enabled:
                                print(f"        🎯 按钮可用，尝试点击...")
                                
                                # 获取点击前的状态
                                current_url = driver.current_url
                                current_title = driver.title
                                
                                try:
                                    # 滚动到元素位置
                                    driver.execute_script("arguments[0].scrollIntoView(true);", elem)
                                    time.sleep(2)
                                    
                                    # 高亮显示元素（方便观察）
                                    driver.execute_script("arguments[0].style.border='3px solid red'", elem)
                                    time.sleep(1)
                                    
                                    # 点击元素
                                    elem.click()
                                    print("        ✅ 点击成功")
                                    
                                    # 等待页面变化
                                    time.sleep(8)
                                    
                                    # 检查变化
                                    new_url = driver.current_url
                                    new_title = driver.title
                                    new_source = driver.page_source
                                    
                                    print(f"        📋 点击前URL: {current_url}")
                                    print(f"        📋 点击后URL: {new_url}")
                                    print(f"        📋 点击前标题: {current_title}")
                                    print(f"        📋 点击后标题: {new_title}")
                                    
                                    # 检查是否翻页成功
                                    success_indicators = ["第2页", "2/12", "第2/12页"]
                                    翻页成功 = False
                                    
                                    for indicator in success_indicators:
                                        if indicator in new_source:
                                            print(f"        🎉 翻页成功！找到指示器: {indicator}")
                                            翻页成功 = True
                                            break
                                    
                                    if 翻页成功:
                                        # 截图保存成功状态
                                        driver.save_screenshot("firefox_pagination_success.png")
                                        print("        📸 已保存成功截图: firefox_pagination_success.png")
                                        
                                        print("        🎉 翻页功能测试成功！")
                                        return True
                                    else:
                                        print("        ⚠️ 点击了按钮但没有检测到翻页成功的迹象")
                                        
                                        # 截图保存当前状态
                                        driver.save_screenshot("firefox_after_click.png")
                                        print("        📸 已保存点击后截图: firefox_after_click.png")
                                    
                                except Exception as click_error:
                                    print(f"        ❌ 点击失败: {click_error}")
                            else:
                                print(f"        ⚠️ 按钮不可见或不可用")
                        
                    except Exception as e:
                        print(f"    [{i+1}] 获取元素信息失败: {e}")
                
            else:
                print("  ❌ 页面源码中未找到翻页关键词")
                print("  💡 这可能意味着:")
                print("     1. 页面确实没有翻页功能")
                print("     2. 翻页内容是通过JavaScript动态加载的")
                print("     3. 需要特定操作才能显示翻页")
            
            # 截图保存当前状态
            driver.save_screenshot("firefox_current_page.png")
            print(f"\n📸 已保存当前页面截图: firefox_current_page.png")
            
            print("\n📊 测试完成")
            
            # 保持浏览器打开，方便手动检查
            print("\n⏳ 保持Firefox浏览器打开60秒，请手动检查页面...")
            print("💡 您可以手动尝试点击翻页按钮，观察是否有效")
            print("💡 如果手动点击有效，请告诉我具体的操作步骤")
            time.sleep(60)
            
            return False
            
        finally:
            print("🔚 关闭Firefox浏览器")
            driver.quit()
            
    except ImportError as e:
        print(f"❌ 导入Selenium失败: {e}")
        print("💡 请安装Selenium: pip install selenium")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_selenium_firefox_simple()
    if success:
        print("🎉 Firefox翻页测试成功！")
    else:
        print("❌ Firefox翻页测试失败，但您可以查看截图了解页面状态")
        print("💡 如果您能手动成功翻页，请告诉我具体的操作步骤，我将据此修复动态翻页模块")
