#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面检测翻页按钮 - 包括iframe、动态加载等情况
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from core.crawler import launch_browser

async def comprehensive_pagination_detection():
    """全面检测翻页按钮"""
    print("🔍 全面检测翻页按钮")
    print("=" * 60)
    
    # 测试多个可能的URL
    test_urls = [
        "https://www.tjszx.gov.cn/tagz/taxd/index.shtml",
        "http://www.tjszx.gov.cn/tagz/taxd/index.shtml",
        "https://www.tjszx.gov.cn/tagz/taxd/",
        "http://www.tjszx.gov.cn/tagz/taxd/"
    ]
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            for url in test_urls:
                print(f"\n🎯 测试URL: {url}")
                
                try:
                    await page.goto(url, timeout=30000)
                    await page.wait_for_load_state('networkidle', timeout=15000)
                    
                    # 等待页面完全加载
                    await page.wait_for_timeout(5000)
                    
                    print("✅ 页面加载完成")
                    
                    # 1. 检查iframe
                    print("\n🔍 检查iframe...")
                    iframes = await page.query_selector_all("iframe")
                    if iframes:
                        print(f"  ✅ 找到 {len(iframes)} 个iframe")
                        for i, iframe in enumerate(iframes):
                            src = await iframe.get_attribute('src')
                            print(f"    [{i+1}] src: {src}")
                            
                            # 尝试访问iframe内容
                            try:
                                frame = await iframe.content_frame()
                                if frame:
                                    frame_content = await frame.content()
                                    if "下一页" in frame_content:
                                        print(f"    🎉 iframe {i+1} 中找到'下一页'!")
                            except Exception as e:
                                print(f"    ⚠️ 无法访问iframe {i+1}: {e}")
                    else:
                        print("  ❌ 未找到iframe")
                    
                    # 2. 等待可能的动态内容加载
                    print("\n⏳ 等待动态内容加载...")
                    await page.wait_for_timeout(3000)
                    
                    # 3. 尝试滚动到页面底部，可能触发翻页按钮显示
                    print("📜 滚动到页面底部...")
                    await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                    await page.wait_for_timeout(2000)
                    
                    # 4. 查找所有包含翻页相关文本的元素
                    print("\n🔍 查找翻页相关文本...")
                    page_content = await page.content()
                    
                    pagination_keywords = ["下一页", "next", "Next", "第", "页", "/", "确定"]
                    found_keywords = []
                    
                    for keyword in pagination_keywords:
                        if keyword in page_content:
                            found_keywords.append(keyword)
                    
                    if found_keywords:
                        print(f"  ✅ 找到翻页关键词: {found_keywords}")
                        
                        # 查找包含这些关键词的具体元素
                        all_elements = await page.query_selector_all("*")
                        
                        for element in all_elements:
                            try:
                                text = await element.text_content()
                                if text and any(keyword in text for keyword in ["下一页", "第1/12页", "确定"]):
                                    tag_name = await element.evaluate("el => el.tagName")
                                    class_name = await element.get_attribute('class')
                                    id_attr = await element.get_attribute('id')
                                    onclick = await element.get_attribute('onclick')
                                    href = await element.get_attribute('href')
                                    is_visible = await element.is_visible()
                                    
                                    print(f"  🎯 找到相关元素:")
                                    print(f"    文本: '{text.strip()[:50]}...'")
                                    print(f"    标签: {tag_name}")
                                    print(f"    class: '{class_name}'")
                                    print(f"    id: '{id_attr}'")
                                    print(f"    onclick: '{onclick}'")
                                    print(f"    href: '{href}'")
                                    print(f"    可见: {is_visible}")
                                    
                                    # 获取元素的HTML
                                    element_html = await element.evaluate("el => el.outerHTML")
                                    print(f"    HTML: {element_html[:200]}...")
                                    print()
                                    
                            except Exception:
                                continue
                    else:
                        print("  ❌ 未找到翻页关键词")
                    
                    # 5. 检查是否有表单提交
                    print("\n🔍 检查表单...")
                    forms = await page.query_selector_all("form")
                    if forms:
                        print(f"  ✅ 找到 {len(forms)} 个表单")
                        for i, form in enumerate(forms):
                            action = await form.get_attribute('action')
                            method = await form.get_attribute('method')
                            print(f"    [{i+1}] action: '{action}', method: '{method}'")
                            
                            # 查找表单中的输入元素
                            inputs = await form.query_selector_all("input")
                            for j, input_elem in enumerate(inputs):
                                input_type = await input_elem.get_attribute('type')
                                input_value = await input_elem.get_attribute('value')
                                input_name = await input_elem.get_attribute('name')
                                print(f"      input[{j+1}]: type='{input_type}', value='{input_value}', name='{input_name}'")
                    else:
                        print("  ❌ 未找到表单")
                    
                    # 6. 截图保存当前页面状态
                    screenshot_path = f"page_screenshot_{url.replace('://', '_').replace('/', '_')}.png"
                    await page.screenshot(path=screenshot_path)
                    print(f"📸 已保存截图: {screenshot_path}")
                    
                    # 如果找到了翻页相关内容，就不需要测试其他URL了
                    if found_keywords:
                        print("🎉 在此URL找到了翻页相关内容！")
                        break
                        
                except Exception as e:
                    print(f"❌ 测试URL {url} 失败: {e}")
                    continue
            
            print("\n📊 检测完成")
            
        except Exception as e:
            print(f"❌ 检测过程中出错: {e}")
            
        finally:
            # 保持浏览器打开，方便手动检查
            print("\n⏳ 保持浏览器打开30秒，请手动检查页面...")
            await page.wait_for_timeout(30000)
            
            await context.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(comprehensive_pagination_detection())
