#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试灵活字段配置系统
演示如何使用可配置的字段列表
"""

import asyncio
import os
import sys
import logging
import tempfile
import csv

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_field_config_manager():
    """测试字段配置管理器"""
    print("=" * 60)
    print("测试字段配置管理器")
    print("=" * 60)
    
    try:
        from core.field_config_manager import get_field_config_manager, apply_field_preset
        
        manager = get_field_config_manager()
        
        # 列出所有字段
        fields_info = manager.list_fields()
        print("📋 可用字段:")
        print(f"   默认字段: {fields_info['default_fields']}")
        print(f"   扩展字段: {fields_info['extended_fields']}")
        print(f"   预设方案: {fields_info['presets']}")
        
        # 测试预设
        presets = manager.get_field_presets()
        print(f"\n🎯 字段预设:")
        for preset_name, field_list in presets.items():
            print(f"   {preset_name}: {field_list}")
        
        # 应用基础预设
        print(f"\n✅ 应用基础预设...")
        success = apply_field_preset("basic")
        print(f"   结果: {'成功' if success else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_extractor():
    """测试字段提取器"""
    print("=" * 60)
    print("测试字段提取器")
    print("=" * 60)
    
    try:
        from core.field_extractor import get_field_extractor
        
        extractor = get_field_extractor()
        
        # 测试HTML内容
        test_html = """
        <html>
        <head><title>测试文章</title></head>
        <body>
            <h1 class="title">测试文章标题</h1>
            <div class="date">2024-01-01</div>
            <div class="author">测试作者</div>
            <div class="content">这是测试文章的内容...</div>
            <div class="like-count">123</div>
            <div class="view-count">456</div>
            <div class="price">￥99.99</div>
        </body>
        </html>
        """
        
        # 准备静态值
        static_values = {
            'articlelink': 'https://example.com/test',
            'content': '这是测试文章的内容...',
            'classid': 'test_class',
            'city': '北京'
        }
        
        print("🔍 提取字段值...")
        
        # 模拟异步提取（简化版）
        async def extract_test():
            values = await extractor.extract_fields(
                page=None,
                url='https://example.com/test',
                content_html=test_html,
                static_values=static_values
            )
            return values
        
        # 运行提取
        values = asyncio.run(extract_test())
        headers = extractor.get_headers()
        
        print(f"📊 提取结果:")
        for i, (header, value) in enumerate(zip(headers, values)):
            print(f"   {i+1}. {header}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_custom_fields():
    """测试自定义字段"""
    print("=" * 60)
    print("测试自定义字段")
    print("=" * 60)
    
    try:
        from core.field_config_manager import get_field_config_manager
        
        manager = get_field_config_manager()
        
        # 添加自定义字段
        custom_field = {
            "name": "自定义字段",
            "type": "text",
            "selectors": [".custom-field"],
            "required": False,
            "default": "默认值",
            "extractor": "text_extractor",
            "description": "这是一个自定义字段"
        }
        
        print("➕ 添加自定义字段...")
        success = manager.add_custom_field("custom_test", custom_field, save_to_file=False)
        print(f"   结果: {'成功' if success else '失败'}")
        
        # 创建自定义预设
        custom_preset = ["title", "articlelink", "content", "custom_test", "getdate"]
        print("🎯 创建自定义预设...")
        success = manager.create_preset("custom_preset", custom_preset, save_to_file=False)
        print(f"   结果: {'成功' if success else '失败'}")
        
        # 应用自定义预设
        print("✅ 应用自定义预设...")
        success = manager.apply_preset("custom_preset")
        print(f"   结果: {'成功' if success else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_extended_fields():
    """测试扩展字段"""
    print("=" * 60)
    print("测试扩展字段")
    print("=" * 60)
    
    try:
        from core.field_config_manager import apply_field_preset
        
        # 应用社交媒体预设（包含点赞、阅读量等）
        print("📱 应用社交媒体预设...")
        success = apply_field_preset("social_media")
        print(f"   结果: {'成功' if success else '失败'}")
        
        if success:
            from core.field_extractor import get_field_extractor
            extractor = get_field_extractor()
            headers = extractor.get_headers()
            print(f"   字段列表: {headers}")
        
        # 应用电商预设
        print("\n🛒 应用电商预设...")
        success = apply_field_preset("ecommerce")
        print(f"   结果: {'成功' if success else '失败'}")
        
        if success:
            from core.field_extractor import get_field_extractor
            extractor = get_field_extractor()
            headers = extractor.get_headers()
            print(f"   字段列表: {headers}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_csv_output():
    """测试CSV输出"""
    print("=" * 60)
    print("测试CSV输出")
    print("=" * 60)
    
    try:
        from core.field_config_manager import apply_field_preset
        from core.field_extractor import get_field_extractor
        
        # 应用综合预设
        print("📊 应用综合预设...")
        success = apply_field_preset("comprehensive")
        
        if not success:
            print("⚠️ 预设不存在，使用基础预设")
            apply_field_preset("basic")
        
        extractor = get_field_extractor()
        headers = extractor.get_headers()
        
        # 创建测试数据
        test_data = [
            ["2024-01-01", "测试来源1", "测试标题1", "https://example.com/1", "测试内容1", "class1", "北京", "100", "200", "50", "10", "99.99", "5", "4.5", "标签1,标签2", "新闻", "2024-01-01 12:00:00"],
            ["2024-01-02", "测试来源2", "测试标题2", "https://example.com/2", "测试内容2", "class2", "上海", "150", "300", "80", "15", "199.99", "8", "4.8", "标签3,标签4", "科技", "2024-01-02 12:00:00"]
        ]
        
        # 写入临时CSV文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8', newline='') as f:
            temp_file = f.name
            writer = csv.writer(f)
            writer.writerow(headers)  # 写入表头
            writer.writerows(test_data)  # 写入数据
        
        print(f"📁 已创建测试CSV文件: {temp_file}")
        print(f"📋 字段数量: {len(headers)}")
        print(f"📊 数据行数: {len(test_data)}")
        
        # 读取并显示前几行
        with open(temp_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()[:3]  # 读取前3行
            print("📄 文件内容预览:")
            for i, line in enumerate(lines):
                print(f"   {i+1}: {line.strip()}")
        
        # 清理临时文件
        os.unlink(temp_file)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试灵活字段配置系统")
    print()
    
    tests = [
        ("字段配置管理器测试", test_field_config_manager),
        ("字段提取器测试", test_field_extractor),
        ("自定义字段测试", test_custom_fields),
        ("扩展字段测试", test_extended_fields),
        ("CSV输出测试", test_csv_output),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！灵活字段系统工作正常。")
    else:
        print("⚠️ 部分测试失败，需要检查实现。")

if __name__ == "__main__":
    asyncio.run(main())
