# 🎯 GUI字段配置功能使用指南

## 📋 概述

GUI已成功集成灵活字段配置系统，用户可以通过图形界面轻松选择和配置需要采集的字段，支持点赞数、阅读量、价格、成交量等各种自定义字段。

## 🚀 功能位置

在主界面中，字段配置功能位于 **"字段配置"** 标签页中。

## 🔧 主要功能

### 1. 字段预设选择

#### 可用预设
- **基础字段 (8个)**: 标题、内容、链接等基本信息
- **社交媒体 (10个)**: 包含点赞数、阅读量、评论数等
- **电商字段 (8个)**: 包含价格、成交量、评分等
- **新闻字段 (9个)**: 包含分类、标签、阅读量等
- **博客字段 (8个)**: 包含字数、阅读时长等
- **综合字段 (17个)**: 包含所有常用字段

#### 使用方法
1. 在 **"字段预设"** 组中选择合适的预设
2. 查看预设说明了解包含的字段
3. 点击 **"应用预设"** 按钮

### 2. 自定义字段选择

#### 字段分类
- **基础字段**: 标题、内容、链接、日期、来源等
- **扩展字段**: 点赞数、阅读量、价格、成交量、评分、标签等

#### 使用方法
1. 在 **"自定义字段选择"** 组中勾选需要的字段
2. 使用 **"全选"** 或 **"清空"** 快速操作
3. 点击 **"应用自定义字段"** 按钮

### 3. 字段预览

#### 功能说明
- 实时显示当前选中的字段
- 显示字段数量统计
- 帮助确认配置是否正确

### 4. 配置管理

#### 可用操作
- **刷新字段列表**: 重新加载可用字段
- **重置为默认**: 恢复基础字段配置
- **导出配置**: 保存当前字段配置到文件
- **导入配置**: 从文件加载字段配置

## 📊 支持的字段类型

### 基础字段
| 字段名 | 中文名 | 说明 |
|--------|--------|------|
| title | 标题 | 文章标题 |
| articlelink | 文章链接 | 文章URL |
| content | 内容 | 文章正文 |
| dateget | 发布日期 | 文章发布日期 |
| source | 来源 | 文章来源或作者 |
| classid | 分类ID | 文章分类标识 |
| city | 城市 | 文章所属城市 |
| getdate | 采集时间 | 数据采集时间 |

### 扩展字段
| 字段名 | 中文名 | 说明 |
|--------|--------|------|
| likes | 点赞数 | 文章点赞数量 |
| views | 阅读量 | 文章阅读量 |
| comments | 评论数 | 文章评论数量 |
| shares | 分享数 | 文章分享次数 |
| price | 价格 | 商品或服务价格 |
| sales | 成交量 | 商品成交数量 |
| rating | 评分 | 文章或商品评分 |
| tags | 标签 | 文章标签列表 |
| category | 分类 | 文章分类 |
| word_count | 字数 | 文章字数统计 |

## 🎯 使用场景

### 场景1: 社交媒体内容采集
1. 选择 **"社交媒体"** 预设
2. 自动包含: 标题、内容、点赞数、阅读量、评论数、分享数等
3. 适用于微博、公众号、论坛等平台

### 场景2: 电商商品信息采集
1. 选择 **"电商字段"** 预设
2. 自动包含: 标题、内容、价格、成交量、评分、分类等
3. 适用于淘宝、京东、拼多多等电商平台

### 场景3: 新闻文章采集
1. 选择 **"新闻字段"** 预设
2. 自动包含: 标题、内容、日期、来源、分类、标签、阅读量等
3. 适用于新闻网站、政府网站等

### 场景4: 完全自定义
1. 在自定义字段选择中勾选需要的字段
2. 可以混合基础字段和扩展字段
3. 适用于特殊需求的采集任务

## 🔄 工作流程

### 1. 配置字段
```
选择预设 → 查看预览 → 应用配置
    ↓
自定义选择 → 勾选字段 → 应用配置
```

### 2. 开始爬取
```
配置其他参数 → 开始爬取 → 自动应用字段配置
```

### 3. 查看结果
```
生成的CSV/Excel文件将包含配置的字段列
```

## ⚙️ 高级功能

### 1. 配置导出/导入
- **导出**: 将当前字段配置保存为JSON文件
- **导入**: 从JSON文件加载字段配置
- **用途**: 在不同项目间共享配置，备份常用配置

### 2. 字段配置持久化
- GUI会记住最后使用的字段配置
- 重启应用后自动恢复上次的配置
- 支持多个配置组的保存和切换

### 3. 智能字段提取
- 系统会根据网页结构自动尝试多个选择器
- 支持数字、货币、日期等特殊类型的智能提取
- 提供默认值和错误处理机制

## 🚨 注意事项

### 1. 字段选择器
- 不同网站的HTML结构不同，某些字段可能无法提取
- 建议先用少量数据测试字段提取效果
- 可以在模组配置中自定义选择器

### 2. 性能考虑
- 字段越多，采集速度可能越慢
- 对于大量数据，建议只选择必要的字段
- 扩展字段的提取可能需要额外的网络请求

### 3. 数据质量
- 某些字段可能在部分页面中不存在
- 系统会提供默认值，但建议检查数据完整性
- 定期验证提取的数据是否符合预期

## 💡 最佳实践

### 1. 预设优先
- 优先使用预设配置，它们经过优化和测试
- 只在预设不满足需求时才使用自定义配置

### 2. 渐进式配置
- 从基础字段开始，逐步添加扩展字段
- 每次添加新字段后都要测试提取效果

### 3. 配置管理
- 为不同类型的网站创建不同的字段配置
- 定期导出重要的配置作为备份
- 在团队中共享有效的字段配置

### 4. 测试验证
- 在正式采集前，先用小量数据测试
- 检查生成的CSV/Excel文件中的字段是否正确
- 验证数据类型和格式是否符合预期

## 🎉 总结

GUI字段配置功能让您能够：

1. **快速配置**: 使用预设快速适应不同场景
2. **灵活定制**: 自由选择需要的字段组合
3. **可视化管理**: 直观的界面操作，无需编程
4. **配置复用**: 导出/导入功能支持配置共享
5. **智能提取**: 自动处理不同数据类型的提取

这个功能大大提升了爬虫的灵活性和易用性，让您能够轻松应对各种数据采集需求。
