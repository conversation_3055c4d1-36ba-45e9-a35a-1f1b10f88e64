#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os

print("测试配置文件...")

config_file = "configs/app/config.json"
if os.path.exists(config_file):
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    if "珠海政协" in config:
        zhzx_config = config["珠海政协"]
        print(f"珠海政协配置: {zhzx_config}")
        
        if "content_selectors" in zhzx_config:
            print("✅ 使用 content_selectors")
        else:
            print("❌ 缺少 content_selectors")
    else:
        print("❌ 未找到珠海政协配置")
else:
    print("❌ 配置文件不存在")
