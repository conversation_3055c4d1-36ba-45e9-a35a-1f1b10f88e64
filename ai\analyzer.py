#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版AI选择器分析器
集成选择器测试模块，实现智能选择器分析和自动测试验证
"""

import asyncio
import logging
import json
import time
from typing import Dict, List, Optional, Tuple, Any
from openai import OpenAI
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup

# 导入相关模块
try:
    from core import crawler
    CRAWLER_AVAILABLE = True
except ImportError:
    CRAWLER_AVAILABLE = False


try:
    from testing.selectors_test import SelectorsTestManager
    SELECTORS_TEST_AVAILABLE = True
except (ImportError, SyntaxError) as e:
    print(f"⚠️ 选择器测试模块不可用: {e}")
    SELECTORS_TEST_AVAILABLE = False

    # 提供一个简单的替代类
    class SelectorsTestManager:
        def __init__(self):
            pass
        async def test_selectors_on_page(self, *args, **kwargs):
            return {"success": False, "error": "选择器测试模块不可用"}
    SELECTORS_TEST_AVAILABLE = True
except ImportError:
    SELECTORS_TEST_AVAILABLE = False

try:
    from core.field_config_manager import FieldConfigManager
    FIELD_CONFIG_AVAILABLE = True
except ImportError:
    FIELD_CONFIG_AVAILABLE = False

MODULES_AVAILABLE = CRAWLER_AVAILABLE and SELECTORS_TEST_AVAILABLE

# AI配置管理
def load_ai_config():
    """加载AI配置"""
    try:
        import json
        import os
        config_file = "configs/ai/llm_config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        logger.warning(f"加载AI配置失败: {e}")

    # 默认配置
    return {
        "api_key": "sk-0f5f39860c914aec9d5a96e8e667b2b0",
        "base_url": "https://api.deepseek.com/v1",
        "model": "deepseek-chat",
        "temperature": 0.3,
        "max_tokens": 1500,
        "enable_ai": True
    }

# 加载配置
AI_CONFIG = load_ai_config()

# 初始化OpenAI客户端
def get_openai_client():
    """获取OpenAI客户端"""
    try:
        config = load_ai_config()
        if not config.get('enable_ai', True):
            logger.warning("AI功能已禁用")
            return None

        api_key = config.get('api_key')
        if not api_key:
            logger.error("AI API密钥未配置")
            return None

        return OpenAI(api_key=api_key, base_url=config['base_url'])
    except Exception as e:
        logger.error(f"初始化OpenAI客户端失败: {e}")
        return None

client = get_openai_client()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('AIAnalyzer')


class AIAnalyzerWithTesting:
    """AI选择器分析器，集成测试验证功能"""

    def __init__(self):
        """初始化AI分析器"""
        # 安全初始化测试管理器
        self.test_manager = None
        if SELECTORS_TEST_AVAILABLE:
            try:
                self.test_manager = SelectorsTestManager()
                logger.info("选择器测试管理器初始化成功")
            except Exception as e:
                logger.warning(f"选择器测试管理器初始化失败: {e}")
        else:
            logger.warning("选择器测试模块不可用，将使用基础测试功能")

        # 初始化字段配置管理器
        self.field_config_manager = None
        if FIELD_CONFIG_AVAILABLE:
            try:
                self.field_config_manager = FieldConfigManager()
                logger.info("字段配置管理器初始化成功")
            except Exception as e:
                logger.warning(f"字段配置管理器初始化失败: {e}")
        else:
            logger.warning("字段配置模块不可用")

        self.retry_strategies = [
            "li a",
            ".article-item a",
            ".news-item a",
            ".list-item a",
            "ul li a",
            ".content a",
            "article a",
            ".post a",
            "div[class*='item'] a",
            "div[class*='list'] a"
        ]
    
    async def analyze_page_structure(self, url: str) -> Dict[str, Any]:
        """分析页面结构，获取HTML内容"""
        logger.info(f"开始分析页面结构: {url}")
        
        async with async_playwright() as p:
            browser, context, page = await crawler.launch_browser(
                p, headless=True, browser_type="chromium"
            )
            
            try:
                # 导入健壮等待策略
                try:
                    from utils.robust_wait_strategy import robust_goto
                    success = await robust_goto(page, url)
                    if not success:
                        raise Exception("页面访问失败")
                except ImportError:
                    # 回退到原有方式
                    await page.goto(url, timeout=60000, wait_until="networkidle")
                    await page.wait_for_load_state('networkidle', timeout=30000)
                    await asyncio.sleep(3)
                
                page_title = await page.title()
                page_html = await page.content()
                
                logger.info(f"页面分析完成: {page_title}")
                
                return {
                    'url': url,
                    'title': page_title,
                    'html': page_html,
                    'cleaned_html': self._clean_html(page_html)
                }
                
            finally:
                await context.close()
                await browser.close()
    
    def _clean_html(self, html: str) -> str:
        """清理HTML内容，减少token使用量"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # 移除不需要的元素
        for tag in ['script', 'style', 'meta', 'link', 'noscript', 'iframe']:
            for element in soup.find_all(tag):
                element.decompose()
        
        # 移除注释
        from bs4 import Comment
        for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()
        
        # 限制长度
        cleaned = str(soup)
        if len(cleaned) > 50000:  # 限制HTML长度
            cleaned = cleaned[:50000] + "..."
        
        return cleaned

    def get_field_selectors_for_analysis(self, field_preset: str = None, custom_fields: List[str] = None) -> Dict[str, List[str]]:
        """根据字段配置获取选择器用于AI分析"""
        if not self.field_config_manager:
            # 返回默认选择器
            return {
                'title_selectors': ['h1', '.title', '.article-title', 'h2', '.news-title'],
                'content_selectors': ['.content', '.article-content', '.main-text'],
                'date_selectors': ['.date', '.publish-date', '.article-date', 'time', '.time'],
                'source_selectors': ['.source', '.author', '.publisher', '.from']
            }

        try:
            # 获取字段列表
            if field_preset:
                fields = self.field_config_manager.get_preset_fields(field_preset)
            elif custom_fields:
                fields = custom_fields
            else:
                fields = ['title', 'content', 'dateget', 'source']

            # 构建选择器映射
            selectors_map = {}
            field_mapping = {
                'title': 'title_selectors',
                'content': 'content_selectors',
                'dateget': 'date_selectors',
                'source': 'source_selectors'
            }

            for field in fields:
                if field in field_mapping:
                    selector_key = field_mapping[field]
                    field_config = self.field_config_manager.get_field_config(field)
                    if field_config and 'selectors' in field_config:
                        selectors_map[selector_key] = field_config['selectors']

            # 确保基本字段存在
            for key, default_selectors in {
                'title_selectors': ['h1', '.title', '.article-title'],
                'content_selectors': ['.content', '.article-content'],
                'date_selectors': ['.date', '.publish-date'],
                'source_selectors': ['.source', '.author']
            }.items():
                if key not in selectors_map:
                    selectors_map[key] = default_selectors

            return selectors_map

        except Exception as e:
            logger.warning(f"获取字段选择器失败: {e}")
            # 返回默认选择器
            return {
                'title_selectors': ['h1', '.title', '.article-title'],
                'content_selectors': ['.content', '.article-content'],
                'date_selectors': ['.date', '.publish-date'],
                'source_selectors': ['.source', '.author']
            }

    async def analyze_list_page_selectors(self, url: str) -> Dict[str, Any]:
        """第一步：分析列表页选择器，找到文章链接"""
        logger.info(f"步骤1: 分析列表页选择器 - {url}")
        
        # 获取页面结构
        page_data = await self.analyze_page_structure(url)
        
        # AI分析列表页选择器
        ai_result = await self._ai_analyze_list_selectors(page_data)
        
        # 解析AI结果
        selectors = self._parse_ai_result(ai_result)
        
        # 测试列表页选择器
        test_results = await self._test_list_selectors(url, selectors)
        
        return {
            'step': 'list_page_analysis',
            'url': url,
            'ai_analysis': ai_result,
            'selectors': selectors,
            'test_results': test_results,
            'success': test_results.get('links_found', 0) > 0
        }
    
    async def analyze_article_page_selectors(self, article_url: str) -> Dict[str, Any]:
        """第二步：分析文章页选择器，找到内容元素"""
        logger.info(f"步骤2: 分析文章页选择器 - {article_url}")
        
        # 获取页面结构
        page_data = await self.analyze_page_structure(article_url)
        
        # AI分析文章页选择器
        ai_result = await self._ai_analyze_article_selectors(page_data)
        
        # 解析AI结果
        selectors = self._parse_ai_result(ai_result)
        
        # 测试文章页选择器
        test_results = await self._test_article_selectors(article_url, selectors)
        
        return {
            'step': 'article_page_analysis',
            'url': article_url,
            'ai_analysis': ai_result,
            'selectors': selectors,
            'test_results': test_results,
            'success': test_results.get('success', False)
        }
    
    async def _ai_analyze_list_selectors(self, page_data: Dict) -> str:
        """AI分析列表页选择器"""
        if not client:
            raise RuntimeError("AI客户端未初始化，请检查配置")

        prompt = f"""
你是专业的网页选择器分析师。请分析以下列表页面，找出最有可能的前5个选择器组合。

页面标题: {page_data['title']}
页面URL: {page_data['url']}
页面HTML: {page_data['cleaned_html']}

请分析并输出以下选择器的前5个最可能的选项（按可能性排序）：

1. list_container_selector - 包含文章列表的主容器（如：ul, .news-list, .article-list）
2. article_item_selector - 单个文章项的选择器（如：li, .item, .article-item）

注意：
- list_container_selector 和 article_item_selector 应该是不同的选择器
- article_item_selector 应该是 list_container_selector 的子元素
- 避免重复的选择器组合

输出格式（JSON）：
{{
    "list_container_selector": [
        "选择器1",
        "选择器2", 
        "选择器3",
        "选择器4",
        "选择器5"
    ],
    "article_item_selector": [
        "选择器1",
        "选择器2",
        "选择器3", 
        "选择器4",
        "选择器5"
    ],
    "analysis": "简要分析页面结构特点"
}}

请确保 article_item_selector 能找到包含 href 属性的链接元素或其父元素。
"""

        try:
            config = load_ai_config()
            # 确保temperature在有效范围内 [0, 2]
            temperature = max(0.0, min(2.0, config.get('temperature', 0.3)))

            response = client.chat.completions.create(
                model=config.get('model', 'deepseek-chat'),
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature,
                max_tokens=config.get('max_tokens', 1000)
            )

            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"AI分析列表页选择器失败: {e}")
            raise
    
    async def _ai_analyze_article_selectors(self, page_data: Dict) -> str:
        """AI分析文章页选择器"""
        if not client:
            raise RuntimeError("AI客户端未初始化，请检查配置")

        prompt = f"""
你是专业的网页选择器分析师。请分析以下文章页面，找出最有可能的前5个选择器组合。

页面标题: {page_data['title']}
页面URL: {page_data['url']}
页面HTML: {page_data['cleaned_html']}

请分析并输出以下选择器的前5个最可能的选项（按可能性排序）：

1. title_selectors - 文章标题选择器
2. content_selectors - 文章内容选择器
3. date_selectors - 发布日期选择器
4. source_selectors - 文章来源选择器

输出格式（JSON）：
{{
    "title_selectors": [
        "选择器1",
        "选择器2",
        "选择器3",
        "选择器4",
        "选择器5"
    ],
    "content_selectors": [
        "选择器1",
        "选择器2",
        "选择器3",
        "选择器4",
        "选择器5"
    ],
    "date_selectors": [
        "选择器1",
        "选择器2",
        "选择器3",
        "选择器4",
        "选择器5"
    ],
    "source_selectors": [
        "选择器1",
        "选择器2",
        "选择器3",
        "选择器4",
        "选择器5"
    ],
    "analysis": "简要分析页面结构特点"
}}

请确保选择器能准确定位到相应的内容元素。
"""

        try:
            config = load_ai_config()
            # 确保temperature在有效范围内 [0, 2]
            temperature = max(0.0, min(2.0, config.get('temperature', 0.3)))

            response = client.chat.completions.create(
                model=config.get('model', 'deepseek-chat'),
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature,
                max_tokens=config.get('max_tokens', 1500)
            )

            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"AI分析文章页选择器失败: {e}")
            raise

    def _parse_ai_result(self, ai_result: str) -> Dict[str, List[str]]:
        """解析AI分析结果"""
        try:
            # 尝试解析JSON格式
            if '{' in ai_result and '}' in ai_result:
                json_start = ai_result.find('{')
                json_end = ai_result.rfind('}') + 1
                json_str = ai_result[json_start:json_end]
                return json.loads(json_str)
        except:
            pass

        # 如果JSON解析失败，使用文本解析
        selectors = {}
        lines = ai_result.split('\n')
        current_key = None

        for line in lines:
            line = line.strip()
            if '=' in line and not line.startswith('['):
                key, value = line.split('=', 1)
                selectors[key.strip()] = [value.strip()] if value.strip() else []
            elif line.startswith('"') and current_key:
                selector = line.strip('"').strip(',')
                if selector:
                    selectors[current_key].append(selector)

        return selectors

    async def _test_list_selectors(self, url: str, selectors: Dict) -> Dict[str, Any]:
        """测试列表页选择器，优先选择稳定的结果"""
        logger.info("测试列表页选择器...")

        list_containers = selectors.get('list_container_selector', ['body'])
        article_items = selectors.get('article_item_selector', ['a'])

        best_result = {'links_found': 0, 'selectors': {}, 'stable': False}
        stable_results = []  # 存储稳定的结果

        # 测试不同的选择器组合
        for container in list_containers[:5]:
            for item in article_items[:5]:
                try:
                    result = await self._test_link_extraction(url, container, item)
                    result['selectors'] = {
                        'list_container_selector': container,
                        'article_item_selector': item
                    }
                    
                    # 优先收集稳定的结果
                    if result.get('stable', False) and result['links_found'] > 0:
                        stable_results.append(result)
                        logger.info(f"✅ 找到稳定选择器: {container} + {item} = {result['links_found']} 个链接")
                        
                        # 找到第一个稳定结果就立即返回，不再测试其他组合
                        logger.info(f"🎯 找到稳定结果，停止测试其他选择器组合")
                        return result
                    
                    # 更新最佳结果
                    if result['links_found'] > best_result['links_found']:
                        best_result = result
                        
                except Exception as e:
                    logger.warning(f"测试选择器失败 {container} + {item}: {e}")

        # 如果没有找到稳定结果，返回最佳结果
        if best_result['links_found'] > 0:
            logger.info(f"⚠️ 未找到稳定结果，使用最佳结果: {best_result['links_found']} 个链接")
            return best_result

        # 如果没有找到链接，使用重试策略
        logger.info("使用重试策略寻找链接...")
        retry_result = await self._retry_link_extraction(url)
        if retry_result['links_found'] > 0:
            return retry_result

        return best_result

    async def _test_link_extraction(self, url: str, container: str, item: str) -> Dict[str, Any]:
        """测试链接提取，增加稳定性检测"""
        from collections import Counter
        
        async with async_playwright() as p:
            browser, context, page = await crawler.launch_browser(
                p, headless=True, browser_type="chromium"
            )

            try:
                link_counts = []
                all_results = []
                max_attempts = 5
                
                for attempt in range(max_attempts):
                    try:
                        _, _, article_links, _ = await crawler.get_article_links_playwright(
                            page, url, container, item
                        )
                        
                        link_count = len(article_links)
                        link_counts.append(link_count)
                        all_results.append(article_links)
                        
                        logger.info(f"测试选择器 {container} + {item} - 尝试 {attempt + 1}: {link_count} 个链接")
                        
                        # 连续3次结果相同且大于0，认为稳定
                        if len(link_counts) >= 3:
                            recent_counts = link_counts[-3:]
                            if len(set(recent_counts)) == 1 and recent_counts[0] > 0:
                                logger.info(f"✅ 选择器稳定: {recent_counts[0]} 个链接")
                                return {
                                    'links_found': recent_counts[0],
                                    'sample_links': all_results[-1][:3],
                                    'stable': True
                                }
                        
                    except Exception as e:
                        logger.warning(f"尝试 {attempt + 1} 失败: {e}")
                        link_counts.append(0)
                        all_results.append([])
                
                # 选择出现次数最多的结果
                if link_counts:
                    count_frequency = Counter(link_counts)
                    most_common_count = count_frequency.most_common(1)[0][0]
                    
                    # 找到第一个匹配最常见数量的结果
                    for i, count in enumerate(link_counts):
                        if count == most_common_count and count > 0:
                            logger.info(f"✅ 选择最稳定结果: {most_common_count} 个链接")
                            return {
                                'links_found': most_common_count,
                                'sample_links': all_results[i][:3],
                                'stable': True
                            }
                    
                    # 如果没有大于0的结果，返回最后一次尝试
                    logger.warning(f"⚠️ 选择器不稳定，返回最后结果: {link_counts[-1]} 个链接")
                    return {
                        'links_found': link_counts[-1],
                        'sample_links': all_results[-1][:3] if all_results[-1] else [],
                        'stable': False
                    }

                return {'links_found': 0, 'sample_links': [], 'stable': False}

            finally:
                await context.close()
                await browser.close()

    async def _retry_link_extraction(self, url: str) -> Dict[str, Any]:
        """重试机制：使用预定义策略寻找链接"""
        best_result = {'links_found': 0, 'selectors': {}}

        for strategy in self.retry_strategies:
            try:
                result = await self._test_link_extraction(url, "body", strategy)
                if result['links_found'] > best_result['links_found']:
                    best_result = result
                    best_result['selectors'] = {
                        'list_container_selector': 'body',
                        'article_item_selector': strategy
                    }

                    # 如果找到足够的链接，停止重试
                    if result['links_found'] >= 5:
                        break

            except Exception as e:
                logger.warning(f"重试策略失败 {strategy}: {e}")

        return best_result

    async def _test_article_selectors(self, url: str, selectors: Dict) -> Dict[str, Any]:
        """测试文章页选择器"""
        if not self.test_manager:
            logger.warning("测试管理器不可用，使用基础测试")
            return await self._basic_article_selector_test(url, selectors)

        logger.info("测试文章页选择器...")

        try:
            # 构建测试配置
            test_config = {
                'content_selectors': selectors.get('content_selectors', []),
                'title_selectors': selectors.get('title_selectors', []),
                'date_selectors': selectors.get('date_selectors', []),
                'source_selectors': selectors.get('source_selectors', [])
            }

            # 使用选择器测试模块进行测试
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                )
                page = await context.new_page()

                try:
                    result = await self.test_manager.test_selectors_on_page(page, url, test_config)

                    # 分析测试结果，找出最佳选择器组合
                    best_selectors = self._find_best_selectors(result, selectors)
                    result['best_selectors'] = best_selectors

                    return result

                finally:
                    await context.close()
                    await browser.close()

        except Exception as e:
            logger.error(f"高级选择器测试失败: {e}")
            return await self._basic_article_selector_test(url, selectors)

    async def _basic_article_selector_test(self, url: str, selectors: Dict) -> Dict[str, Any]:
        """基础文章页选择器测试（当高级测试不可用时使用）"""
        logger.info("使用基础文章页选择器测试...")

        try:
            if not CRAWLER_AVAILABLE:
                return {'success': False, 'error': 'crawler模块不可用'}

            async with async_playwright() as p:
                browser, context, page = await crawler.launch_browser(
                    p, headless=True, browser_type="chromium"
                )

                try:
                    # 使用健壮等待策略
                    try:
                        from utils.robust_wait_strategy import robust_goto
                        success = await robust_goto(page, url, timeout=30000, preferred_strategy="domcontentloaded")
                        if not success:
                            raise Exception("页面访问失败")
                    except ImportError:
                        # 回退到原有方式
                        await page.goto(url, timeout=30000, wait_until="domcontentloaded")
                        await page.wait_for_load_state('domcontentloaded', timeout=10000)

                    # 简单测试每个选择器是否能找到元素
                    extractions = {}

                    for field_type in ['title', 'content', 'date', 'source']:
                        selector_key = f"{field_type}_selectors"
                        selectors_list = selectors.get(selector_key, [])

                        for selector in selectors_list[:3]:  # 只测试前3个
                            try:
                                element = await page.query_selector(selector)
                                if element:
                                    text = await element.text_content()
                                    if text and text.strip():
                                        extractions[field_type] = {
                                            'selector': selector,
                                            'text': text.strip()[:100]  # 限制长度
                                        }
                                        break
                            except:
                                continue

                    return {
                        'success': bool(extractions.get('content')),
                        'extractions': extractions,
                        'test_type': 'basic'
                    }

                finally:
                    await context.close()
                    await browser.close()

        except Exception as e:
            logger.error(f"基础文章页选择器测试失败: {e}")
            return {'success': False, 'error': str(e)}

    def _find_best_selectors(self, test_result: Dict, ai_selectors: Dict) -> Dict[str, str]:
        """从测试结果中找出最佳选择器"""
        best_selectors = {}
        extractions = test_result.get('extractions', {})

        # 找出成功提取内容的选择器
        for field in ['content', 'title', 'date', 'source']:
            if field in extractions and extractions[field]:
                selector_key = f"{field}_selectors"
                best_selectors[selector_key] = [extractions[field]['selector']]
            elif f"{field}_selectors" in ai_selectors:
                # 如果测试失败，使用AI推荐的第一个选择器
                best_selectors[selector_key] = ai_selectors[f"{field}_selectors"][:1]

        return best_selectors

    async def full_analysis_with_testing(self, list_url: str) -> Dict[str, Any]:
        """完整的两步分析和测试流程"""
        logger.info(f"开始完整分析流程: {list_url}")

        results = {
            'list_url': list_url,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'step1_list_analysis': None,
            'step2_article_analysis': None,
            'final_config': None,
            'success': False
        }

        try:
            # 步骤1：分析列表页
            step1_result = await self.analyze_list_page_selectors(list_url)
            results['step1_list_analysis'] = step1_result

            if not step1_result['success']:
                results['error'] = '步骤1失败：无法找到有效的文章链接'
                return results

            # 获取第一个文章链接
            sample_links = step1_result['test_results'].get('sample_links', [])
            if not sample_links:
                results['error'] = '步骤1失败：未找到示例文章链接'
                return results

            # 构建完整的文章URL
            article_url = crawler.get_full_link(
                sample_links[0], list_url, list_url, "absolute"
            )

            if not article_url:
                results['error'] = '步骤1失败：无法构建有效的文章URL'
                return results

            # 步骤2：分析文章页
            step2_result = await self.analyze_article_page_selectors(article_url)
            results['step2_article_analysis'] = step2_result

            if step2_result['success']:
                # 生成最终配置
                results['final_config'] = self._generate_final_config(
                    step1_result, step2_result
                )
                results['success'] = True
            else:
                results['error'] = '步骤2失败：文章页选择器测试未通过'

        except Exception as e:
            results['error'] = f'分析过程异常: {str(e)}'
            logger.error(f"完整分析失败: {e}")

        return results

    async def analyze_with_field_config(self, list_url: str, field_preset: str = None,
                                      custom_fields: List[str] = None) -> Dict[str, Any]:
        """使用字段配置进行增强分析"""
        logger.info(f"开始字段配置增强分析: {list_url}")

        results = {
            'list_url': list_url,
            'field_preset': field_preset,
            'custom_fields': custom_fields,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'step1_list_analysis': None,
            'step2_article_analysis': None,
            'field_config_analysis': None,
            'final_config': None,
            'success': False
        }

        try:
            # 步骤1：分析列表页（与原有流程相同）
            step1_result = await self.analyze_list_page_selectors(list_url)
            results['step1_list_analysis'] = step1_result

            if not step1_result['success']:
                results['error'] = '步骤1失败：无法找到有效的文章链接'
                return results

            # 获取第一个文章链接
            sample_links = step1_result['test_results'].get('sample_links', [])
            if not sample_links:
                results['error'] = '步骤1失败：未找到示例文章链接'
                return results

            article_url = crawler.get_full_link(
                sample_links[0], list_url, list_url, "absolute"
            )

            if not article_url:
                results['error'] = '步骤1失败：无法构建有效的文章URL'
                return results

            # 步骤2：使用字段配置进行文章页分析
            field_selectors = self.get_field_selectors_for_analysis(field_preset, custom_fields)
            results['field_config_analysis'] = {
                'field_selectors': field_selectors,
                'preset_used': field_preset,
                'custom_fields_used': custom_fields
            }

            # 分析文章页（使用字段配置的选择器）
            step2_result = await self.analyze_article_page_selectors_with_config(
                article_url, field_selectors
            )
            results['step2_article_analysis'] = step2_result

            if step2_result['success']:
                # 生成最终配置
                results['final_config'] = self._generate_field_aware_config(
                    step1_result, step2_result, field_preset, custom_fields
                )
                results['success'] = True
            else:
                results['error'] = '步骤2失败：文章页选择器测试未通过'

        except Exception as e:
            results['error'] = f'字段配置分析过程异常: {str(e)}'
            logger.error(f"字段配置分析失败: {e}")

        return results

    async def analyze_article_page_selectors_with_config(self, url: str, field_selectors: Dict[str, List[str]]) -> Dict[str, Any]:
        """使用字段配置分析文章页选择器"""
        logger.info(f"使用字段配置分析文章页选择器 - {url}")

        # 使用字段配置的选择器进行测试
        test_results = await self._test_article_selectors(url, field_selectors)

        return {
            'step': 'article_page_analysis_with_config',
            'url': url,
            'field_selectors': field_selectors,
            'test_results': test_results,
            'success': test_results.get('success', False)
        }

    def _generate_field_aware_config(self, step1_result: Dict, step2_result: Dict,
                                   field_preset: str, custom_fields: List[str]) -> Dict[str, Any]:
        """生成字段感知的最终配置"""
        list_selectors = step1_result['test_results']['selectors']
        article_selectors = step2_result['test_results'].get('best_selectors', {})

        config = {
            'list_container_selector': list_selectors.get('list_container_selector'),
            'article_item_selector': list_selectors.get('article_item_selector'),
            'title_selectors': article_selectors.get('title_selectors', []),
            'content_selectors': article_selectors.get('content_selectors', []),
            'date_selectors': article_selectors.get('date_selectors', []),
            'source_selectors': article_selectors.get('source_selectors', []),
            'confidence_score': self._calculate_confidence_score(step1_result, step2_result),
            'field_config': {
                'preset': field_preset,
                'custom_fields': custom_fields,
                'field_config_used': True
            }
        }

        # 如果有字段配置管理器，添加扩展字段信息
        if self.field_config_manager and (field_preset or custom_fields):
            try:
                if field_preset:
                    fields = self.field_config_manager.get_preset_fields(field_preset)
                else:
                    fields = custom_fields or []

                config['field_config']['enabled_fields'] = fields
                config['field_config']['field_details'] = {}

                for field in fields:
                    field_config = self.field_config_manager.get_field_config(field)
                    if field_config:
                        config['field_config']['field_details'][field] = {
                            'name': field_config.get('name', field),
                            'type': field_config.get('type', 'text'),
                            'required': field_config.get('required', False)
                        }
            except Exception as e:
                logger.warning(f"添加字段配置信息失败: {e}")

        return config

    def _generate_final_config(self, step1_result: Dict, step2_result: Dict) -> Dict[str, Any]:
        """生成最终的配置"""
        list_selectors = step1_result['test_results']['selectors']
        article_selectors = step2_result['test_results'].get('best_selectors', {})

        return {
            'list_container_selector': list_selectors.get('list_container_selector'),
            'article_item_selector': list_selectors.get('article_item_selector'),
            'title_selectors': article_selectors.get('title_selectors', []),
            'content_selectors': article_selectors.get('content_selectors', []),
            'date_selectors': article_selectors.get('date_selectors', []),
            'source_selectors': article_selectors.get('source_selectors', []),
            'confidence_score': self._calculate_confidence_score(step1_result, step2_result)
        }

    def _calculate_confidence_score(self, step1_result: Dict, step2_result: Dict) -> float:
        """计算配置的置信度分数"""
        score = 0.0

        # 列表页分数 (40%)
        links_found = step1_result['test_results'].get('links_found', 0)
        if links_found > 0:
            score += min(0.4, links_found * 0.1)

        # 文章页分数 (60%)
        extractions = step2_result['test_results'].get('extractions', {})
        if extractions.get('content'):
            score += 0.3
        if extractions.get('title'):
            score += 0.15
        if extractions.get('date'):
            score += 0.075
        if extractions.get('source'):
            score += 0.075

        return min(1.0, score)

    def save_analysis_result(self, result: Dict, filename: str = None) -> str:
        """保存分析结果到文件"""
        if not filename:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"ai_analysis_{timestamp}.json"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            logger.info(f"分析结果已保存到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
            return None
