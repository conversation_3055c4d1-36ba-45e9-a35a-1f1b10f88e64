#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的JSP翻页逻辑
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from modules.jsp_website_handler import jsp_handler

async def test_jsp_pagination_analysis():
    """测试JSP翻页结构分析"""
    print("🧪 测试JSP翻页结构分析...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            # 访问测试页面
            await page.goto(test_url, wait_until='networkidle')
            print(f"✅ 成功访问测试页面: {test_url}")
            
            # 分析翻页结构
            pagination_info = await jsp_handler._analyze_pagination_structure(page, 1)
            
            print(f"\n📊 翻页结构分析结果:")
            print(f"   - 下一页链接: {len(pagination_info.get('nextPageLinks', []))} 个")
            for i, link in enumerate(pagination_info.get('nextPageLinks', [])):
                print(f"     [{i+1}] 文本: '{link['text']}', href: '{link['href']}', 禁用: {link['disabled']}")
            
            print(f"   - 页码链接: {len(pagination_info.get('pageNumbers', []))} 个")
            for page_info in pagination_info.get('pageNumbers', []):
                print(f"     页码: {page_info['pageNum']}, href: '{page_info['href']}'")
            
            print(f"   - JavaScript函数: {pagination_info.get('jsFunction', 'None')}")
            
            # 测试翻页功能
            print(f"\n🔄 测试翻页功能...")
            success = await jsp_handler._go_to_next_page(page, 1)
            
            if success:
                print(f"✅ 翻页成功！")
                
                # 等待页面加载
                await page.wait_for_timeout(2000)
                
                # 检查是否真的翻页了
                current_url = page.url
                print(f"   当前URL: {current_url}")
                
                # 再次分析翻页结构，看是否在第2页
                pagination_info_2 = await jsp_handler._analyze_pagination_structure(page, 2)
                print(f"   第2页的JavaScript函数: {pagination_info_2.get('jsFunction', 'None')}")
                
                # 尝试再翻一页
                print(f"\n🔄 测试从第2页翻到第3页...")
                success_2 = await jsp_handler._go_to_next_page(page, 2)
                
                if success_2:
                    print(f"✅ 第二次翻页也成功！")
                    await page.wait_for_timeout(2000)
                    print(f"   最终URL: {page.url}")
                else:
                    print(f"❌ 第二次翻页失败")
                
            else:
                print(f"❌ 翻页失败")
                
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

async def test_jsp_link_extraction():
    """测试修复后的JSP链接提取"""
    print("\n🧪 测试JSP链接提取...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            # 测试完整的翻页和链接收集流程
            article_links = await jsp_handler.handle_pagination(
                page=page,
                current_page=1,
                max_pages=3  # 测试3页
            )
            
            print(f"✅ 完整流程测试结果:")
            print(f"   - 总共收集到: {len(article_links)} 个链接")
            
            # 按页面分组显示
            page_groups = {}
            for link in article_links:
                page_num = link.get('page', 'unknown')
                if page_num not in page_groups:
                    page_groups[page_num] = []
                page_groups[page_num].append(link)
            
            for page_num in sorted(page_groups.keys()):
                links = page_groups[page_num]
                print(f"   - 第{page_num}页: {len(links)} 个链接")
                for i, link in enumerate(links[:3]):  # 只显示前3个
                    print(f"     [{i+1}] {link['text'][:30]}...")
                if len(links) > 3:
                    print(f"     ... 还有 {len(links) - 3} 个链接")
            
            # 检查是否有重复链接
            urls = [link['href'] for link in article_links]
            unique_urls = set(urls)
            if len(urls) != len(unique_urls):
                print(f"   ⚠️ 发现重复链接: 总计{len(urls)}个，去重后{len(unique_urls)}个")
            else:
                print(f"   ✅ 无重复链接")
                
        except Exception as e:
            print(f"❌ 链接提取测试出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

async def test_pagination_detection():
    """测试翻页检测逻辑"""
    print("\n🧪 测试翻页检测逻辑...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(test_url, wait_until='networkidle')
            
            # 检查页面中的翻页元素
            pagination_elements = await page.evaluate("""
                () => {
                    const elements = [];
                    
                    // 查找所有链接
                    document.querySelectorAll('a').forEach(link => {
                        const text = link.textContent.trim();
                        const href = link.getAttribute('href') || '';
                        const onclick = link.getAttribute('onclick') || '';
                        
                        if (text.includes('下一页') || text.includes('下页') || text === '>' || 
                            href.includes('javascript:goto') || onclick.includes('goto') ||
                            /^\\d+$/.test(text)) {
                            elements.push({
                                text: text,
                                href: href,
                                onclick: onclick,
                                className: link.className
                            });
                        }
                    });
                    
                    return elements;
                }
            """)
            
            print(f"📋 页面中的翻页相关元素:")
            for i, elem in enumerate(pagination_elements):
                print(f"   [{i+1}] 文本: '{elem['text']}', href: '{elem['href']}', onclick: '{elem['onclick']}', class: '{elem['className']}'")
            
            # 检查JavaScript函数
            js_functions = await page.evaluate("""
                () => {
                    const functions = {};
                    if (typeof goto === 'function') functions.goto = 'available';
                    if (typeof nextPage === 'function') functions.nextPage = 'available';
                    if (typeof goPage === 'function') functions.goPage = 'available';
                    if (typeof turnPage === 'function') functions.turnPage = 'available';
                    if (typeof page === 'function') functions.page = 'available';
                    return functions;
                }
            """)
            
            print(f"\n🔧 页面中的JavaScript翻页函数:")
            for func_name, status in js_functions.items():
                print(f"   - {func_name}: {status}")
            
            if not js_functions:
                print(f"   ❌ 未找到JavaScript翻页函数")
                
        except Exception as e:
            print(f"❌ 翻页检测测试出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

async def main():
    """主测试函数"""
    print("🚀 开始JSP翻页逻辑修复测试...")
    
    # 运行所有测试
    await test_pagination_detection()
    await test_jsp_pagination_analysis()
    await test_jsp_link_extraction()
    
    print("\n🎯 所有测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
