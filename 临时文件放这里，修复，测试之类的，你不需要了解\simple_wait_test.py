#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的等待策略测试
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from utils.robust_wait_strategy import robust_goto
import time

async def simple_test():
    """简化测试"""
    
    test_urls = [
        "https://www.baidu.com",
        "https://www.shrd.gov.cn/",
        "https://httpbin.org/html"
    ]
    
    print("🧪 简化的健壮等待策略测试")
    print("=" * 50)
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        )
        page = await context.new_page()
        
        for i, url in enumerate(test_urls, 1):
            print(f"\n📋 测试 {i}/{len(test_urls)}: {url}")
            print("-" * 30)
            
            start_time = time.time()
            
            try:
                success = await robust_goto(page, url)
                load_time = time.time() - start_time
                
                if success:
                    title = await page.title()
                    content_length = len(await page.content())
                    print(f"✅ 成功 - {load_time:.2f}s")
                    print(f"📄 标题: {title[:30]}...")
                    print(f"📊 内容: {content_length} 字符")
                else:
                    print(f"❌ 失败 - {load_time:.2f}s")
                    
            except Exception as e:
                load_time = time.time() - start_time
                print(f"❌ 异常 - {load_time:.2f}s: {e}")
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(simple_test())
