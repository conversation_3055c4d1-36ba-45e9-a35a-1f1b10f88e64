#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置加载和失败URL处理修复
"""

def test_config_loading():
    """测试配置加载"""
    print("🔍 测试配置加载...")
    
    try:
        # 测试config.json是否包含myconfig
        import json
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        if "myconfig" in config.get("groups", {}):
            print("✅ config.json 包含 myconfig 配置组")
        else:
            print("❌ config.json 不包含 myconfig 配置组")
            return False
        
        # 测试myconfig模块
        import myconfig
        config_manager = myconfig.ConfigManager()
        groups = config_manager.get_groups()
        
        if "myconfig" in groups:
            print("✅ myconfig 模块可以加载 myconfig 配置组")
        else:
            print("❌ myconfig 模块无法加载 myconfig 配置组")
            return False
        
        # 测试GUI配置管理器
        from gui_config_manager import GUIConfigManager
        gui_config = GUIConfigManager()
        gui_groups = gui_config.get_groups()
        
        if "myconfig" in gui_groups:
            print("✅ GUI配置管理器可以加载 myconfig 配置组")
            return True
        else:
            print("❌ GUI配置管理器无法加载 myconfig 配置组")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False

def test_failed_url_processing():
    """测试失败URL处理逻辑"""
    print("\n🔍 测试失败URL处理逻辑...")
    
    try:
        # 检查新的处理方法是否存在
        from crawler_gui_new import CrawlerGUI
        
        if hasattr(CrawlerGUI, 'process_failed_urls_with_crawler'):
            print("✅ 新的失败URL处理方法存在")
        else:
            print("❌ 新的失败URL处理方法不存在")
            return False
        
        # 检查方法签名
        import inspect
        method = getattr(CrawlerGUI, 'process_failed_urls_with_crawler')
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())
        
        required_params = ['self', 'failed_file', 'save_dir', 'export_filename', 
                          'file_format', 'retry_count', 'retry_interval', 'max_workers',
                          'progress_callback', 'log_callback', 'stop_check']
        
        missing_params = [p for p in required_params if p not in params]
        if missing_params:
            print(f"❌ 缺少参数: {missing_params}")
            return False
        else:
            print("✅ 方法参数完整")
        
        # 检查是否支持Excel文件
        with open("crawler_gui_new.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        if "openpyxl" in content and "xlsx" in content:
            print("✅ 支持Excel文件处理")
            return True
        else:
            print("❌ 不支持Excel文件处理")
            return False
            
    except Exception as e:
        print(f"❌ 失败URL处理测试失败: {e}")
        return False

def test_file_dialog_excel_support():
    """测试文件对话框Excel支持"""
    print("\n🔍 测试文件对话框Excel支持...")
    
    try:
        with open("crawler_gui_new.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查文件对话框过滤器
        if "*.xlsx" in content and "Excel文件" in content:
            print("✅ 文件对话框支持Excel格式")
            return True
        else:
            print("❌ 文件对话框不支持Excel格式")
            return False
            
    except Exception as e:
        print(f"❌ 文件对话框测试失败: {e}")
        return False

def test_crawler_integration():
    """测试crawler集成"""
    print("\n🔍 测试crawler集成...")
    
    try:
        # 检查save_article函数是否存在
        from crawler import save_article
        print("✅ save_article 函数可导入")
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(save_article)
        params = list(sig.parameters.keys())
        
        required_params = ['link', 'title_selectors', 'content_selectors', 
                          'file_path', 'file_format', 'use_module_config']
        
        missing_params = [p for p in required_params if p not in params]
        if missing_params:
            print(f"❌ save_article 缺少参数: {missing_params}")
            return False
        else:
            print("✅ save_article 参数完整")
            return True
            
    except Exception as e:
        print(f"❌ crawler集成测试失败: {e}")
        return False

def create_test_excel_file():
    """创建测试Excel文件"""
    print("\n🔧 创建测试Excel文件...")
    
    try:
        import openpyxl
        
        wb = openpyxl.Workbook()
        ws = wb.active
        
        # 添加表头
        ws.append(["URL", "Title", "Status"])
        
        # 添加测试数据
        test_urls = [
            "https://httpbin.org/html",
            "https://httpbin.org/json",
            "https://example.com"
        ]
        
        for url in test_urls:
            ws.append([url, "Test Title", "Failed"])
        
        wb.save("test_failed_urls.xlsx")
        print("✅ 测试Excel文件创建成功: test_failed_urls.xlsx")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试Excel文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("配置加载和失败URL处理修复验证")
    print("=" * 50)
    
    tests = [
        ("配置加载测试", test_config_loading),
        ("失败URL处理逻辑", test_failed_url_processing),
        ("文件对话框Excel支持", test_file_dialog_excel_support),
        ("crawler集成测试", test_crawler_integration),
        ("创建测试Excel文件", create_test_excel_file),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！修复成功。")
        print("\n修复内容:")
        print("✅ config.json 包含 myconfig 配置组")
        print("✅ GUI可以正确加载配置组")
        print("✅ 失败URL处理改为遍历文件+crawler处理")
        print("✅ 支持Excel格式的失败文件")
        print("✅ 集成了完整的crawler处理逻辑")
        
        print("\n现在可以:")
        print("1. 在GUI中看到myconfig配置组")
        print("2. 处理Excel格式的失败URL文件")
        print("3. 使用当前GUI配置重新处理失败URL")
        print("4. 享受更灵活的失败URL处理逻辑")
        
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件")

if __name__ == "__main__":
    main()
