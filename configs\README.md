# 配置文件说明

## 📁 配置文件夹结构

```
configs/
├── app/                     # 应用配置
│   ├── config.json         # 主应用配置
│   └── myconfig.json       # 用户配置
├── modules/                 # 模组配置
│   └── module_configs.json # 模组配置文件
├── ai/                      # AI配置
│   └── llm_config.json     # LLM配置
├── crawler/                 # 爬虫配置
│   └── crawler_config.json # 爬虫配置
├── testing/                 # 测试配置
│   └── test_configs.json   # 测试配置
└── backup/                  # 配置备份
    └── *.backup            # 自动备份文件
```

## 📋 配置文件说明

### 应用配置 (app/)
- **config.json**: 主应用配置，包含基础设置
- **myconfig.json**: 用户个人配置，包含用户偏好设置

### 模组配置 (modules/)
- **module_configs.json**: 所有模组的配置信息

### AI配置 (ai/)
- **llm_config.json**: LLM API配置，包含API密钥和设置

### 爬虫配置 (crawler/)
- **crawler_config.json**: 爬虫相关配置，包含请求设置等

### 测试配置 (testing/)
- **test_configs.json**: 测试相关配置

### 备份配置 (backup/)
- 自动备份的配置文件，按时间戳命名

## 🔧 使用方式

### 导入统一配置管理器
```python
from config.unified_manager import unified_config_manager

# 获取应用配置
app_config = unified_config_manager.get_app_config()

# 获取模组配置
module_configs = unified_config_manager.get_module_configs()

# 保存配置
unified_config_manager.save_config('app', 'config.json', new_config)

# 备份配置
unified_config_manager.backup_config('app', 'config.json')
```

## 📝 注意事项

1. 所有配置文件都使用UTF-8编码
2. 配置文件格式为JSON
3. 修改配置前会自动创建备份
4. 配置文件路径已统一管理，便于维护
