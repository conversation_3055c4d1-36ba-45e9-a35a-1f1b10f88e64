#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入分析天津市政协网站的翻页机制
"""

import asyncio
import sys
import os
import re

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from core.crawler import launch_browser

async def detailed_analysis():
    """详细分析网站翻页机制"""
    print("🔍 深入分析天津市政协网站翻页机制")
    print("=" * 60)
    
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            print(f"📋 访问网站: {url}")
            await page.goto(url, timeout=30000)
            await page.wait_for_load_state('networkidle', timeout=15000)
            
            # 1. 获取完整的HTML内容
            print("\n🔍 分析HTML结构...")
            html_content = await page.content()
            
            # 2. 查找所有可能的翻页相关代码
            print("\n🔍 查找翻页相关的JavaScript代码...")
            
            # 查找JavaScript函数
            js_function_patterns = [
                r'function\s+\w*[Pp]age\w*\s*\([^)]*\)\s*{[^}]*}',
                r'function\s+\w*[Nn]ext\w*\s*\([^)]*\)\s*{[^}]*}',
                r'function\s+\w*[Gg]o\w*\s*\([^)]*\)\s*{[^}]*}',
                r'onclick\s*=\s*["\'][^"\']*[Pp]age[^"\']*["\']',
                r'onclick\s*=\s*["\'][^"\']*[Nn]ext[^"\']*["\']',
                r'href\s*=\s*["\']javascript:[^"\']*["\']'
            ]
            
            for pattern in js_function_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
                if matches:
                    print(f"  ✅ 找到模式 '{pattern[:30]}...': {len(matches)} 个匹配")
                    for i, match in enumerate(matches[:2]):  # 只显示前2个
                        print(f"    [{i+1}] {match[:100]}...")
            
            # 3. 查找特定的翻页元素
            print("\n🔍 查找翻页相关元素...")
            
            # 检查是否有隐藏的翻页元素
            all_elements = await page.query_selector_all("*")
            pagination_keywords = ["page", "next", "prev", "翻页", "下一页", "上一页"]
            
            found_hidden_elements = []
            for element in all_elements[:100]:  # 限制检查前100个元素
                try:
                    # 获取元素的所有属性
                    element_html = await element.evaluate("el => el.outerHTML")
                    
                    # 检查是否包含翻页关键词
                    for keyword in pagination_keywords:
                        if keyword.lower() in element_html.lower():
                            is_visible = await element.is_visible()
                            if not is_visible:
                                found_hidden_elements.append({
                                    'html': element_html[:200],
                                    'keyword': keyword
                                })
                            break
                except:
                    continue
            
            if found_hidden_elements:
                print(f"  ✅ 找到 {len(found_hidden_elements)} 个隐藏的翻页相关元素:")
                for i, elem in enumerate(found_hidden_elements[:5]):
                    print(f"    [{i+1}] 关键词: {elem['keyword']}")
                    print(f"        HTML: {elem['html']}...")
            
            # 4. 检查页面是否有动态加载的内容
            print("\n🔍 检查动态内容加载...")
            
            # 等待一段时间看是否有新内容加载
            await page.wait_for_timeout(3000)
            
            # 检查页面中的文章列表
            article_selectors = [
                ".article",
                ".news",
                ".list",
                ".item",
                "li",
                ".content",
                "tr",
                "td"
            ]
            
            for selector in article_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements and len(elements) > 5:  # 如果找到较多元素
                        print(f"  ✅ 找到可能的文章列表: {selector} ({len(elements)} 个元素)")
                        
                        # 检查前几个元素的内容
                        for i, elem in enumerate(elements[:3]):
                            text = await elem.text_content()
                            if text and len(text.strip()) > 10:
                                print(f"    [{i+1}] {text.strip()[:50]}...")
                except:
                    continue
            
            # 5. 尝试滚动页面看是否有无限滚动
            print("\n🔍 测试无限滚动...")
            
            # 获取初始页面高度
            initial_height = await page.evaluate("document.body.scrollHeight")
            print(f"  📋 初始页面高度: {initial_height}px")
            
            # 滚动到页面底部
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(3000)
            
            # 检查页面高度是否改变
            new_height = await page.evaluate("document.body.scrollHeight")
            print(f"  📋 滚动后页面高度: {new_height}px")
            
            if new_height > initial_height:
                print("  ✅ 检测到无限滚动机制")
            else:
                print("  ❌ 未检测到无限滚动")
            
            # 6. 检查是否有iframe
            print("\n🔍 检查iframe...")
            iframes = await page.query_selector_all("iframe")
            if iframes:
                print(f"  ✅ 找到 {len(iframes)} 个iframe")
                for i, iframe in enumerate(iframes):
                    src = await iframe.get_attribute('src')
                    print(f"    [{i+1}] src: {src}")
            else:
                print("  ❌ 未找到iframe")
            
            # 7. 检查网络请求
            print("\n🔍 监听网络请求...")
            
            # 设置网络监听
            requests = []
            def handle_request(request):
                if any(keyword in request.url.lower() for keyword in ['page', 'list', 'data', 'ajax']):
                    requests.append({
                        'url': request.url,
                        'method': request.method
                    })
            
            page.on('request', handle_request)
            
            # 尝试触发一些可能的翻页操作
            try:
                # 尝试点击页面中的各种元素
                clickable_elements = await page.query_selector_all("a, button, .clickable, [onclick]")
                print(f"  📋 找到 {len(clickable_elements)} 个可点击元素")
                
                # 尝试点击前几个看起来像翻页的元素
                for element in clickable_elements[:5]:
                    try:
                        text = await element.text_content()
                        onclick = await element.get_attribute('onclick')
                        href = await element.get_attribute('href')
                        
                        if any(keyword in str(text).lower() for keyword in ['下一页', 'next', '更多', '>']):
                            print(f"    🎯 尝试点击: 文本='{text}' | onclick='{onclick}' | href='{href}'")
                            await element.click()
                            await page.wait_for_timeout(2000)
                            break
                    except:
                        continue
                        
            except Exception as e:
                print(f"  ⚠️ 点击测试失败: {e}")
            
            # 显示捕获的网络请求
            if requests:
                print(f"  ✅ 捕获到 {len(requests)} 个相关网络请求:")
                for req in requests[:5]:
                    print(f"    - {req['method']} {req['url']}")
            else:
                print("  ❌ 未捕获到相关网络请求")
            
            print("\n📊 详细分析完成")
            
        except Exception as e:
            print(f"❌ 分析过程中出错: {e}")
            
        finally:
            await context.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(detailed_analysis())
