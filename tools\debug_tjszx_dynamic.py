#!/usr/bin/env python3
"""
专门调试天津税务局网站的动态翻页元素
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.crawler import launch_browser
from playwright.async_api import async_playwright

async def debug_dynamic_pagination():
    """调试动态翻页元素"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("🔍 调试天津税务局网站动态翻页元素")
    print(f"📍 URL: {url}")
    
    async with async_playwright() as p:
        # 启动浏览器（可见模式）
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            # 监听网络请求
            requests = []
            def handle_request(request):
                requests.append({
                    'url': request.url,
                    'method': request.method,
                    'resource_type': request.resource_type
                })

            page.on('request', handle_request)

            # 访问页面
            print("🌐 正在访问页面...")
            await page.goto(url, wait_until='networkidle', timeout=30000)

            # 等待一段时间让JavaScript执行
            print("⏳ 等待JavaScript执行...")
            await page.wait_for_timeout(8000)  # 增加等待时间

            # 检查网络请求
            print(f"\n=== 🔍 检查网络请求 ({len(requests)}个) ===")
            js_requests = [r for r in requests if 'count_page_list' in r['url'] or r['resource_type'] == 'script']
            if js_requests:
                print("✅ 找到相关JavaScript请求:")
                for req in js_requests:
                    print(f"   {req['method']} {req['url']}")
            else:
                print("❌ 未找到翻页相关的JavaScript请求")

            # 显示所有JavaScript请求
            all_js = [r for r in requests if r['resource_type'] == 'script']
            if all_js:
                print(f"\n所有JavaScript请求 ({len(all_js)}个):")
                for req in all_js[-5:]:  # 显示最后5个
                    print(f"   {req['url']}")

            # 手动执行JavaScript来检查函数是否存在
            print("\n=== 🔍 检查JavaScript函数 ===")
            js_functions = await page.evaluate("""
                () => {
                    return {
                        nextpage: typeof nextpage !== 'undefined',
                        changepage: typeof changepage !== 'undefined',
                        window_nextpage: typeof window.nextpage !== 'undefined'
                    };
                }
            """)

            print(f"nextpage函数存在: {js_functions['nextpage']}")
            print(f"changepage函数存在: {js_functions['changepage']}")
            print(f"window.nextpage存在: {js_functions['window_nextpage']}")

            # 手动加载JavaScript文件
            print("\n=== 🔧 尝试手动加载JavaScript ===")
            try:
                js_url = "http://www.tjszx.gov.cn/tagz/system/count/0009003/000000000000/count_page_list_0009003000000000000.js"
                await page.evaluate(f"""
                    () => {{
                        return new Promise((resolve, reject) => {{
                            const script = document.createElement('script');
                            script.src = '{js_url}';
                            script.onload = () => resolve('loaded');
                            script.onerror = () => reject('failed');
                            document.head.appendChild(script);
                        }});
                    }}
                """)
                print("✅ JavaScript文件手动加载成功")

                # 再次等待
                await page.wait_for_timeout(3000)

            except Exception as e:
                print(f"❌ 手动加载JavaScript失败: {e}")
            
            # 检查pagetemple容器
            print("\n=== 🔍 检查pagetemple容器 ===")
            pagetemple_content = await page.evaluate("""
                () => {
                    const elem = document.getElementById('pagetemple');
                    return elem ? elem.innerHTML : null;
                }
            """)
            
            if pagetemple_content:
                print(f"✅ pagetemple容器存在，内容长度: {len(pagetemple_content)}")
                print(f"内容预览: {pagetemple_content[:300]}...")
                
                # 保存完整内容到文件
                with open('pagetemple_content.html', 'w', encoding='utf-8') as f:
                    f.write(pagetemple_content)
                print("💾 完整内容已保存到: pagetemple_content.html")
            else:
                print("❌ pagetemple容器不存在或为空")
            
            # 检查具体的翻页按钮
            print("\n=== 🔍 检查翻页按钮 ===")
            
            # 检查downpage按钮
            downpage_info = await page.evaluate("""
                () => {
                    const elem = document.getElementById('downpage');
                    if (elem) {
                        return {
                            exists: true,
                            text: elem.textContent,
                            href: elem.href,
                            onclick: elem.getAttribute('onclick'),
                            visible: elem.offsetParent !== null,
                            style: elem.getAttribute('style'),
                            className: elem.className
                        };
                    }
                    return { exists: false };
                }
            """)
            
            if downpage_info['exists']:
                print("✅ 找到#downpage按钮:")
                print(f"   文本: '{downpage_info['text']}'")
                print(f"   href: '{downpage_info['href']}'")
                print(f"   onclick: '{downpage_info['onclick']}'")
                print(f"   可见: {downpage_info['visible']}")
                print(f"   样式: {downpage_info['style']}")
                print(f"   类名: '{downpage_info['className']}'")
            else:
                print("❌ 未找到#downpage按钮")
            
            # 检查uppage按钮
            uppage_info = await page.evaluate("""
                () => {
                    const elem = document.getElementById('uppage');
                    if (elem) {
                        return {
                            exists: true,
                            text: elem.textContent,
                            href: elem.href,
                            onclick: elem.getAttribute('onclick'),
                            visible: elem.offsetParent !== null,
                            style: elem.getAttribute('style'),
                            className: elem.className
                        };
                    }
                    return { exists: false };
                }
            """)
            
            if uppage_info['exists']:
                print("✅ 找到#uppage按钮:")
                print(f"   文本: '{uppage_info['text']}'")
                print(f"   href: '{uppage_info['href']}'")
                print(f"   onclick: '{uppage_info['onclick']}'")
                print(f"   可见: {uppage_info['visible']}")
                print(f"   样式: {uppage_info['style']}")
                print(f"   类名: '{uppage_info['className']}'")
            else:
                print("❌ 未找到#uppage按钮")
            
            # 检查页码信息
            print("\n=== 🔍 检查页码信息 ===")
            page_info = await page.evaluate("""
                () => {
                    const current = document.getElementById('currentid');
                    const total = document.getElementById('allid');
                    return {
                        current: current ? current.textContent : null,
                        total: total ? total.textContent : null
                    };
                }
            """)
            
            if page_info['current'] and page_info['total']:
                print(f"✅ 页码信息: 第{page_info['current']}页 / 共{page_info['total']}页")
            else:
                print("❌ 未找到页码信息")
            
            # 尝试使用Playwright的选择器查找
            print("\n=== 🔍 使用Playwright选择器查找 ===")
            
            selectors_to_test = [
                '#downpage',
                '#uppage',
                'a.next',
                'a[onclick*="nextpage"]',
                '.pagination a',
                '#pagetemple a'
            ]
            
            for selector in selectors_to_test:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"✅ 找到元素: {selector} ({len(elements)}个)")
                        for i, elem in enumerate(elements):
                            text = await elem.text_content()
                            onclick = await elem.get_attribute('onclick')
                            is_visible = await elem.is_visible()
                            print(f"   [{i+1}] 文本: '{text}', onclick: '{onclick}', 可见: {is_visible}")
                    else:
                        print(f"❌ 未找到: {selector}")
                except Exception as e:
                    print(f"❌ 检查{selector}时出错: {e}")
            
            # 测试点击下一页按钮
            if downpage_info['exists']:
                print("\n=== 🧪 测试点击下一页按钮 ===")
                try:
                    # 记录点击前的URL
                    initial_url = page.url
                    print(f"点击前URL: {initial_url}")
                    
                    # 点击下一页按钮
                    await page.click('#downpage')
                    print("✅ 成功点击下一页按钮")
                    
                    # 等待页面变化
                    await page.wait_for_timeout(3000)
                    
                    # 检查URL变化
                    new_url = page.url
                    print(f"点击后URL: {new_url}")
                    
                    if new_url != initial_url:
                        print("🎉 URL已变化，翻页成功！")
                    else:
                        print("⚠️ URL未变化，可能是AJAX翻页")
                    
                    # 检查页码变化
                    new_page_info = await page.evaluate("""
                        () => {
                            const current = document.getElementById('currentid');
                            return current ? current.textContent : null;
                        }
                    """)
                    
                    if new_page_info:
                        print(f"当前页码: {new_page_info}")
                    
                except Exception as e:
                    print(f"❌ 点击测试失败: {e}")
            
            # 截图保存
            await page.screenshot(path='tjszx_dynamic_debug.png', full_page=True)
            print(f"\n📸 页面截图已保存: tjszx_dynamic_debug.png")
            
            # 关闭浏览器
            await context.close()
            await browser.close()
            
            print(f"\n🎯 动态翻页调试完成！")
            
        except Exception as e:
            print(f"❌ 调试过程中出错: {e}")
            await page.screenshot(path='tjszx_dynamic_error.png')
            try:
                await context.close()
                await browser.close()
            except:
                pass
            raise e

if __name__ == "__main__":
    print("天津税务局网站动态翻页调试工具")
    print("=" * 50)
    
    asyncio.run(debug_dynamic_pagination())
