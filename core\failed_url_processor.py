#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失败URL重试处理模块
用于处理失败的CSV文件，使用模组配置重新处理失败的URL
"""

import csv
import os
import logging
import time
import asyncio
from typing import List, Dict, Optional, Any
import pandas as pd

# 导入爬虫相关模块 - 延迟导入避免循环导入
try:
    from modules.manager import get_config_for_url, match_module_for_url
    CRAWLER_AVAILABLE = True
except ImportError as e:
    CRAWLER_AVAILABLE = False
    print(f"警告: 无法导入模组管理器: {e}")

# 延迟导入爬虫函数
def _import_crawler_functions():
    """延迟导入爬虫函数以避免循环导入"""
    try:
        from core.crawler import save_article_async, crawl_articles_async
        return save_article_async, crawl_articles_async
    except ImportError as e:
        print(f"警告: 无法导入爬虫函数: {e}")
        return None, None

logger = logging.getLogger(__name__)

class FailedUrlProcessor:
    """失败URL处理器"""

    def __init__(self):
        """初始化处理器"""
        self.processed_count = 0
        self.success_count = 0
        self.failed_count = 0
        self.log_callback = None
        self.progress_callback = None
        self.stop_flag = None
    
    def load_failed_urls(self, failed_csv_path: str) -> List[Dict[str, Any]]:
        """
        从失败文件中加载失败的URL（支持CSV和Excel）

        Args:
            failed_csv_path: 失败文件路径

        Returns:
            失败URL信息列表
        """
        failed_urls = []

        if not os.path.exists(failed_csv_path):
            if self.log_callback:
                self.log_callback(f"❌ 失败文件不存在: {failed_csv_path}")
            else:
                logger.error(f"失败文件不存在: {failed_csv_path}")
            return failed_urls

        try:
            if failed_csv_path.lower().endswith('.xlsx'):
                # 处理Excel文件
                import openpyxl
                wb = openpyxl.load_workbook(failed_csv_path)
                ws = wb.active

                # 查找URL列
                url_column = None
                for col in range(1, ws.max_column + 1):
                    cell_value = ws.cell(row=1, column=col).value
                    if cell_value and ('url' in str(cell_value).lower() or 'link' in str(cell_value).lower()):
                        url_column = col
                        break

                if url_column is None:
                    url_column = 1  # 默认第一列

                # 读取URL
                for row in range(2, ws.max_row + 1):
                    url = ws.cell(row=row, column=url_column).value
                    if url and str(url).strip():
                        failed_urls.append({
                            'failed_time': '',
                            'failed_url': str(url).strip(),
                            'title': '',
                            'reason': '重试处理',
                            'status': '待重试'
                        })
            else:
                # 处理CSV文件
                with open(failed_csv_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        # 查找URL列
                        url = None
                        for key in row.keys():
                            if 'url' in key.lower() or 'link' in key.lower():
                                url = row[key]
                                break

                        if not url and row:
                            # 如果没找到URL列，使用第一列
                            url = list(row.values())[0] if row else None

                        if url and url.strip():
                            failed_urls.append({
                                'failed_time': row.get('failed_time', ''),
                                'failed_url': url.strip(),
                                'title': row.get('title', ''),
                                'reason': row.get('reason', '重试处理'),
                                'status': row.get('status', '待重试')
                            })

            if self.log_callback:
                self.log_callback(f"📥 从 {failed_csv_path} 加载了 {len(failed_urls)} 个URL")
            else:
                logger.info(f"从 {failed_csv_path} 加载了 {len(failed_urls)} 个URL")
            return failed_urls

        except Exception as e:
            error_msg = f"加载失败文件时出错: {e}"
            if self.log_callback:
                self.log_callback(f"❌ {error_msg}")
            else:
                logger.error(error_msg)
            return failed_urls

    def convert_to_all_articles(self, failed_urls: List[Dict[str, Any]], save_dir: str, classid: str = "") -> List[tuple]:
        """
        将失败URL列表转换为 all_articles 格式

        Args:
            failed_urls: 失败URL信息列表
            save_dir: 保存目录
            classid: 分类ID

        Returns:
            all_articles 格式的列表: [(title, href, save_dir, page_title, page_url, classid), ...]
        """
        all_articles = []

        for url_info in failed_urls:
            url = url_info.get('failed_url', '')
            title = url_info.get('title', '') or '重试文章'

            if url and url.strip():
                # all_articles 格式: (title, href, save_dir, page_title, page_url, classid)
                all_articles.append((
                    title,           # title
                    url.strip(),     # href
                    save_dir,        # save_dir
                    title,           # page_title
                    url.strip(),     # page_url
                    classid          # classid
                ))

        return all_articles

    async def process_failed_urls_via_crawler(self, failed_csv_path: str, save_dir: str = "articles",
                                       export_filename: str = None, file_format: str = "CSV",
                                       classid: str = "", retry: int = 3, interval: float = 1.0,
                                       max_workers: int = 5, content_selectors: List[str] = None,
                                       title_selectors: List[str] = None, date_selectors: List[str] = None,
                                       source_selectors: List[str] = None, mode: str = "balance",
                                       collect_links: bool = True) -> Dict[str, int]:
        """
        通过 crawler.py 的 all_articles 路由处理失败URL（推荐方式）

        Args:
            failed_csv_path: 失败CSV文件路径
            save_dir: 保存目录
            export_filename: 导出文件名
            file_format: 文件格式
            classid: 分类ID
            retry: 重试次数
            interval: 重试间隔
            max_workers: 最大工作线程数
            content_selectors: 内容选择器列表
            title_selectors: 标题选择器列表
            date_selectors: 日期选择器列表
            source_selectors: 来源选择器列表
            mode: 爬取模式
            collect_links: 是否收集链接

        Returns:
            处理结果统计
        """
        # 使用回调函数记录日志
        def log(message):
            if self.log_callback:
                self.log_callback(message)
            else:
                logger.info(message)

        # 加载失败的URL
        log("🔄 开始加载失败URL文件...")
        failed_urls = self.load_failed_urls(failed_csv_path)

        if not failed_urls:
            log("⚠️ 没有需要重试的URL")
            return {"total": 0, "success": 0, "failed": 0}

        # 转换为 all_articles 格式
        log(f"🔄 转换 {len(failed_urls)} 个失败URL为 all_articles 格式...")
        all_articles = self.convert_to_all_articles(failed_urls, save_dir, classid)

        if not all_articles:
            log("⚠️ 没有有效的URL可以处理")
            return {"total": 0, "success": 0, "failed": 0}

        # 确保保存目录存在
        os.makedirs(save_dir, exist_ok=True)
        log(f"📁 保存目录: {save_dir}")

        # 导入爬虫函数
        save_article_async, crawl_articles_async = _import_crawler_functions()
        if crawl_articles_async is None:
            log("❌ 无法导入爬虫函数")
            return {"total": 0, "success": 0, "failed": 0}

        # 设置默认选择器
        if content_selectors is None:
            content_selectors = [
                ".article_cont",
                "div[class*='content']",
                "div[class*='article']",
                "div.view.TRS_UEDITOR.trs_paper_default.trs_web",
                ".TRS_Editor",
                "div.zhengwen"
            ]

        log(f"🚀 通过 crawler.py 统一路由处理 {len(all_articles)} 个失败URL...")

        # 路由给 crawler.py 统一处理
        try:
            result = await crawl_articles_async(
                all_articles=all_articles,
                content_selectors=content_selectors,
                title_selectors=title_selectors,
                date_selectors=date_selectors,
                source_selectors=source_selectors,
                mode=mode,
                collect_links=collect_links,
                export_filename=export_filename,
                file_format=file_format,
                retry=retry,
                interval=interval,
                max_workers=max_workers,
                log_callback=self.log_callback,
                progress_callback=self.progress_callback,
                use_module_config=True,  # 让 crawler.py 统一处理模组配置
                stop_check_callback=self.stop_flag
            )

            log(f"✅ 处理完成: 总计 {result.get('total', 0)}, 成功 {result.get('success', 0)}, 失败 {result.get('failed', 0)}")
            return result

        except Exception as e:
            error_msg = f"通过 crawler.py 路由处理失败URL时出错: {e}"
            log(f"❌ {error_msg}")
            return {"total": len(all_articles), "success": 0, "failed": len(all_articles), "error": str(e)}

    async def process_single_url(self, url_info: Dict[str, Any], save_dir: str,
                          export_filename: str = None, file_format: str = "CSV",
                          classid: str = "", retry: int = 3, interval: float = 1.0) -> bool:
        """
        处理单个失败的URL
        
        Args:
            url_info: URL信息字典
            save_dir: 保存目录
            export_filename: 导出文件名
            file_format: 文件格式
            classid: 分类ID
            retry: 重试次数
            interval: 重试间隔
            
        Returns:
            是否处理成功
        """
        if not CRAWLER_AVAILABLE:
            logger.error("爬虫模块不可用，无法处理URL")
            return False
        
        url = url_info.get('failed_url', '')
        title = url_info.get('title', '')
        
        if not url:
            logger.warning("URL为空，跳过处理")
            return False
        
        try:
            # 使用模组配置获取合适的配置
            module_config = get_config_for_url(url)
            module_name = match_module_for_url(url)
            
            if module_config:
                logger.info(f"使用模组配置 {module_name} 处理URL: {url}")
                
                # 从模组配置中获取选择器
                content_selectors = module_config.get('content_selectors', ['.content'])
                title_selectors = module_config.get('title_selectors', ['h1'])
                date_selectors = module_config.get('date_selectors', ['.date'])
                source_selectors = module_config.get('source_selectors', ['.source'])
                
                # 获取选择器类型
                content_type = module_config.get('content_type', 'CSS')
                title_selector_type = module_config.get('title_selector_type', 'CSS')
                date_selector_type = module_config.get('date_selector_type', 'CSS')
                source_selector_type = module_config.get('source_selector_type', 'CSS')
                
                # 获取其他设置
                mode = module_config.get('mode', 'balance')
                collect_links = module_config.get('collect_links', True)
                module_retry = module_config.get('retry', retry)
                module_interval = module_config.get('interval', interval)
                
            else:
                logger.warning(f"未找到匹配的模组配置，使用默认配置处理URL: {url}")
                # 使用默认配置
                content_selectors = ['.content', '.article-content', 'article']
                title_selectors = ['h1', '.title', '.article-title']
                date_selectors = ['.date', '.publish-time', '.time']
                source_selectors = ['.source', '.author', '.publisher']
                content_type = 'CSS'
                title_selector_type = 'CSS'
                date_selector_type = 'CSS'
                source_selector_type = 'CSS'
                mode = 'balance'
                collect_links = True
                module_retry = retry
                module_interval = interval
            
            # 调用save_article_async函数处理URL
            save_article_async, crawl_articles_async = _import_crawler_functions()
            if save_article_async is None:
                logger.error(f"无法导入爬虫函数，跳过URL: {url}")
                return False

            success = await save_article_async(
                link=url,
                save_dir=save_dir,
                page_title=title or "重试文章",
                content_selectors=content_selectors,
                title_selectors=title_selectors,
                date_selectors=date_selectors,
                source_selectors=source_selectors,
                content_type=content_type,
                collect_links=collect_links,
                mode=mode,
                export_filename=export_filename,
                classid=classid,
                file_format=file_format,
                retry=module_retry,
                interval=module_interval,
                use_module_config=False  # 已经手动应用了配置，避免重复应用
            )
            
            if success:
                logger.info(f"成功处理URL: {url}")
                return True
            else:
                logger.warning(f"处理URL失败: {url}")
                return False
                
        except Exception as e:
            logger.error(f"处理URL时出错 {url}: {e}")
            return False
    
    async def process_failed_urls(self, failed_csv_path: str, save_dir: str = "articles",
                           export_filename: str = None, file_format: str = "CSV",
                           classid: str = "", retry: int = 3, interval: float = 1.0,
                           max_workers: int = 5) -> Dict[str, int]:
        """
        批量处理失败的URL
        
        Args:
            failed_csv_path: 失败CSV文件路径
            save_dir: 保存目录
            export_filename: 导出文件名
            file_format: 文件格式
            classid: 分类ID
            retry: 重试次数
            interval: 重试间隔
            max_workers: 最大工作线程数
            
        Returns:
            处理结果统计
        """
        # 使用回调函数记录日志
        def log(message):
            if self.log_callback:
                self.log_callback(message)
            else:
                logger.info(message)

        # 加载失败的URL
        log("🔄 开始加载失败URL文件...")
        failed_urls = self.load_failed_urls(failed_csv_path)

        if not failed_urls:
            log("⚠️ 没有需要重试的URL")
            return {"total": 0, "success": 0, "failed": 0}

        # 确保保存目录存在
        os.makedirs(save_dir, exist_ok=True)
        log(f"📁 保存目录: {save_dir}")

        # 重置计数器
        self.processed_count = 0
        self.success_count = 0
        self.failed_count = 0

        log(f"📋 开始处理 {len(failed_urls)} 个失败的URL，使用 {max_workers} 个并发任务")

        # 使用异步并发处理
        import asyncio
        semaphore = asyncio.Semaphore(max_workers)

        async def process_with_semaphore(url_info):
            async with semaphore:
                return await self.process_single_url(
                    url_info,
                    save_dir,
                    export_filename,
                    file_format,
                    classid,
                    retry,
                    interval
                )

        # 创建所有任务
        tasks = []
        for url_info in failed_urls:
            # 检查停止标志
            if self.stop_flag and self.stop_flag():
                log("🛑 收到停止信号，终止处理")
                break

            task = asyncio.create_task(process_with_semaphore(url_info))
            tasks.append((task, url_info))

        # 处理完成的任务
        for task, url_info in tasks:
            # 检查停止标志
            if self.stop_flag and self.stop_flag():
                log("🛑 收到停止信号，终止处理")
                break

            try:
                success = await task
                if success:
                    self.success_count += 1
                    log(f"✅ 成功处理: {url_info.get('failed_url', '')}")
                else:
                    self.failed_count += 1
                    log(f"❌ 处理失败: {url_info.get('failed_url', '')}")

                # 更新进度
                if self.progress_callback:
                    self.progress_callback(self.processed_count, len(failed_urls))

                # 输出进度
                progress = (self.processed_count / len(failed_urls)) * 100
                log(f"📊 处理进度: {self.processed_count}/{len(failed_urls)} ({progress:.1f}%) "
                    f"成功: {self.success_count}, 失败: {self.failed_count}")

            except Exception as e:
                self.failed_count += 1
                log(f"❌ 处理URL时出现异常 {url_info.get('failed_url', '')}: {e}")
        
        # 更新失败文件状态
        self._update_failed_file_status(failed_csv_path, failed_urls)
        
        result = {
            "total": len(failed_urls),
            "success": self.success_count,
            "failed": self.failed_count,
            "stopped": self.stop_flag and self.stop_flag() if self.stop_flag else False
        }

        log(f"🎉 处理完成: 总计 {result['total']}, 成功 {result['success']}, 失败 {result['failed']}")
        return result
    
    def _update_failed_file_status(self, failed_csv_path: str, processed_urls: List[Dict[str, Any]]):
        """
        更新失败文件中的状态
        
        Args:
            failed_csv_path: 失败CSV文件路径
            processed_urls: 已处理的URL列表
        """
        try:
            # 读取原始文件
            df = pd.read_csv(failed_csv_path, encoding='utf-8')
            
            # 创建URL到处理结果的映射
            processed_urls_map = {url_info['failed_url']: url_info for url_info in processed_urls}
            
            # 更新状态
            for index, row in df.iterrows():
                url = row['failed_url']
                if url in processed_urls_map and row['status'] == '待重试':
                    # 这里简化处理，实际应该根据处理结果更新状态
                    df.at[index, 'status'] = '已重试'
            
            # 保存更新后的文件
            backup_path = failed_csv_path + '.backup'
            df.to_csv(backup_path, index=False, encoding='utf-8')
            logger.info(f"已备份原始失败文件到: {backup_path}")
            
            df.to_csv(failed_csv_path, index=False, encoding='utf-8')
            logger.info(f"已更新失败文件状态: {failed_csv_path}")
            
        except Exception as e:
            logger.error(f"更新失败文件状态时出错: {e}")


async def process_failed_csv_via_crawler(failed_csv_path: str, save_dir: str = "articles",
                                  export_filename: str = None, file_format: str = "CSV",
                                  classid: str = "", retry: int = 3, interval: float = 1.0,
                                  max_workers: int = 5, progress_callback=None,
                                  log_callback=None, stop_flag=None,
                                  content_selectors: List[str] = None,
                                  title_selectors: List[str] = None,
                                  date_selectors: List[str] = None,
                                  source_selectors: List[str] = None,
                                  mode: str = "balance",
                                  collect_links: bool = True) -> Dict[str, int]:
    """
    便捷函数：通过 crawler.py 路由处理失败的CSV文件（推荐方式）

    Args:
        failed_csv_path: 失败CSV文件路径
        save_dir: 保存目录
        export_filename: 导出文件名
        file_format: 文件格式
        classid: 分类ID
        retry: 重试次数
        interval: 重试间隔
        max_workers: 最大并发任务数
        progress_callback: 进度回调函数
        log_callback: 日志回调函数
        stop_flag: 停止标志检查函数
        content_selectors: 内容选择器列表
        title_selectors: 标题选择器列表
        date_selectors: 日期选择器列表
        source_selectors: 来源选择器列表
        mode: 爬取模式
        collect_links: 是否收集链接

    Returns:
        处理结果统计
    """
    processor = FailedUrlProcessor()

    # 设置回调函数
    if log_callback:
        processor.log_callback = log_callback
    if progress_callback:
        processor.progress_callback = progress_callback
    if stop_flag:
        processor.stop_flag = stop_flag

    return await processor.process_failed_urls_via_crawler(
        failed_csv_path=failed_csv_path,
        save_dir=save_dir,
        export_filename=export_filename,
        file_format=file_format,
        classid=classid,
        retry=retry,
        interval=interval,
        max_workers=max_workers,
        content_selectors=content_selectors,
        title_selectors=title_selectors,
        date_selectors=date_selectors,
        source_selectors=source_selectors,
        mode=mode,
        collect_links=collect_links
    )


async def process_failed_csv(failed_csv_path: str, save_dir: str = "articles",
                      export_filename: str = None, file_format: str = "CSV",
                      classid: str = "", retry: int = 3, interval: float = 1.0,
                      max_workers: int = 5, progress_callback=None,
                      log_callback=None, stop_flag=None) -> Dict[str, int]:
    """
    便捷函数：处理失败的CSV文件（传统方式，直接调用 save_article_async）

    注意：建议使用 process_failed_csv_via_crawler() 以获得更好的架构一致性

    Args:
        failed_csv_path: 失败CSV文件路径
        save_dir: 保存目录
        export_filename: 导出文件名
        file_format: 文件格式
        classid: 分类ID
        retry: 重试次数
        interval: 重试间隔
        max_workers: 最大并发任务数
        progress_callback: 进度回调函数
        log_callback: 日志回调函数
        stop_flag: 停止标志检查函数

    Returns:
        处理结果统计
    """
    processor = FailedUrlProcessor()

    # 设置回调函数
    if log_callback:
        processor.log_callback = log_callback
    if progress_callback:
        processor.progress_callback = progress_callback
    if stop_flag:
        processor.stop_flag = stop_flag

    return await processor.process_failed_urls(
        failed_csv_path=failed_csv_path,
        save_dir=save_dir,
        export_filename=export_filename,
        file_format=file_format,
        classid=classid,
        retry=retry,
        interval=interval,
        max_workers=max_workers
    )


if __name__ == "__main__":
    # 测试代码
    import asyncio
    logging.basicConfig(level=logging.INFO)

    async def test_failed_url_processor():
        # 示例：处理失败的CSV文件
        failed_csv = "articles/上海人大_代表风采_failed.csv"

        if os.path.exists(failed_csv):
            print(f"开始处理失败文件: {failed_csv}")
            print("使用新的路由方式（推荐）...")

            # 使用新的路由方式
            result = await process_failed_csv_via_crawler(
                failed_csv_path=failed_csv,
                save_dir="articles",
                export_filename="重试结果_路由版",
                file_format="CSV",
                classid="3802",
                retry=3,
                interval=1.0,
                max_workers=3,
                mode="balance"
            )
            print(f"路由方式处理结果: {result}")

            print("\n使用传统方式（兼容性）...")
            # 使用传统方式
            result_legacy = await process_failed_csv(
                failed_csv_path=failed_csv,
                save_dir="articles",
                export_filename="重试结果_传统版",
                file_format="CSV",
                classid="3802",
                retry=3,
                interval=1.0,
                max_workers=3
            )
            print(f"传统方式处理结果: {result_legacy}")
        else:
            print(f"失败文件不存在: {failed_csv}")

    # 运行测试
    asyncio.run(test_failed_url_processor())
