#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置迁移脚本 - 将现有配置迁移到新的三级分类结构
"""

import json
import os
from collections import OrderedDict
from datetime import datetime


def determine_category_path(group_name, group_config):
    """根据配置组名称和内容智能确定分类路径"""
    name_lower = group_name.lower()
    
    # 人大系统判断
    if any(keyword in name_lower for keyword in ['人大', 'rd', 'npc']):
        if any(keyword in name_lower for keyword in ['全国', '中国', 'national']):
            return "政府机构/人大系统/全国人大"
        elif any(keyword in name_lower for keyword in ['监督', '纵横']):
            return "政府机构/人大系统/监督纵横"
        else:
            return "政府机构/人大系统/地方人大"
    
    # 政协系统判断
    elif any(keyword in name_lower for keyword in ['政协', 'zx', 'cppcc']):
        if any(keyword in name_lower for keyword in ['全国', '中国', 'national']):
            return "政府机构/政协系统/全国政协"
        elif any(keyword in name_lower for keyword in ['提案', '工作']):
            return "政府机构/政协系统/提案工作"
        else:
            return "政府机构/政协系统/地方政协"
    
    # 媒体判断
    elif any(keyword in name_lower for keyword in ['人民日报', 'people', 'daily']):
        return "新闻媒体/中央媒体/人民日报"
    elif any(keyword in name_lower for keyword in ['新华', 'xinhua']):
        return "新闻媒体/中央媒体/新华社"
    elif any(keyword in name_lower for keyword in ['央视', 'cctv']):
        return "新闻媒体/中央媒体/央视网"
    
    # 默认分类
    return "政府机构/人大系统/地方人大"


def add_to_category_path(config, group_name, category_path):
    """将配置组添加到指定的分类路径"""
    try:
        parts = category_path.split("/")
        if len(parts) == 3:
            parent, sub, child = parts
            categories = config.get("categories", {})
            
            if (parent in categories and 
                "subcategories" in categories[parent] and
                sub in categories[parent]["subcategories"] and
                "subcategories" in categories[parent]["subcategories"][sub] and
                child in categories[parent]["subcategories"][sub]["subcategories"]):
                
                child_cat = categories[parent]["subcategories"][sub]["subcategories"][child]
                if "configs" not in child_cat:
                    child_cat["configs"] = []
                if group_name not in child_cat["configs"]:
                    child_cat["configs"].append(group_name)
    except Exception as e:
        print(f"添加到分类路径失败: {e}")


def create_new_category_structure():
    """创建新的三级分类结构"""
    return OrderedDict({
        "政府机构": {
            "description": "政府机构相关配置",
            "subcategories": {
                "人大系统": {
                    "description": "人大系统配置",
                    "subcategories": {
                        "全国人大": {"configs": [], "description": "全国人大配置"},
                        "地方人大": {"configs": [], "description": "地方人大配置"},
                        "监督纵横": {"configs": [], "description": "人大监督工作配置"}
                    }
                },
                "政协系统": {
                    "description": "政协系统配置",
                    "subcategories": {
                        "全国政协": {"configs": [], "description": "全国政协配置"},
                        "地方政协": {"configs": [], "description": "地方政协配置"},
                        "提案工作": {"configs": [], "description": "政协提案工作配置"}
                    }
                }
            },
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        "新闻媒体": {
            "description": "新闻媒体相关配置",
            "subcategories": {
                "中央媒体": {
                    "description": "中央媒体配置",
                    "subcategories": {
                        "人民日报": {"configs": [], "description": "人民日报相关配置"},
                        "新华社": {"configs": [], "description": "新华社相关配置"},
                        "央视网": {"configs": [], "description": "央视网相关配置"}
                    }
                },
                "地方媒体": {
                    "description": "地方媒体配置",
                    "subcategories": {
                        "省级媒体": {"configs": [], "description": "省级媒体配置"},
                        "市级媒体": {"configs": [], "description": "市级媒体配置"}
                    }
                }
            },
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
    })


def migrate_config():
    """迁移配置文件"""
    config_file = "configs/app/config.json"
    backup_file = f"configs/app/config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        # 读取现有配置
        with open(config_file, 'r', encoding='utf-8') as f:
            old_config = json.load(f, object_pairs_hook=OrderedDict)
        
        # 备份原配置
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(old_config, f, ensure_ascii=False, indent=4)
        print(f"✅ 原配置已备份到: {backup_file}")
        
        # 创建新配置结构
        new_config = OrderedDict()
        new_config["last_used"] = old_config.get("last_used", "default")
        
        # 创建新的三级分类结构
        new_config["categories"] = create_new_category_structure()
        
        # 迁移配置组
        new_config["groups"] = OrderedDict()
        old_groups = old_config.get("groups", {})
        
        migration_report = []
        
        for group_name, group_config in old_groups.items():
            # 智能确定分类路径
            category_path = determine_category_path(group_name, group_config)
            
            # 创建新的配置组
            new_group_config = group_config.copy()
            new_group_config["category_path"] = category_path
            
            # 确保有缓存结构
            if "cache" not in new_group_config:
                new_group_config["cache"] = {
                    "last_update": None,
                    "last_urls": [],
                    "total_articles": 0,
                    "success_rate": 0.0,
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
            
            new_config["groups"][group_name] = new_group_config
            
            # 添加到对应的三级分类
            add_to_category_path(new_config, group_name, category_path)
            
            migration_report.append(f"  {group_name} -> {category_path}")
        
        # 保存新配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(new_config, f, ensure_ascii=False, indent=4)
        
        print("✅ 配置迁移完成!")
        print("\n📋 迁移报告:")
        for report in migration_report:
            print(report)
        
        return True
        
    except Exception as e:
        print(f"❌ 配置迁移失败: {e}")
        return False


if __name__ == "__main__":
    print("🔄 开始配置迁移...")
    if migrate_config():
        print("\n🎉 配置迁移成功完成!")
        print("💡 提示: 原配置已备份，如有问题可以恢复")
    else:
        print("\n❌ 配置迁移失败!")
