#!/usr/bin/env python3
"""
简单测试天津税务局网站翻页功能
使用修复后的简单翻页模式
"""

import asyncio
import sys
import os
import logging

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志级别为DEBUG以查看详细信息
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger('PaginationHandler')
logger.setLevel(logging.DEBUG)

from core.PaginationHandler import PaginationHandler
from core.crawler import launch_browser
from playwright.async_api import async_playwright

async def test_tjszx_simple_mode():
    """测试天津税务局网站的简单翻页模式"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("🚀 测试天津税务局网站 - 简单翻页模式")
    print(f"📍 URL: {url}")
    
    async with async_playwright() as p:
        # 启动浏览器（可见模式便于观察）
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            # 创建PaginationHandler实例
            handler = PaginationHandler(page)
            
            # 访问页面
            print("🌐 正在访问页面...")
            await page.goto(url, wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(3000)  # 等待页面完全加载
            
            # 准备文章提取配置（根据原始测试脚本的发现修正）
            extract_config = {
                'list_container_selector': 'body',  # 使用body作为容器
                'article_item_selector': 'ul li a',  # 原始测试发现是ul li，我们要a标签
                'title_selector': 'a',
                'save_dir': "天津税务局测试",
                'page_title': "天津税务局简单翻页测试",
                'classid': 'tjszx_test',
                'base_url': 'https://www.tjszx.gov.cn',
                'url_mode': 'relative'
            }
            
            print("🔄 开始简单翻页测试...")
            print("   - 使用简单翻页模式")
            print("   - 禁用自动检测")
            print("   - 使用稳定的等待机制")
            
            # 执行简单翻页
            pages_processed = await handler.click_pagination(
                next_button_selector='a:has-text("下一页")',
                max_pages=5,  # 测试5页
                wait_after_click=2000,  # 等待2秒
                extract_articles_config=extract_config,
                use_simple_pagination=True,  # 启用简单翻页模式
                auto_detect_pagination=False  # 禁用自动检测
            )
            
            # 获取收集的文章
            articles = handler.get_all_articles()
            
            print(f"\n🎉 测试完成！")
            print(f"📄 成功处理页数: {pages_processed}")
            print(f"📊 收集文章总数: {len(articles)}")
            
            if len(articles) > 0:
                print(f"📈 平均每页文章数: {len(articles) / pages_processed:.1f}")
                print(f"\n📋 收集的文章样例（前10篇）:")
                for i, article in enumerate(articles[:10]):
                    title = article[0] if len(article) > 0 else "无标题"
                    article_url = article[1] if len(article) > 1 else "无URL"
                    print(f"  [{i+1:2d}] {title[:60]}...")
                    print(f"       {article_url}")
                
                if len(articles) > 10:
                    print(f"       ... 还有 {len(articles) - 10} 篇文章")
            else:
                print("⚠️ 未收集到任何文章")
            
            # 关闭浏览器
            await context.close()
            await browser.close()
            
            return {
                'success': pages_processed > 1,
                'pages': pages_processed,
                'articles': len(articles)
            }
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            await page.screenshot(path='tjszx_simple_test_error.png')
            print(f"📸 错误截图已保存: tjszx_simple_test_error.png")
            
            try:
                await context.close()
                await browser.close()
            except:
                pass
            
            return {
                'success': False,
                'error': str(e)
            }

async def compare_with_original():
    """与原始测试脚本进行对比"""
    print("🔄 运行对比测试...")
    
    # 首先运行简单翻页模式
    print("\n1️⃣ 测试简单翻页模式:")
    simple_result = await test_tjszx_simple_mode()
    
    # 然后运行智能翻页模式（会自动回退）
    print("\n2️⃣ 测试智能翻页模式:")
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            handler = PaginationHandler(page)
            await page.goto(url, wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(3000)
            
            extract_config = {
                'list_container_selector': 'body',  # 修正容器选择器
                'article_item_selector': 'ul li a',  # 修正文章项选择器
                'title_selector': 'a',
                'save_dir': "天津税务局智能测试",
                'page_title': "天津税务局智能翻页测试",
                'classid': 'tjszx_smart_test',
                'base_url': 'https://www.tjszx.gov.cn',
                'url_mode': 'relative'
            }
            
            pages_processed = await handler.click_pagination(
                next_button_selector='a:has-text("下一页")',
                max_pages=5,
                wait_after_click=2000,
                extract_articles_config=extract_config,
                use_simple_pagination=False,  # 使用智能模式
                auto_detect_pagination=True   # 启用自动检测
            )
            
            articles = handler.get_all_articles()
            smart_result = {
                'success': pages_processed > 1,
                'pages': pages_processed,
                'articles': len(articles)
            }
            
            print(f"📄 智能模式处理页数: {pages_processed}")
            print(f"📊 智能模式收集文章: {len(articles)}")
            
            await context.close()
            await browser.close()
            
        except Exception as e:
            print(f"❌ 智能模式测试失败: {e}")
            smart_result = {'success': False, 'error': str(e)}
            try:
                await context.close()
                await browser.close()
            except:
                pass
    
    # 输出对比结果
    print(f"\n{'='*60}")
    print("📊 对比结果")
    print(f"{'='*60}")
    print(f"简单翻页模式: {simple_result}")
    print(f"智能翻页模式: {smart_result}")
    
    if simple_result['success'] and smart_result['success']:
        if simple_result['pages'] >= smart_result['pages']:
            print("🏆 简单翻页模式表现更好或相同")
        else:
            print("🏆 智能翻页模式表现更好")
    elif simple_result['success']:
        print("🏆 只有简单翻页模式成功")
    elif smart_result['success']:
        print("🏆 只有智能翻页模式成功")
    else:
        print("❌ 两种模式都失败了")
    
    return simple_result, smart_result

if __name__ == "__main__":
    print("天津税务局网站翻页测试")
    print("=" * 40)
    
    choice = input("选择测试模式:\n1. 仅测试简单翻页模式\n2. 对比测试（简单 vs 智能）\n请输入选择 (1 或 2): ").strip()
    
    if choice == "2":
        simple_result, smart_result = asyncio.run(compare_with_original())
        print(f"\n🎯 测试完成！")
        if simple_result['success'] or smart_result['success']:
            print("✅ 至少有一种模式成功，翻页功能已修复")
        else:
            print("❌ 两种模式都失败，需要进一步调试")
    else:
        result = asyncio.run(test_tjszx_simple_mode())
        print(f"\n🎯 测试完成！")
        if result['success']:
            print("✅ 简单翻页模式成功，翻页功能已修复")
        else:
            print("❌ 简单翻页模式失败，需要进一步调试")
