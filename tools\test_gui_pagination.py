#!/usr/bin/env python3
"""
测试GUI中的翻页配置问题
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.PaginationHandler import PaginationHandler
from core.crawler import launch_browser
from playwright.async_api import async_playwright

async def test_gui_pagination_config():
    """测试GUI翻页配置"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("🔍 测试GUI翻页配置问题")
    print(f"📍 URL: {url}")
    
    async with async_playwright() as p:
        # 启动浏览器
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            # 创建PaginationHandler实例
            handler = PaginationHandler(page)
            
            # 访问页面
            print("🌐 正在访问页面...")
            await page.goto(url, wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(3000)
            
            # 模拟GUI的配置
            extract_config = {
                'list_container_selector': 'body',
                'article_item_selector': 'ul li a',
                'title_selector': 'a',
                'save_dir': "GUI测试",
                'page_title': "GUI翻页测试",
                'classid': 'gui_test',
                'base_url': 'https://www.tjszx.gov.cn',
                'url_mode': 'relative'
            }
            
            # 测试不同的max_pages值
            test_cases = [
                {'max_pages': 1, 'description': 'max_pages=1 (应该不翻页)'},
                {'max_pages': 2, 'description': 'max_pages=2 (应该翻1页)'},
                {'max_pages': 3, 'description': 'max_pages=3 (应该翻2页)'},
                {'max_pages': 5, 'description': 'max_pages=5 (GUI默认值)'}
            ]
            
            for i, test_case in enumerate(test_cases):
                print(f"\n{'='*60}")
                print(f"🧪 测试 {i+1}: {test_case['description']}")
                print(f"{'='*60}")
                
                # 重新访问页面确保状态一致
                await page.goto(url, wait_until='networkidle', timeout=30000)
                await page.wait_for_timeout(2000)
                
                # 创建新的handler实例
                test_handler = PaginationHandler(page)
                
                try:
                    # 测试智能翻页模式（GUI默认）
                    pages_processed = await test_handler.click_pagination(
                        next_button_selector='a:has-text("下一页")',
                        max_pages=test_case['max_pages'],
                        wait_after_click=2000,
                        extract_articles_config=extract_config,
                        use_simple_pagination=False,  # 使用智能模式
                        auto_detect_pagination=True
                    )
                    
                    articles = test_handler.get_all_articles()
                    
                    print(f"✅ 测试结果:")
                    print(f"   📄 处理页数: {pages_processed}")
                    print(f"   📊 收集文章: {len(articles)} 篇")
                    print(f"   🎯 预期页数: {test_case['max_pages']}")
                    
                    if pages_processed == test_case['max_pages']:
                        print(f"   ✅ 结果正确")
                    else:
                        print(f"   ❌ 结果不符合预期")
                        
                except Exception as e:
                    print(f"❌ 测试失败: {e}")
            
            # 关闭浏览器
            await context.close()
            await browser.close()
            
            print(f"\n🎯 GUI翻页配置测试完成！")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            try:
                await context.close()
                await browser.close()
            except:
                pass
            raise e

async def test_simple_mode_directly():
    """直接测试简单翻页模式"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("\n" + "="*60)
    print("🔧 直接测试简单翻页模式")
    print("="*60)
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            handler = PaginationHandler(page)
            
            await page.goto(url, wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(3000)
            
            extract_config = {
                'list_container_selector': 'body',
                'article_item_selector': 'ul li a',
                'title_selector': 'a',
                'save_dir': "简单模式测试",
                'page_title': "简单翻页测试",
                'classid': 'simple_test',
                'base_url': 'https://www.tjszx.gov.cn',
                'url_mode': 'relative'
            }
            
            # 直接调用简单翻页模式
            pages_processed = await handler._simple_click_pagination(
                next_button_selector='a:has-text("下一页")',
                max_pages=3,
                wait_after_click=2000,
                extract_articles_config=extract_config
            )
            
            articles = handler.get_all_articles()
            
            print(f"🎉 简单翻页模式测试结果:")
            print(f"   📄 处理页数: {pages_processed}")
            print(f"   📊 收集文章: {len(articles)} 篇")
            
            await context.close()
            await browser.close()
            
        except Exception as e:
            print(f"❌ 简单翻页模式测试失败: {e}")
            try:
                await context.close()
                await browser.close()
            except:
                pass

if __name__ == "__main__":
    print("GUI翻页配置测试工具")
    print("=" * 50)
    
    choice = input("选择测试:\n1. GUI配置测试\n2. 简单模式直接测试\n3. 两个都测试\n请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        asyncio.run(test_gui_pagination_config())
    elif choice == "2":
        asyncio.run(test_simple_mode_directly())
    else:
        asyncio.run(test_gui_pagination_config())
        asyncio.run(test_simple_mode_directly())
