#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 content_selector 到 content_selectors 的迁移
"""

import json
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_files():
    """测试配置文件是否正确使用 content_selectors"""
    print("🔍 === 测试配置文件 ===")
    
    config_file = "configs/app/config.json"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查珠海政协配置
        if "珠海政协" in config:
            zhzx_config = config["珠海政协"]
            
            if "content_selectors" in zhzx_config:
                print("✅ 珠海政协配置使用 content_selectors")
                print(f"   选择器: {zhzx_config['content_selectors']}")
            else:
                print("❌ 珠海政协配置缺少 content_selectors")
            
            if "content_selector" in zhzx_config:
                print("⚠️ 珠海政协配置仍包含旧的 content_selector")
            else:
                print("✅ 珠海政协配置已移除旧的 content_selector")
        else:
            print("❌ 未找到珠海政协配置")
    else:
        print("❌ 配置文件不存在")

def test_gui_config_manager():
    """测试GUI配置管理器"""
    print("\n🔍 === 测试GUI配置管理器 ===")
    
    try:
        from gui.config_manager import ConfigManager
        
        # 创建测试配置
        test_config = {
            'content_selectors': ['.article_cont', '.content', '.TRS_Editor']
        }
        
        manager = ConfigManager()
        content_selectors = manager.get_content_selectors(test_config)
        
        if content_selectors:
            print("✅ ConfigManager.get_content_selectors() 工作正常")
            print(f"   返回选择器: {content_selectors}")
        else:
            print("❌ ConfigManager.get_content_selectors() 返回空")
            
    except Exception as e:
        print(f"❌ GUI配置管理器测试失败: {e}")

def test_crawler_function():
    """测试爬虫函数参数"""
    print("\n🔍 === 测试爬虫函数参数 ===")
    
    try:
        from core.crawler import crawl_articles_async
        import inspect
        
        # 获取函数签名
        sig = inspect.signature(crawl_articles_async)
        params = list(sig.parameters.keys())
        
        if 'content_selectors' in params:
            print("✅ crawl_articles_async 使用 content_selectors 参数")
        else:
            print("❌ crawl_articles_async 缺少 content_selectors 参数")
            
        if 'content_selector' in params:
            print("⚠️ crawl_articles_async 仍包含旧的 content_selector 参数")
        else:
            print("✅ crawl_articles_async 已移除旧的 content_selector 参数")
            
    except Exception as e:
        print(f"❌ 爬虫函数测试失败: {e}")

def test_default_selectors():
    """测试默认选择器配置"""
    print("\n🔍 === 测试默认选择器配置 ===")
    
    try:
        from core.crawler import crawl_articles_async
        import inspect
        
        # 获取默认参数
        sig = inspect.signature(crawl_articles_async)
        content_selectors_param = sig.parameters.get('content_selectors')
        
        if content_selectors_param and content_selectors_param.default:
            default_selectors = content_selectors_param.default
            print("✅ 找到默认内容选择器:")
            for selector in default_selectors:
                print(f"   - {selector}")
                
            # 检查是否包含 .TRS_Editor
            if '.TRS_Editor' in default_selectors:
                print("✅ 默认选择器包含 .TRS_Editor")
            else:
                print("⚠️ 默认选择器不包含 .TRS_Editor")
        else:
            print("❌ 未找到默认内容选择器")
            
    except Exception as e:
        print(f"❌ 默认选择器测试失败: {e}")

def test_module_config():
    """测试模组配置"""
    print("\n🔍 === 测试模组配置 ===")
    
    try:
        module_config_file = "configs/modules/module_configs.json"
        if os.path.exists(module_config_file):
            with open(module_config_file, 'r', encoding='utf-8') as f:
                modules = json.load(f)
            
            for module_name, module_info in modules.items():
                if isinstance(module_info, dict) and 'config' in module_info:
                    config = module_info['config']
                    
                    if 'content_selectors' in config:
                        print(f"✅ 模组 '{module_name}' 使用 content_selectors")
                    elif 'content_selector' in config:
                        print(f"⚠️ 模组 '{module_name}' 仍使用旧的 content_selector")
                    else:
                        print(f"❓ 模组 '{module_name}' 无内容选择器配置")
        else:
            print("❌ 模组配置文件不存在")
            
    except Exception as e:
        print(f"❌ 模组配置测试失败: {e}")

def test_field_config():
    """测试字段配置"""
    print("\n🔍 === 测试字段配置 ===")
    
    try:
        from core.field_config_manager import FieldConfigManager
        
        manager = FieldConfigManager()
        fields = manager.get_available_fields()
        
        if 'content' in fields:
            content_field = fields['content']
            if 'selectors' in content_field:
                print("✅ 字段配置中的content字段有selectors")
                print(f"   选择器: {content_field['selectors']}")
            else:
                print("❌ 字段配置中的content字段缺少selectors")
        else:
            print("❌ 字段配置中未找到content字段")
            
    except ImportError:
        print("⚠️ 字段配置功能不可用")
    except Exception as e:
        print(f"❌ 字段配置测试失败: {e}")

def main():
    """主测试函数"""
    print("🧪 === content_selector 到 content_selectors 迁移测试 ===")
    print("=" * 60)
    
    test_config_files()
    test_gui_config_manager()
    test_crawler_function()
    test_default_selectors()
    test_module_config()
    test_field_config()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")
    print("如果看到任何 ⚠️ 或 ❌，请检查相应的配置或代码。")

if __name__ == "__main__":
    main()
