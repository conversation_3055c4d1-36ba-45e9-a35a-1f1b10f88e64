#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式AI选择器分析工具
提供命令行界面，方便用户使用AI分析和测试功能
"""

import asyncio
import logging
import json
from analyzer import AIAnalyzerWithTesting

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class InteractiveAIAnalyzer:
    """交互式AI分析器"""
    
    def __init__(self):
        self.analyzer = AIAnalyzerWithTesting()
    
    async def run(self):
        """运行交互式界面"""
        print("🤖 AI选择器分析器 - 交互式工具")
        print("="*60)
        print("集成选择器测试模块，智能分析网站结构")
        print("="*60)
        
        while True:
            print("\n请选择功能:")
            print("1. 完整分析（推荐）")
            print("2. 分步分析 - 列表页")
            print("3. 分步分析 - 文章页")
            print("4. 测试重试机制")
            print("5. 查看分析历史")
            print("6. 导出配置到GUI")
            print("7. 退出")
            
            choice = input("\n请输入选择 (1-7): ").strip()
            
            if choice == "1":
                await self.full_analysis()
            elif choice == "2":
                await self.analyze_list_page()
            elif choice == "3":
                await self.analyze_article_page()
            elif choice == "4":
                await self.test_retry_mechanism()
            elif choice == "5":
                self.view_analysis_history()
            elif choice == "6":
                await self.export_to_gui()
            elif choice == "7":
                print("退出AI分析器")
                break
            else:
                print("❌ 无效选择，请重新输入")
    
    async def full_analysis(self):
        """完整分析流程"""
        print("\n🚀 完整分析流程")
        print("-"*40)
        
        url = input("请输入要分析的列表页URL: ").strip()
        if not url:
            print("❌ URL不能为空")
            return
        
        print(f"\n⏳ 开始分析: {url}")
        print("这可能需要几分钟时间...")
        
        try:
            result = await self.analyzer.full_analysis_with_testing(url)
            
            self._display_full_result(result)
            
            # 询问是否保存结果
            save = input("\n是否保存分析结果？(y/n): ").strip().lower()
            if save == 'y':
                filename = self.analyzer.save_analysis_result(result)
                if filename:
                    print(f"✅ 结果已保存到: {filename}")
            
            # 询问是否导出到GUI
            export = input("是否导出配置到GUI？(y/n): ").strip().lower()
            if export == 'y':
                await self._export_config_to_gui(result)
        
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    async def analyze_list_page(self):
        """分析列表页"""
        print("\n🔍 列表页分析")
        print("-"*40)
        
        url = input("请输入列表页URL: ").strip()
        if not url:
            print("❌ URL不能为空")
            return
        
        try:
            result = await self.analyzer.analyze_list_page_selectors(url)
            self._display_list_result(result)
        except Exception as e:
            print(f"❌ 列表页分析失败: {e}")
    
    async def analyze_article_page(self):
        """分析文章页"""
        print("\n🔍 文章页分析")
        print("-"*40)
        
        url = input("请输入文章页URL: ").strip()
        if not url:
            print("❌ URL不能为空")
            return
        
        try:
            result = await self.analyzer.analyze_article_page_selectors(url)
            self._display_article_result(result)
        except Exception as e:
            print(f"❌ 文章页分析失败: {e}")
    
    async def test_retry_mechanism(self):
        """测试重试机制"""
        print("\n🔄 重试机制测试")
        print("-"*40)
        
        url = input("请输入要测试的URL: ").strip()
        if not url:
            print("❌ URL不能为空")
            return
        
        try:
            result = await self.analyzer._retry_link_extraction(url)
            
            print(f"\n📊 重试结果:")
            print(f"   找到链接数: {result.get('links_found', 0)}")
            
            if result.get('links_found', 0) > 0:
                selectors = result.get('selectors', {})
                print(f"   成功策略: {selectors.get('article_item_selector')}")
                
                sample_links = result.get('sample_links', [])
                if sample_links:
                    print(f"   示例链接:")
                    for i, link in enumerate(sample_links[:3], 1):
                        print(f"     {i}. {link}")
            else:
                print("   ❌ 所有重试策略都失败了")
        
        except Exception as e:
            print(f"❌ 重试机制测试失败: {e}")
    
    def view_analysis_history(self):
        """查看分析历史"""
        print("\n📚 分析历史")
        print("-"*40)
        
        import os
        import glob
        
        # 查找分析结果文件
        pattern = "ai_analysis_*.json"
        files = glob.glob(pattern)
        
        if not files:
            print("📭 暂无分析历史")
            return
        
        print(f"找到 {len(files)} 个分析结果:")
        
        for i, file in enumerate(sorted(files, reverse=True)[:10], 1):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                url = data.get('list_url', 'Unknown')
                timestamp = data.get('timestamp', 'Unknown')
                success = data.get('success', False)
                
                print(f"  {i}. {file}")
                print(f"     URL: {url}")
                print(f"     时间: {timestamp}")
                print(f"     状态: {'✅ 成功' if success else '❌ 失败'}")
                print()
            except:
                print(f"  {i}. {file} (无法读取)")
    
    async def export_to_gui(self):
        """导出配置到GUI"""
        print("\n📤 导出配置到GUI")
        print("-"*40)
        
        # 查找最近的分析结果
        import os
        import glob
        
        pattern = "ai_analysis_*.json"
        files = glob.glob(pattern)
        
        if not files:
            print("❌ 没有找到分析结果文件")
            return
        
        # 显示可用的结果文件
        print("可用的分析结果:")
        for i, file in enumerate(sorted(files, reverse=True)[:5], 1):
            print(f"  {i}. {file}")
        
        choice = input("请选择要导出的结果 (输入序号): ").strip()
        
        try:
            file_index = int(choice) - 1
            selected_file = sorted(files, reverse=True)[file_index]
            
            with open(selected_file, 'r', encoding='utf-8') as f:
                result = json.load(f)
            
            await self._export_config_to_gui(result)
        
        except (ValueError, IndexError, FileNotFoundError):
            print("❌ 无效选择或文件不存在")
        except Exception as e:
            print(f"❌ 导出失败: {e}")
    
    def _display_full_result(self, result):
        """显示完整分析结果"""
        print(f"\n📊 完整分析结果")
        print("="*50)
        print(f"URL: {result['list_url']}")
        print(f"时间: {result['timestamp']}")
        print(f"状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
        
        if result['success']:
            config = result['final_config']
            print(f"置信度: {config['confidence_score']:.2f}")
            
            print(f"\n📋 推荐配置:")
            print(f"  列表容器: {config['list_container_selector']}")
            print(f"  文章项目: {config['article_item_selector']}")
            print(f"  标题选择器: {config['title_selectors']}")
            print(f"  内容选择器: {config['content_selectors']}")
            print(f"  日期选择器: {config['date_selectors']}")
            print(f"  来源选择器: {config['source_selectors']}")
        else:
            print(f"错误: {result.get('error', '未知错误')}")
    
    def _display_list_result(self, result):
        """显示列表页分析结果"""
        print(f"\n📊 列表页分析结果")
        print("="*40)
        print(f"状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
        
        if result['success']:
            test_results = result['test_results']
            selectors = test_results['selectors']
            
            print(f"找到链接数: {test_results.get('links_found', 0)}")
            print(f"容器选择器: {selectors.get('list_container_selector')}")
            print(f"项目选择器: {selectors.get('article_item_selector')}")
            
            sample_links = test_results.get('sample_links', [])
            if sample_links:
                print(f"示例链接: {sample_links[0]}")
    
    def _display_article_result(self, result):
        """显示文章页分析结果"""
        print(f"\n📊 文章页分析结果")
        print("="*40)
        print(f"状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
        
        if result['success']:
            extractions = result['test_results'].get('extractions', {})
            
            for field in ['title', 'content', 'date', 'source']:
                if field in extractions and extractions[field]:
                    selector = extractions[field]['selector']
                    text = extractions[field]['text']
                    if field == 'content':
                        print(f"  {field}: ✅ {selector} ({len(text)} 字符)")
                    else:
                        print(f"  {field}: ✅ {selector} - {text[:50]}...")
                else:
                    print(f"  {field}: ❌")
    
    async def _export_config_to_gui(self, result):
        """导出配置到GUI配置管理器"""
        if not result.get('success'):
            print("❌ 只能导出成功的分析结果")
            return
        
        try:
            from config.manager import ConfigManager
            
            config_manager = ConfigManager()
            final_config = result['final_config']
            
            # 构建GUI配置格式
            gui_config = {
                'input_url': result['list_url'],
                'list_container_selector': final_config['list_container_selector'],
                'article_item_selector': final_config['article_item_selector'],
                'title_selector': final_config['title_selectors'][0] if final_config['title_selectors'] else '',
                'content_selector': final_config['content_selectors'][0] if final_config['content_selectors'] else '',
                'date_selector': final_config['date_selectors'][0] if final_config['date_selectors'] else '',
                'source_selector': final_config['source_selectors'][0] if final_config['source_selectors'] else '',
                'mode': 'safe',
                'headless': True,
                'file_format': 'CSV'
            }
            
            # 生成配置组名称
            from urllib.parse import urlparse
            domain = urlparse(result['list_url']).netloc
            config_name = f"AI分析_{domain}_{result['timestamp'].replace(':', '').replace(' ', '_')}"
            
            # 添加到配置管理器
            success = config_manager.add_group(config_name, gui_config)
            
            if success:
                print(f"✅ 配置已导出到GUI: {config_name}")
                print(f"   置信度: {final_config['confidence_score']:.2f}")
            else:
                print("❌ 导出到GUI失败")
        
        except Exception as e:
            print(f"❌ 导出到GUI失败: {e}")

async def main():
    """主函数"""
    analyzer = InteractiveAIAnalyzer()
    await analyzer.run()

if __name__ == "__main__":
    asyncio.run(main())
