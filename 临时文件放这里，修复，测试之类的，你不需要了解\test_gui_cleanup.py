#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI清理验证测试脚本
验证GUI模块是否完全清理了旧的类型参数
"""

import sys
import os
import re

def test_gui_main_window():
    """测试GUI主窗口是否清理干净"""
    print("🔍 测试GUI主窗口清理情况...")
    
    file_path = 'gui/main_window.py'
    if not os.path.exists(file_path):
        print(f"  ❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有旧的类型参数控件
        old_controls = [
            'list_container_type',
            'article_item_type',
            'title_selector_type',
            'date_selector_type', 
            'source_selector_type'
        ]
        
        issues_found = []
        
        for control in old_controls:
            # 查找控件定义
            pattern = rf'self\.{control}\s*='
            matches = re.findall(pattern, content)
            if matches:
                issues_found.append(f"发现旧控件定义: {control}")
            
            # 查找控件使用
            pattern = rf'self\.{control}\.'
            matches = re.findall(pattern, content)
            if matches:
                issues_found.append(f"发现旧控件使用: {control} ({len(matches)}次)")
        
        # 检查是否还有单数形式的选择器引用
        old_selectors = ['title_selector', 'date_selector', 'source_selector']
        for selector in old_selectors:
            # 查找配置获取中的单数形式（排除兼容性代码）
            pattern = rf"config_data\.get\('{selector}'"
            matches = re.findall(pattern, content)
            if matches:
                # 检查是否是兼容性代码
                for match in re.finditer(pattern, content):
                    line_start = content.rfind('\n', 0, match.start()) + 1
                    line_end = content.find('\n', match.end())
                    line = content[line_start:line_end]
                    if 'get(' in line and selector + 's' not in line:
                        issues_found.append(f"发现非兼容性的单数选择器引用: {selector}")
        
        if issues_found:
            print("  ❌ 发现以下问题:")
            for issue in issues_found:
                print(f"    - {issue}")
            return False
        else:
            print("  ✅ GUI主窗口清理完成")
            return True
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_gui_config_consistency():
    """测试GUI配置的一致性"""
    print("\n🔍 测试GUI配置一致性...")
    
    try:
        from gui import main_window
        
        # 检查是否能正常导入
        print("  ✅ GUI模块导入成功")
        
        # 检查主要类是否存在
        if hasattr(main_window, 'CrawlerGUI'):
            print("  ✅ CrawlerGUI 类存在")
        else:
            print("  ❌ CrawlerGUI 类不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ GUI配置测试失败: {e}")
        return False

def test_gui_parameter_usage():
    """测试GUI中参数使用的一致性"""
    print("\n🔍 测试GUI参数使用一致性...")
    
    file_path = 'gui/main_window.py'
    if not os.path.exists(file_path):
        print(f"  ❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查复数形式选择器的使用
        expected_selectors = ['title_selectors', 'date_selectors', 'source_selectors']
        found_selectors = []
        
        for selector in expected_selectors:
            # 查找控件定义
            pattern = rf'self\.{selector}_edit'
            matches = re.findall(pattern, content)
            if matches:
                found_selectors.append(selector)
                print(f"  ✅ 发现复数选择器控件: {selector}_edit")
            else:
                print(f"  ❌ 缺少复数选择器控件: {selector}_edit")
        
        # 检查是否正确使用复数形式
        success = len(found_selectors) == len(expected_selectors)
        
        if success:
            print("  ✅ GUI参数使用一致性良好")
        else:
            print("  ❌ GUI参数使用存在问题")
        
        return success
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧹 GUI清理验证测试")
    print("=" * 50)
    
    tests = [
        ("GUI主窗口清理测试", test_gui_main_window),
        ("GUI配置一致性测试", test_gui_config_consistency),
        ("GUI参数使用一致性测试", test_gui_parameter_usage),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 GUI清理测试结果:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("\n🎉 GUI清理验证测试全部通过！")
        print("✅ 旧的类型参数控件已删除")
        print("✅ 复数形式选择器正确使用")
        print("✅ GUI模块功能完整")
    else:
        print("\n⚠️  GUI清理存在问题，需要进一步修复")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
