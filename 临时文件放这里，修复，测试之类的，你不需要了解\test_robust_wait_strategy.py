#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试健壮等待策略
验证新的渐进式等待机制是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from utils.robust_wait_strategy import robust_goto, get_success_stats
import time

async def test_robust_wait_strategy():
    """测试健壮等待策略"""
    
    # 测试URL列表（包含不同类型的网站）
    test_urls = [
        {
            "url": "https://mp.weixin.qq.com/s/hZmZVjxiQGmpS7z3Tjtg5g",
            "name": "微信公众号",
            "description": "测试微信公众号的等待策略"
        },
        {
            "url": "https://www.shrd.gov.cn/",
            "name": "上海人大",
            "description": "测试政府网站的等待策略"
        },
        {
            "url": "https://www.baidu.com",
            "name": "百度首页",
            "description": "测试简单网站的等待策略"
        },
        {
            "url": "https://httpbin.org/delay/3",
            "name": "延迟测试",
            "description": "测试延迟加载的网站"
        }
    ]
    
    print("🧪 开始测试健壮等待策略")
    print("=" * 60)
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        page = await context.new_page()
        
        results = []
        
        for i, test_case in enumerate(test_urls, 1):
            print(f"\n📋 测试 {i}/{len(test_urls)}: {test_case['name']}")
            print(f"🔗 URL: {test_case['url']}")
            print(f"📝 描述: {test_case['description']}")
            print("-" * 40)
            
            start_time = time.time()
            
            try:
                # 使用健壮等待策略
                success = await robust_goto(page, test_case['url'])
                
                load_time = time.time() - start_time
                
                if success:
                    # 获取页面信息
                    title = await page.title()
                    content_length = len(await page.content())
                    
                    print(f"✅ 访问成功")
                    print(f"⏱️  加载时间: {load_time:.2f} 秒")
                    print(f"📄 页面标题: {title[:50]}...")
                    print(f"📊 内容长度: {content_length} 字符")
                    
                    results.append({
                        "name": test_case['name'],
                        "url": test_case['url'],
                        "success": True,
                        "load_time": load_time,
                        "title": title,
                        "content_length": content_length,
                        "error": None
                    })
                else:
                    print(f"❌ 访问失败")
                    print(f"⏱️  尝试时间: {load_time:.2f} 秒")
                    
                    results.append({
                        "name": test_case['name'],
                        "url": test_case['url'],
                        "success": False,
                        "load_time": load_time,
                        "title": None,
                        "content_length": 0,
                        "error": "所有等待策略都失败"
                    })
                    
            except Exception as e:
                load_time = time.time() - start_time
                print(f"❌ 测试异常: {e}")
                print(f"⏱️  异常时间: {load_time:.2f} 秒")
                
                results.append({
                    "name": test_case['name'],
                    "url": test_case['url'],
                    "success": False,
                    "load_time": load_time,
                    "title": None,
                    "content_length": 0,
                    "error": str(e)
                })
        
        await browser.close()
        
        # 显示测试结果总结
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        success_rate = success_count / total_count * 100
        
        print(f"✅ 成功: {success_count}/{total_count} ({success_rate:.1f}%)")
        print(f"❌ 失败: {total_count - success_count}/{total_count}")
        
        print("\n📋 详细结果:")
        for result in results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['name']}: {result['load_time']:.2f}s")
            if result['error']:
                print(f"   错误: {result['error']}")
        
        # 显示成功率统计
        print("\n📈 等待策略成功率统计:")
        stats = get_success_stats()
        if stats:
            for domain, strategies in stats.items():
                print(f"\n🌐 {domain}:")
                for strategy, data in strategies.items():
                    print(f"  {strategy}: {data['success_rate']} ({data['success']}/{data['total']})")
        else:
            print("  暂无统计数据")
        
        return results


async def test_specific_strategies():
    """测试特定的等待策略"""
    print("\n🔬 测试特定等待策略")
    print("=" * 60)
    
    test_url = "https://www.baidu.com"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        # 测试不同的首选策略
        strategies = ["networkidle", "domcontentloaded", "load", "commit"]
        
        for strategy in strategies:
            print(f"\n🧪 测试策略: {strategy}")
            start_time = time.time()
            
            try:
                success = await robust_goto(
                    page, 
                    test_url, 
                    preferred_strategy=strategy,
                    timeout=30000
                )
                
                load_time = time.time() - start_time
                
                if success:
                    title = await page.title()
                    print(f"✅ 成功 - {load_time:.2f}s - {title[:30]}...")
                else:
                    print(f"❌ 失败 - {load_time:.2f}s")
                    
            except Exception as e:
                load_time = time.time() - start_time
                print(f"❌ 异常 - {load_time:.2f}s - {e}")
        
        await browser.close()


async def main():
    """主函数"""
    print("🚀 健壮等待策略测试程序")
    print("测试渐进式等待机制: networkidle -> domcontentloaded -> load -> commit")
    print()
    
    try:
        # 基本功能测试
        await test_robust_wait_strategy()
        
        # 特定策略测试
        await test_specific_strategies()
        
        print("\n🎉 测试完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
