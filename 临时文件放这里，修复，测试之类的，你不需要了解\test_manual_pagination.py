#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试手动翻页功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from core.crawler import launch_browser
from manual_pagination.manual_pagination_handler import ManualPaginationHandler

async def test_manual_pagination():
    """测试手动翻页功能"""
    print("🧪 测试手动翻页功能")
    print("=" * 60)
    
    # 检查Excel文件是否存在
    excel_path = "manual_pagination/url_templates.xlsx"
    if not os.path.exists(excel_path):
        print(f"❌ Excel文件不存在: {excel_path}")
        print("💡 请先运行以下命令创建Excel模板:")
        print(f"   python -c \"from manual_pagination.manual_pagination_handler import ManualPaginationHandler; h = ManualPaginationHandler(None); h.create_template_excel('{excel_path}')\"")
        return False
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            # 创建手动翻页处理器
            handler = ManualPaginationHandler(page)
            
            print(f"📋 使用Excel文件: {excel_path}")
            
            # 加载URL列表
            url_list = handler.load_urls_from_excel(excel_path)
            if not url_list:
                print("❌ 无法加载URL列表")
                return False
            
            print(f"✅ 加载了 {len(url_list)} 个URL")
            for i, url_info in enumerate(url_list, 1):
                print(f"  [{i}] {url_info['name']}: {url_info['url']}")
            
            # 配置文章提取参数
            extract_config = {
                'list_container_selector': 'body',
                'article_item_selector': 'a[href*="/tagz/system/"], a[href*="/yzjy/system/"]',
                'url_mode': 'absolute'
            }
            
            print(f"\n🚀 开始手动翻页处理...")
            
            # 处理手动翻页
            processed_pages, total_pages = await handler.process_manual_pagination(
                excel_path=excel_path,
                extract_config=extract_config,
                timeout=30000,
                wait_between_pages=3000,  # 3秒等待
                save_progress=True
            )
            
            # 获取结果
            all_articles = handler.get_all_articles()
            processed_urls = handler.get_processed_urls()
            failed_urls = handler.get_failed_urls()
            stats = handler.get_statistics()
            
            print(f"\n📊 处理结果:")
            print(f"  ✅ 成功处理: {processed_pages}/{total_pages} 页")
            print(f"  📄 提取文章: {len(all_articles)} 篇")
            print(f"  📈 成功率: {stats['success_rate']:.1f}%")
            
            if processed_urls:
                print(f"\n✅ 成功处理的页面:")
                for url_info in processed_urls:
                    print(f"  - {url_info['name']}: {url_info['article_count']} 篇文章")
            
            if failed_urls:
                print(f"\n❌ 失败的页面:")
                for url_info in failed_urls:
                    print(f"  - {url_info['name']}: {url_info['status']}")
            
            if all_articles:
                print(f"\n📋 前几篇文章示例:")
                for i, article in enumerate(all_articles[:5], 1):
                    title = article[0] if len(article) > 0 else "无标题"
                    url = article[1] if len(article) > 1 else "无URL"
                    page_name = article[2] if len(article) > 2 else "未知页面"
                    print(f"  [{i}] {title[:50]}...")
                    print(f"      URL: {url}")
                    print(f"      来源: {page_name}")
                    print()
            
            # 保存结果到Excel
            if all_articles:
                from core.excel_writer import ExcelWriter
                writer = ExcelWriter()
                filename = await writer.save_to_excel(all_articles, "手动翻页测试结果")
                print(f"💾 结果已保存到: {filename}")
            
            return processed_pages > 0
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            return False
            
        finally:
            await context.close()
            await browser.close()

async def create_test_excel():
    """创建测试用的Excel文件"""
    print("📝 创建测试Excel文件...")
    
    from manual_pagination.manual_pagination_handler import ManualPaginationHandler
    
    # 创建处理器
    handler = ManualPaginationHandler(None)
    
    # 创建测试URL列表
    test_urls = [
        "https://www.tjszx.gov.cn/tagz/taxd/index.shtml",
        "http://www.tjszx.gov.cn/yzjy/qthyfy/index.shtml",
        "https://www.tjszx.gov.cn/tagz/taxd/index_2.shtml"  # 这个可能不存在，用于测试错误处理
    ]
    
    excel_path = "manual_pagination/url_templates.xlsx"
    
    # 确保目录存在
    os.makedirs("manual_pagination", exist_ok=True)
    
    # 创建Excel模板
    success = handler.create_template_excel(excel_path, test_urls)
    
    if success:
        print(f"✅ 测试Excel文件已创建: {excel_path}")
        print("💡 您可以编辑此文件来添加更多URL")
        return True
    else:
        print("❌ 创建Excel文件失败")
        return False

if __name__ == "__main__":
    print("🚀 手动翻页功能测试")
    
    # 检查是否需要创建Excel文件
    excel_path = "manual_pagination/url_templates.xlsx"
    if not os.path.exists(excel_path):
        print("📝 Excel文件不存在，正在创建...")
        if not asyncio.run(create_test_excel()):
            print("❌ 无法创建Excel文件，测试终止")
            sys.exit(1)
    
    # 运行测试
    success = asyncio.run(test_manual_pagination())
    
    if success:
        print("\n🎉 手动翻页功能测试成功！")
        print("💡 您现在可以在GUI中使用手动翻页功能了")
    else:
        print("\n❌ 手动翻页功能测试失败")
        print("💡 请检查Excel文件和URL配置")
