# 选择器测试模块使用说明

## 概述

选择器测试模块 (`selectors_test.py`) 是一个专门用于测试和验证网站选择器配置的工具，支持模组配置和传统配置的测试验证。

## 主要功能

### 1. 单URL测试
- 测试单个URL的选择器配置
- 支持模组配置和自定义配置
- 提供详细的测试结果和性能数据

### 2. 批量测试
- 同时测试多个URL
- 支持批量验证模组配置
- 生成综合测试报告

### 3. 模组配置验证
- 验证所有模组配置的完整性
- 检查必要配置项
- 识别配置缺陷

### 4. 交互式测试
- 提供友好的命令行界面
- 支持实时配置和测试
- 即时查看测试结果

## 使用方法

### 基本命令

```bash
# 运行预定义测试用例
python selectors_test.py

# 交互式测试模式
python selectors_test.py interactive

# 运行预定义测试
python selectors_test.py predefined

# 验证模组配置
python selectors_test.py verify

# 创建配置模板
python selectors_test.py template
```

### 交互式测试

运行交互式模式后，可以选择以下操作：

1. **测试单个URL（使用模组配置）**
   - 输入URL，自动匹配模组配置进行测试
   - 适用于验证模组配置是否正确

2. **测试单个URL（使用自定义配置）**
   - 输入URL和自定义选择器配置
   - 适用于测试新的选择器组合

3. **批量测试URL列表**
   - 输入多个URL（逗号分隔）
   - 选择使用模组配置或自定义配置

4. **运行预定义测试用例**
   - 执行内置的测试用例
   - 包含微信公众号、政府网站等常见场景

5. **验证模组配置**
   - 检查所有模组配置的完整性
   - 识别缺失的必要配置项

## 配置管理

### 测试配置文件

使用 `selectors_test_config.py` 管理测试配置：

```bash
# 创建测试套件
python selectors_test_config.py create

# 生成报告模板
python selectors_test_config.py template
```

### 配置文件结构

```json
{
  "测试名称": {
    "description": "测试描述",
    "urls": ["测试URL列表"],
    "expected_module": "期望匹配的模组名称",
    "selectors_config": {
      "content_selectors": ["内容选择器"],
      "title_selectors": ["标题选择器"],
      "date_selectors": ["日期选择器"],
      "source_selectors": ["来源选择器"],
      "mode": "safe",
      "use_module_config": true
    }
  }
}
```

## 预定义测试用例

### 微信公众号测试
- 测试微信公众号文章的选择器
- 验证模组配置是否正确应用
- 检查内容提取效果

### 政府网站测试
- 测试各种政府网站的选择器
- 验证通用选择器的适用性
- 检查兼容性问题

## 测试结果

### 结果格式

```json
{
  "url": "测试URL",
  "test_name": "测试名称",
  "success": true,
  "duration": 2.5,
  "module_name": "匹配的模组名称",
  "config": "使用的配置",
  "timestamp": "测试时间",
  "error": "错误信息（如果有）"
}
```

### 测试报告

测试完成后会生成详细报告，包含：
- 测试总数和成功率
- 每个URL的详细结果
- 性能数据（响应时间）
- 错误信息和建议

## 最佳实践

### 1. 新网站测试流程

1. **首次测试**
   ```bash
   python selectors_test.py interactive
   # 选择 "2. 测试单个URL（使用自定义配置）"
   ```

2. **配置优化**
   - 根据测试结果调整选择器
   - 测试多个页面确保稳定性

3. **模组配置创建**
   - 将稳定的配置添加到模组管理器
   - 使用模组配置重新测试

### 2. 模组配置验证

定期运行验证命令：
```bash
python selectors_test.py verify
```

检查：
- 配置完整性
- 必要字段是否存在
- 选择器是否有效

### 3. 批量测试

对于大量URL的测试：
```bash
python selectors_test.py interactive
# 选择 "3. 批量测试URL列表"
```

建议：
- 每批不超过10个URL
- 设置适当的间隔时间
- 保存测试结果供后续分析

## 故障排除

### 常见问题

1. **模组管理器不可用**
   - 检查 `module_manager.py` 是否存在
   - 确认模组配置文件格式正确

2. **爬虫模块不可用**
   - 检查 `crawler.py` 是否存在
   - 确认依赖库已安装

3. **选择器测试失败**
   - 检查网站是否可访问
   - 验证选择器语法是否正确
   - 确认网站结构是否发生变化

### 调试技巧

1. **启用详细日志**
   ```python
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **单步测试**
   - 先测试单个URL
   - 逐步增加测试范围

3. **配置验证**
   - 使用配置验证功能
   - 检查JSON格式是否正确

## 扩展开发

### 添加新的测试类型

1. 继承 `SelectorsTestManager` 类
2. 实现自定义测试方法
3. 添加到预定义测试用例

### 自定义报告格式

1. 修改 `generate_test_report` 方法
2. 添加新的报告字段
3. 支持不同的输出格式

## 注意事项

1. **测试频率**
   - 避免过于频繁的测试请求
   - 设置适当的间隔时间

2. **网站礼貌**
   - 遵守网站的robots.txt
   - 不要对同一网站进行过度测试

3. **数据安全**
   - 测试结果可能包含敏感信息
   - 适当保护测试数据

4. **版本兼容**
   - 定期更新选择器配置
   - 关注网站结构变化
