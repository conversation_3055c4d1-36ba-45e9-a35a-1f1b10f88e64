# 手动翻页功能

## 📋 功能说明

手动翻页功能允许用户手动填写URL列表，适用于以下场景：
- 动态翻页无法正常工作的网站
- 需要精确控制爬取页面的情况
- 已知具体页面URL的批量爬取
- 复杂的翻页逻辑或特殊URL格式

## 📁 文件结构

```
manual_pagination/
├── README.md                 # 说明文档
├── url_templates.xlsx        # URL模板Excel文件
├── manual_pagination_handler.py  # 手动翻页处理器
└── examples/                 # 示例文件夹
    ├── example_urls.xlsx     # 示例URL列表
    └── template_formats.md   # URL格式说明
```

## 🎯 使用方法

### 方法一：GUI界面使用

1. **启动程序**: 运行 `python main.py` 启动GUI
2. **选择翻页模式**: 在"动态翻页"标签页中，选择"手动翻页"
3. **配置Excel文件**:
   - 点击"浏览"按钮选择Excel文件
   - 或点击"创建Excel模板"创建新的模板文件
4. **编辑URL列表**: 点击"编辑Excel文件"在Excel中填写URL
5. **配置参数**: 设置页面间等待时间等参数
6. **开始爬取**: 点击"开始爬取"按钮

### 方法二：代码调用

```python
from manual_pagination.manual_pagination_handler import ManualPaginationHandler
import asyncio

async def manual_crawl():
    # 创建处理器
    handler = ManualPaginationHandler(page)

    # 配置参数
    extract_config = {
        'list_container_selector': 'body',
        'article_item_selector': 'a[href*="/article/"]',
        'url_mode': 'absolute'
    }

    # 处理手动翻页
    processed, total = await handler.process_manual_pagination(
        excel_path="manual_pagination/url_templates.xlsx",
        extract_config=extract_config,
        wait_between_pages=2000
    )

    # 获取结果
    articles = handler.get_all_articles()
    return articles
```

## 📊 Excel文件格式

Excel文件包含以下列：

| 列名 | 说明 | 是否必填 | 示例 |
|------|------|----------|------|
| **URL** | 要爬取的页面URL | ✅ 必填 | https://example.com/page1.html |
| **页面名称** | 页面描述 | 可选 | 第1页 |
| **状态** | 爬取状态 | 自动更新 | 完成/失败/待处理 |
| **文章数量** | 提取的文章数量 | 自动更新 | 25 |
| **备注** | 其他说明 | 可选 | 重要页面 |

### 状态说明

- `待处理`: 尚未开始处理
- `处理中`: 正在处理该页面
- `完成`: 成功处理并提取到文章
- `无文章`: 处理成功但未找到文章
- `失败: 错误信息`: 处理失败及具体原因

## 🚀 快速开始

### 1. 创建Excel模板

```bash
# 运行测试脚本自动创建模板
python test_manual_pagination.py
```

或在GUI中点击"创建Excel模板"按钮。

### 2. 编辑URL列表

在Excel中填写要爬取的URL：

```
URL                                          页面名称    状态    文章数量  备注
https://www.tjszx.gov.cn/tagz/taxd/index.shtml  提案第1页   待处理   0        天津政协
https://www.tjszx.gov.cn/tagz/taxd/index_2.shtml 提案第2页   待处理   0        天津政协
https://www.tjszx.gov.cn/tagz/taxd/index_3.shtml 提案第3页   待处理   0        天津政协
```

### 3. 配置爬取参数

在GUI中设置：
- **文章选择器**: 用于提取文章链接的CSS选择器
- **页面间等待**: 页面之间的等待时间（毫秒）
- **保存进度**: 是否将处理状态保存回Excel

### 4. 开始爬取

点击"开始爬取"按钮，系统会：
1. 按顺序访问Excel中的每个URL
2. 从每个页面提取文章链接
3. 更新Excel中的处理状态
4. 合并所有文章并保存结果

## 💡 使用建议

### URL填写建议
- 使用完整的URL，包含协议（http://或https://）
- 按照逻辑顺序填写URL，便于管理
- 使用描述性的页面名称，如"提案第1页"

### 参数配置建议
- **页面间等待**: 建议设置2-5秒，避免访问过快
- **文章选择器**: 根据网站结构调整，确保能正确提取链接
- **保存进度**: 建议开启，便于中断后继续

### 错误处理建议
- 定期备份Excel文件
- 检查失败的URL并手动验证
- 根据错误信息调整选择器或URL

## 🔧 高级功能

### 1. 批量URL生成

对于有规律的URL，可以使用脚本批量生成：

```python
# 生成连续页码的URL
base_url = "https://example.com/list"
urls = []
for i in range(1, 11):  # 生成1-10页
    if i == 1:
        url = f"{base_url}.html"
    else:
        url = f"{base_url}_{i}.html"
    urls.append(url)
```

### 2. 条件处理

可以在Excel中添加条件，只处理特定的URL：

```python
# 只处理状态为"待处理"的URL
url_list = handler.load_urls_from_excel(excel_path)
pending_urls = [url for url in url_list if url['status'] == '待处理']
```

### 3. 自定义字段提取

配置更复杂的文章提取规则：

```python
extract_config = {
    'list_container_selector': '.article-list',
    'article_item_selector': '.article-item a',
    'url_mode': 'absolute',
    'title_selector': '.article-title',
    'date_selector': '.article-date'
}
```

## ⚠️ 注意事项

### 1. 网站访问
- 确保所有URL都能正常访问
- 检查是否需要登录或特殊权限
- 遵守网站的robots.txt规则

### 2. 访问频率
- 合理设置页面间等待时间
- 避免对目标网站造成过大负担
- 考虑在非高峰时段进行爬取

### 3. 数据管理
- 定期备份Excel文件和结果数据
- 记录重要的配置参数
- 保存失败URL的处理记录

## 🐛 常见问题

### Q: Excel文件无法打开？
A: 确保安装了pandas库：`pip install pandas openpyxl`

### Q: 某些URL处理失败？
A: 检查URL是否正确，网站是否可访问，选择器是否正确

### Q: 提取不到文章？
A: 检查文章选择器是否正确，可以使用浏览器开发者工具验证

### Q: 处理速度太慢？
A: 可以适当减少页面间等待时间，但要注意不要过快

## 📞 技术支持

如果遇到问题，请：
1. 检查日志输出中的错误信息
2. 验证Excel文件格式是否正确
3. 确认URL和选择器配置
4. 查看示例文件和文档
