#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模组配置管理器
用于管理不同网站的爬虫配置模组，根据URL自动选择合适的配置
"""

import json
import re
import os
import logging
from urllib.parse import urlparse
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class ModuleManager:
    """模组配置管理器"""
    
    def __init__(self, config_file: str = "configs/modules/module_configs.json"):
        """
        初始化模组管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.modules = {}
        self.match_priority = []
        self.load_modules()
    
    def load_modules(self):
        """加载模组配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 加载模组配置
                for module_name, module_config in data.items():
                    if module_name in ['match_priority']:
                        continue
                    self.modules[module_name] = module_config

                # 加载匹配优先级
                self.match_priority = data.get('match_priority', list(self.modules.keys()))

                logger.info(f"✅ 已加载 {len(self.modules)} 个模组配置")
                print(f"✅ 模组配置加载成功: {len(self.modules)} 个模组")
            else:
                logger.warning(f"⚠️ 配置文件 {self.config_file} 不存在，使用默认配置")
                print(f"⚠️ 配置文件不存在: {self.config_file}")
                self._create_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON格式错误 {self.config_file}: {e}")
            print(f"❌ JSON格式错误: {e}")
            self._create_default_config()
        except Exception as e:
            logger.error(f"❌ 加载模组配置失败: {e}")
            print(f"❌ 加载模组配置失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        self.modules = {}
        self.match_priority = []
    
    def save_modules(self):
        """保存模组配置到文件"""
        try:
            data = dict(self.modules)
            data['match_priority'] = self.match_priority

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            logger.info(f"模组配置已保存到 {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"保存模组配置失败: {e}")
            return False
    
    def match_url(self, url: str) -> Optional[str]:
        """
        根据URL匹配合适的模组

        Args:
            url: 要匹配的URL

        Returns:
            匹配的模组名称，如果没有匹配则返回None
        """
        if not url:
            return None
            
        parsed_url = urlparse(url)
        domain = parsed_url.netloc.lower()
        
        # 按优先级顺序匹配
        for module_name in self.match_priority:
            if module_name not in self.modules:
                continue

            module = self.modules[module_name]

            # 检查域名模式
            domain_patterns = module.get('domain_patterns', [])
            domain_matched = False

            # 如果没有域名模式，则认为域名匹配通过
            if not domain_patterns:
                domain_matched = True
            else:
                for pattern in domain_patterns:
                    if self._match_pattern(domain, pattern):
                        domain_matched = True
                        break

            # 只有域名匹配时，才进行进一步检查
            if domain_matched:
                url_patterns = module.get('url_patterns', [])

                # 如果没有URL模式，则仅域名匹配即可
                if not url_patterns:
                    logger.info(f"URL {url} 匹配到模组: {module_name} (仅域名匹配)")
                    return module_name

                # 如果有URL模式，则需要URL模式也匹配
                for pattern in url_patterns:
                    if self._match_url_pattern(url, pattern):
                        logger.info(f"URL {url} 匹配到模组: {module_name} (域名+URL匹配: {pattern})")
                        return module_name
        
        # 如果没有匹配到，返回None
        logger.info(f"URL {url} 未匹配到任何模组")
        return None
    
    def _match_pattern(self, text: str, pattern: str) -> bool:
        """
        匹配模式
        
        Args:
            text: 要匹配的文本
            pattern: 匹配模式，支持通配符*
            
        Returns:
            是否匹配
        """
        if pattern == "*":
            return True
        
        # 转换通配符为正则表达式
        regex_pattern = pattern.replace("*", ".*").replace(".", "\\.")
        try:
            return bool(re.match(f"^{regex_pattern}$", text, re.IGNORECASE))
        except re.error:
            logger.warning(f"无效的匹配模式: {pattern}")
            return False
    
    def _match_url_pattern(self, url: str, pattern: str) -> bool:
        """
        匹配URL模式
        
        Args:
            url: 要匹配的URL
            pattern: URL匹配模式（正则表达式）
            
        Returns:
            是否匹配
        """
        try:
            return bool(re.search(pattern, url, re.IGNORECASE))
        except re.error:
            logger.warning(f"无效的URL匹配模式: {pattern}")
            return False
    
    def get_module_config(self, module_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模组配置
        
        Args:
            module_name: 模组名称
            
        Returns:
            模组配置字典，如果不存在则返回None
        """
        if module_name in self.modules:
            return self.modules[module_name].get('config', {})
        return None
    
    def get_config_for_url(self, url: str, global_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        根据URL获取配置，模组配置缺失的参数使用全局配置

        Args:
            url: 目标URL
            global_config: 全局配置，用于填补模组配置中缺失的参数

        Returns:
            合并后的配置字典
        """
        module_name = self.match_url(url)
        if module_name:
            module_config = self.get_module_config(module_name)
            if module_config:
                # 如果提供了全局配置，则合并配置
                if global_config:
                    return self.merge_with_global_config(module_config, global_config)
                return module_config

        # 如果没有匹配的模组配置，返回全局配置或空配置
        return global_config if global_config else {}

    def merge_with_global_config(self, module_config: Dict[str, Any], global_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        将模组配置与全局配置合并，模组配置优先，缺失的参数使用全局配置

        Args:
            module_config: 模组配置
            global_config: 全局配置

        Returns:
            合并后的配置字典
        """
        # 从全局配置开始，作为基础配置
        merged_config = global_config.copy()

        # 用模组配置覆盖全局配置中的对应项
        for key, value in module_config.items():
            if isinstance(value, dict) and key in merged_config and isinstance(merged_config[key], dict):
                # 对于嵌套字典，递归合并
                merged_config[key] = self.merge_with_global_config(value, merged_config[key])
            else:
                # 直接覆盖
                merged_config[key] = value

        logger.debug(f"配置合并完成，模组配置项: {len(module_config)}, 全局配置项: {len(global_config)}, 合并后: {len(merged_config)}")
        return merged_config
    
    def add_module(self, module_name: str, module_config: Dict[str, Any]) -> bool:
        """
        添加新模组
        
        Args:
            module_name: 模组名称
            module_config: 模组配置
            
        Returns:
            是否添加成功
        """
        try:
            self.modules[module_name] = module_config
            if module_name not in self.match_priority:
                self.match_priority.insert(-1, module_name)  # 插入到默认配置之前
            logger.info(f"已添加模组: {module_name}")
            return True
        except Exception as e:
            logger.error(f"添加模组失败: {e}")
            return False

    def match_module_for_url(self, url: str) -> Optional[str]:
        """
        根据URL匹配模组（类方法版本）

        Args:
            url: 要匹配的URL

        Returns:
            匹配的模组名称，如果没有匹配则返回None
        """
        return self.match_url(url)
    
    def update_module(self, module_name: str, module_config: Dict[str, Any]) -> bool:
        """
        更新模组配置
        
        Args:
            module_name: 模组名称
            module_config: 新的模组配置
            
        Returns:
            是否更新成功
        """
        try:
            if module_name in self.modules:
                self.modules[module_name] = module_config
                logger.info(f"已更新模组: {module_name}")
                return True
            else:
                logger.warning(f"模组不存在: {module_name}")
                return False
        except Exception as e:
            logger.error(f"更新模组失败: {e}")
            return False
    
    def delete_module(self, module_name: str) -> bool:
        """
        删除模组
        
        Args:
            module_name: 模组名称
            
        Returns:
            是否删除成功
        """
        try:
            if module_name in self.modules:
                del self.modules[module_name]
                if module_name in self.match_priority:
                    self.match_priority.remove(module_name)
                logger.info(f"已删除模组: {module_name}")
                return True
            else:
                logger.warning(f"模组不存在: {module_name}")
                return False
        except Exception as e:
            logger.error(f"删除模组失败: {e}")
            return False
    
    def list_modules(self) -> List[str]:
        """
        列出所有模组名称
        
        Returns:
            模组名称列表
        """
        return list(self.modules.keys())
    
    def get_module_info(self, module_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模组信息
        
        Args:
            module_name: 模组名称
            
        Returns:
            模组信息字典
        """
        if module_name in self.modules:
            return self.modules[module_name]
        return None


# 全局模组管理器实例
module_manager = ModuleManager()


def get_config_for_url(url: str, global_config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    便捷函数：根据URL获取配置，支持与全局配置合并

    Args:
        url: 目标URL
        global_config: 全局配置，用于填补模组配置中缺失的参数

    Returns:
        配置字典（如果提供了全局配置，则为合并后的配置）
    """
    return module_manager.get_config_for_url(url, global_config)


def match_module_for_url(url: str) -> Optional[str]:
    """
    便捷函数：根据URL匹配模组

    Args:
        url: 目标URL

    Returns:
        匹配的模组名称
    """
    return module_manager.match_url(url)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 测试URL匹配
    test_urls = [
        "https://mp.weixin.qq.com/s/2EGonPvQhmqoEKszLUxZCg",
        "https://www.shrd.gov.cn/n8347/n8378/u1ai270696.html",
        "https://www.example.com/news/article.html"
    ]
    
    for url in test_urls:
        module_name = match_module_for_url(url)
        config = get_config_for_url(url)
        print(f"URL: {url}")
        print(f"匹配模组: {module_name}")
        print(f"配置: {config}")
        print("-" * 50)
