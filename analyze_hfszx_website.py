#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析合肥市政协网站的结构和特点
专门针对 JSP 动态网站的健壮性策略分析
"""

import asyncio
import sys
import os
from playwright.async_api import async_playwright
import json
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def analyze_hfszx_website():
    """分析合肥市政协网站"""
    url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    print("🔍 开始分析合肥市政协网站...")
    print(f"📋 目标URL: {url}")
    
    async with async_playwright() as p:
        # 启动浏览器（非无头模式便于观察）
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = await context.new_page()
        
        try:
            print("\n📊 第一步：页面加载分析...")
            
            # 记录加载时间
            start_time = time.time()
            
            # 访问页面
            await page.goto(url, wait_until='networkidle', timeout=30000)
            
            load_time = time.time() - start_time
            print(f"⏱️ 页面加载时间: {load_time:.2f}秒")
            
            # 获取页面标题
            title = await page.title()
            print(f"📄 页面标题: {title}")
            
            # 等待页面完全加载
            await asyncio.sleep(3)
            
            print("\n🏗️ 第二步：页面结构分析...")
            
            # 分析页面结构
            page_info = await page.evaluate("""
                () => {
                    const info = {
                        url: window.location.href,
                        title: document.title,
                        hasJQuery: typeof jQuery !== 'undefined',
                        hasFrames: window.frames.length > 0,
                        frameCount: window.frames.length,
                        scripts: Array.from(document.scripts).map(s => s.src || 'inline').slice(0, 10),
                        forms: document.forms.length,
                        links: document.links.length,
                        images: document.images.length
                    };
                    return info;
                }
            """)
            
            print(f"🔗 页面信息:")
            print(f"   - jQuery支持: {'✅' if page_info['hasJQuery'] else '❌'}")
            print(f"   - 框架数量: {page_info['frameCount']}")
            print(f"   - 表单数量: {page_info['forms']}")
            print(f"   - 链接数量: {page_info['links']}")
            print(f"   - 图片数量: {page_info['images']}")
            print(f"   - 脚本文件: {len(page_info['scripts'])} 个")
            
            print("\n📋 第三步：文章列表分析...")
            
            # 查找可能的文章列表容器
            potential_containers = [
                '.list', '.content', '.main', '.article-list', 
                '#list', '#content', '#main', 
                'table', 'tbody', 'ul', 'ol',
                '.news-list', '.item-list'
            ]
            
            container_info = {}
            for selector in potential_containers:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        container_info[selector] = len(elements)
                        # 获取第一个元素的文本内容（前100字符）
                        first_element = elements[0]
                        text_content = await first_element.text_content()
                        if text_content:
                            container_info[f"{selector}_sample"] = text_content[:100].strip()
                except:
                    pass
            
            print("🎯 发现的容器元素:")
            for selector, count in container_info.items():
                if not selector.endswith('_sample'):
                    sample_key = f"{selector}_sample"
                    sample_text = container_info.get(sample_key, "")
                    print(f"   - {selector}: {count} 个元素")
                    if sample_text:
                        print(f"     样本内容: {sample_text}")
            
            print("\n🔗 第四步：链接模式分析...")
            
            # 分析链接模式
            links_analysis = await page.evaluate("""
                () => {
                    const links = Array.from(document.links);
                    const analysis = {
                        total: links.length,
                        internal: 0,
                        external: 0,
                        jsp_links: 0,
                        detail_links: [],
                        patterns: {}
                    };
                    
                    links.forEach(link => {
                        const href = link.href;
                        if (href.includes(window.location.hostname)) {
                            analysis.internal++;
                        } else if (href.startsWith('http')) {
                            analysis.external++;
                        }
                        
                        if (href.includes('.jsp')) {
                            analysis.jsp_links++;
                        }
                        
                        // 收集可能的详情页链接
                        if (href.includes('detail') || href.includes('show') || href.includes('view') || 
                            href.includes('article') || href.includes('news')) {
                            analysis.detail_links.push({
                                href: href,
                                text: link.textContent.trim().substring(0, 50)
                            });
                        }
                        
                        // 分析URL模式
                        try {
                            const url = new URL(href);
                            const pattern = url.pathname.split('/').filter(p => p).join('/');
                            analysis.patterns[pattern] = (analysis.patterns[pattern] || 0) + 1;
                        } catch (e) {}
                    });
                    
                    return analysis;
                }
            """)
            
            print(f"🔗 链接分析结果:")
            print(f"   - 总链接数: {links_analysis['total']}")
            print(f"   - 内部链接: {links_analysis['internal']}")
            print(f"   - 外部链接: {links_analysis['external']}")
            print(f"   - JSP链接: {links_analysis['jsp_links']}")
            print(f"   - 详情页链接: {len(links_analysis['detail_links'])}")
            
            if links_analysis['detail_links']:
                print("   详情页链接样本:")
                for i, link in enumerate(links_analysis['detail_links'][:5]):
                    print(f"     {i+1}. {link['text']} -> {link['href']}")
            
            print("\n🎯 第五步：翻页机制分析...")
            
            # 查找翻页元素
            pagination_selectors = [
                '.page', '.pager', '.pagination', '.page-nav',
                'a[href*="page"]', 'a[href*="Page"]', 
                'a:contains("下一页")', 'a:contains("下页")', 'a:contains(">")',
                'input[name*="page"]', 'input[name*="Page"]'
            ]
            
            pagination_info = {}
            for selector in pagination_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        pagination_info[selector] = len(elements)
                        # 获取元素的属性信息
                        first_element = elements[0]
                        tag_name = await first_element.evaluate('el => el.tagName')
                        if tag_name.lower() == 'a':
                            href = await first_element.get_attribute('href')
                            text = await first_element.text_content()
                            pagination_info[f"{selector}_info"] = f"{text.strip()} -> {href}"
                        elif tag_name.lower() == 'input':
                            input_type = await first_element.get_attribute('type')
                            name = await first_element.get_attribute('name')
                            pagination_info[f"{selector}_info"] = f"type={input_type}, name={name}"
                except:
                    pass
            
            print("📄 翻页元素分析:")
            for selector, count in pagination_info.items():
                if not selector.endswith('_info'):
                    info_key = f"{selector}_info"
                    info = pagination_info.get(info_key, "")
                    print(f"   - {selector}: {count} 个元素")
                    if info:
                        print(f"     详情: {info}")
            
            print("\n⚡ 第六步：JavaScript执行环境分析...")
            
            # 检查JavaScript环境
            js_env = await page.evaluate("""
                () => {
                    return {
                        jquery_version: typeof jQuery !== 'undefined' ? jQuery.fn.jquery : null,
                        has_ajax: typeof XMLHttpRequest !== 'undefined',
                        has_fetch: typeof fetch !== 'undefined',
                        console_errors: window.console_errors || [],
                        page_ready: document.readyState,
                        dom_content_loaded: true
                    };
                }
            """)
            
            print(f"⚡ JavaScript环境:")
            print(f"   - jQuery版本: {js_env['jquery_version'] or '未检测到'}")
            print(f"   - AJAX支持: {'✅' if js_env['has_ajax'] else '❌'}")
            print(f"   - Fetch支持: {'✅' if js_env['has_fetch'] else '❌'}")
            print(f"   - 页面状态: {js_env['page_ready']}")
            
            # 保存分析结果
            analysis_result = {
                'url': url,
                'page_info': page_info,
                'container_info': container_info,
                'links_analysis': links_analysis,
                'pagination_info': pagination_info,
                'js_env': js_env,
                'load_time': load_time,
                'analysis_time': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 保存到文件
            with open('hfszx_analysis_result.json', 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 分析结果已保存到: hfszx_analysis_result.json")
            print("\n🎉 网站分析完成！")
            
            # 等待用户观察
            print("\n⏸️ 按 Enter 键关闭浏览器...")
            input()
            
        except Exception as e:
            print(f"❌ 分析过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await context.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(analyze_hfszx_website())
