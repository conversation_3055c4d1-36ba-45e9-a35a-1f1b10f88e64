#!/usr/bin/env python3
"""
测试无头模式 vs 可见模式的差异
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.PaginationHandler import PaginationHandler
from core.crawler import launch_browser
from playwright.async_api import async_playwright

async def test_headless_vs_visible():
    """测试无头模式vs可见模式的差异"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("🔍 测试无头模式 vs 可见模式的差异")
    print(f"📍 URL: {url}")
    
    results = {}
    
    # 测试两种模式
    modes = [
        {'headless': True, 'name': '无头模式'},
        {'headless': False, 'name': '可见模式'}
    ]
    
    for mode in modes:
        print(f"\n{'='*60}")
        print(f"🧪 测试 {mode['name']} (headless={mode['headless']})")
        print(f"{'='*60}")
        
        async with async_playwright() as p:
            try:
                # 启动浏览器
                browser, context, page = await launch_browser(p, headless=mode['headless'])
                
                # 访问页面
                print("🌐 正在访问页面...")
                await page.goto(url, wait_until='networkidle', timeout=30000)
                await page.wait_for_timeout(5000)  # 等待5秒
                
                # 检查翻页按钮
                print("🔍 检查翻页按钮...")
                next_selectors = [
                    'a:has-text("下一页")',
                    'a:has-text("下页")',
                    'a.next',
                    '.pagination .next',
                    'a[title="下一页"]',
                    'button:has-text("下一页")'
                ]
                
                found_buttons = []
                for selector in next_selectors:
                    try:
                        btn = await page.query_selector(selector)
                        if btn:
                            is_visible = await btn.is_visible()
                            btn_text = await btn.text_content()
                            btn_href = await btn.get_attribute('href')
                            found_buttons.append({
                                'selector': selector,
                                'text': btn_text,
                                'href': btn_href,
                                'visible': is_visible
                            })
                            print(f"   ✅ 找到: {selector} - 文本: '{btn_text}', 可见: {is_visible}")
                    except Exception as e:
                        print(f"   ❌ 检查 {selector} 时出错: {e}")
                
                # 检查pagetemple容器
                print("🔍 检查pagetemple容器...")
                pagetemple_content = await page.evaluate("""
                    () => {
                        const elem = document.getElementById('pagetemple');
                        return elem ? elem.innerHTML : null;
                    }
                """)
                
                pagetemple_exists = pagetemple_content is not None
                pagetemple_length = len(pagetemple_content) if pagetemple_content else 0
                
                print(f"   pagetemple存在: {pagetemple_exists}")
                print(f"   pagetemple内容长度: {pagetemple_length}")
                
                if pagetemple_content:
                    print(f"   内容预览: {pagetemple_content[:200]}...")
                
                # 检查JavaScript函数
                print("🔍 检查JavaScript函数...")
                js_functions = await page.evaluate("""
                    () => {
                        return {
                            nextpage: typeof nextpage !== 'undefined',
                            changepage: typeof changepage !== 'undefined'
                        };
                    }
                """)
                
                print(f"   nextpage函数存在: {js_functions['nextpage']}")
                print(f"   changepage函数存在: {js_functions['changepage']}")
                
                # 保存结果
                results[mode['name']] = {
                    'buttons_found': len(found_buttons),
                    'buttons': found_buttons,
                    'pagetemple_exists': pagetemple_exists,
                    'pagetemple_length': pagetemple_length,
                    'js_functions': js_functions
                }
                
                # 截图
                screenshot_name = f"tjszx_{mode['name'].replace('模式', '')}.png"
                await page.screenshot(path=screenshot_name, full_page=True)
                print(f"📸 截图已保存: {screenshot_name}")
                
                # 关闭浏览器
                await context.close()
                await browser.close()
                
            except Exception as e:
                print(f"❌ {mode['name']}测试失败: {e}")
                results[mode['name']] = {'error': str(e)}
                try:
                    await context.close()
                    await browser.close()
                except:
                    pass
    
    # 对比结果
    print(f"\n{'='*80}")
    print("📊 对比结果")
    print(f"{'='*80}")
    
    for mode_name, result in results.items():
        if 'error' in result:
            print(f"{mode_name}: ❌ 测试失败 - {result['error']}")
        else:
            print(f"{mode_name}:")
            print(f"   翻页按钮数量: {result['buttons_found']}")
            print(f"   pagetemple存在: {result['pagetemple_exists']}")
            print(f"   pagetemple长度: {result['pagetemple_length']}")
            print(f"   nextpage函数: {result['js_functions']['nextpage']}")
            print(f"   changepage函数: {result['js_functions']['changepage']}")
    
    # 分析差异
    if '无头模式' in results and '可见模式' in results:
        headless_result = results['无头模式']
        visible_result = results['可见模式']
        
        if not ('error' in headless_result or 'error' in visible_result):
            print(f"\n🔍 差异分析:")
            
            if headless_result['buttons_found'] != visible_result['buttons_found']:
                print(f"   ⚠️ 翻页按钮数量不同: 无头={headless_result['buttons_found']}, 可见={visible_result['buttons_found']}")
            
            if headless_result['pagetemple_length'] != visible_result['pagetemple_length']:
                print(f"   ⚠️ pagetemple内容长度不同: 无头={headless_result['pagetemple_length']}, 可见={visible_result['pagetemple_length']}")
            
            if headless_result['js_functions'] != visible_result['js_functions']:
                print(f"   ⚠️ JavaScript函数状态不同:")
                print(f"      无头模式: {headless_result['js_functions']}")
                print(f"      可见模式: {visible_result['js_functions']}")
            
            if (headless_result['buttons_found'] == visible_result['buttons_found'] and 
                headless_result['pagetemple_length'] == visible_result['pagetemple_length'] and
                headless_result['js_functions'] == visible_result['js_functions']):
                print(f"   ✅ 两种模式结果相同")
    
    print(f"\n🎯 测试完成！请检查截图文件查看页面差异")
    return results

if __name__ == "__main__":
    print("无头模式 vs 可见模式对比测试")
    print("=" * 50)
    
    results = asyncio.run(test_headless_vs_visible())
    
    print(f"\n📁 测试结果已保存，请查看截图文件进行对比")
