#!/usr/bin/env python3
"""
测试修复后的GUI翻页功能
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.PaginationHandler import PaginationHandler
from core.crawler import launch_browser
from playwright.async_api import async_playwright

async def test_fixed_gui_pagination():
    """测试修复后的GUI翻页功能"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("🚀 测试修复后的GUI翻页功能")
    print(f"📍 URL: {url}")
    
    async with async_playwright() as p:
        try:
            # 使用修复后的launch_browser（翻页兼容模式）
            print("🔧 启动浏览器（翻页兼容模式）...")
            browser, context, page = await launch_browser(
                p, 
                headless=False,
                pagination_compatible=True  # 启用翻页兼容模式
            )
            
            # 创建PaginationHandler实例
            handler = PaginationHandler(page)
            
            # 访问页面
            print("🌐 访问页面...")
            await page.goto(url, wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(3000)
            
            # 准备文章提取配置（与GUI相同）
            extract_config = {
                'list_container_selector': 'body',
                'article_item_selector': 'ul li a',
                'title_selector': 'a',
                'save_dir': "修复测试",
                'page_title': "GUI翻页修复测试",
                'classid': 'fixed_test',
                'base_url': 'https://www.tjszx.gov.cn',
                'url_mode': 'relative'
            }
            
            print("🔄 开始翻页测试（智能模式）...")
            
            # 测试智能翻页模式（GUI默认）
            pages_processed = await handler.click_pagination(
                next_button_selector='a:has-text("下一页")',
                max_pages=3,
                wait_after_click=2000,
                extract_articles_config=extract_config,
                use_simple_pagination=False,  # 使用智能模式
                auto_detect_pagination=True
            )
            
            # 获取收集的文章
            articles = handler.get_all_articles()
            
            print(f"\n🎉 测试结果:")
            print(f"   📄 处理页数: {pages_processed}")
            print(f"   📊 收集文章: {len(articles)} 篇")
            print(f"   🎯 目标页数: 3")
            
            success = pages_processed >= 2  # 至少翻页1次
            
            if success:
                print(f"   ✅ 翻页成功！")
                
                if len(articles) > 0:
                    print(f"\n📋 收集的文章样例（前5篇）:")
                    for i, article in enumerate(articles[:5]):
                        title = article[0] if len(article) > 0 else "无标题"
                        article_url = article[1] if len(article) > 1 else "无URL"
                        print(f"   [{i+1}] {title[:50]}...")
                        print(f"       {article_url}")
            else:
                print(f"   ❌ 翻页失败")
            
            # 关闭浏览器
            await context.close()
            await browser.close()
            
            return {
                'success': success,
                'pages': pages_processed,
                'articles': len(articles)
            }
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            await page.screenshot(path='fixed_gui_pagination_error.png')
            try:
                await context.close()
                await browser.close()
            except:
                pass
            
            return {
                'success': False,
                'error': str(e)
            }

async def test_compatibility_modes():
    """测试不同兼容模式的效果"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("\n" + "="*80)
    print("🔄 测试不同兼容模式的效果")
    print("="*80)
    
    test_modes = [
        {
            'name': '标准模式',
            'pagination_compatible': False,
            'description': '使用完整的反检测配置'
        },
        {
            'name': '翻页兼容模式',
            'pagination_compatible': True,
            'description': '移除影响JavaScript的配置'
        }
    ]
    
    results = {}
    
    for mode in test_modes:
        print(f"\n🧪 测试 {mode['name']} ({mode['description']})")
        
        async with async_playwright() as p:
            try:
                # 启动浏览器
                browser, context, page = await launch_browser(
                    p, 
                    headless=False,
                    pagination_compatible=mode['pagination_compatible']
                )
                
                # 访问页面
                await page.goto(url, wait_until='networkidle', timeout=30000)
                await page.wait_for_timeout(5000)
                
                # 检查翻页按钮
                next_selectors = [
                    'a:has-text("下一页")',
                    'a:has-text("下页")',
                    'a.next'
                ]
                
                found_button = False
                for selector in next_selectors:
                    btn = await page.query_selector(selector)
                    if btn:
                        is_visible = await btn.is_visible()
                        is_disabled = await btn.evaluate('(el) => el.hasAttribute("disabled") || el.classList.contains("disabled")')
                        if is_visible and not is_disabled:
                            found_button = True
                            print(f"   ✅ 找到可用翻页按钮: {selector}")
                            break
                
                if not found_button:
                    print(f"   ❌ 未找到可用翻页按钮")
                
                results[mode['name']] = {
                    'button_found': found_button,
                    'compatible': mode['pagination_compatible']
                }
                
                # 关闭浏览器
                await context.close()
                await browser.close()
                
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
                results[mode['name']] = {
                    'button_found': False,
                    'error': str(e)
                }
    
    # 对比结果
    print(f"\n📊 对比结果:")
    for mode_name, result in results.items():
        status = "✅ 成功" if result['button_found'] else "❌ 失败"
        print(f"   {mode_name}: {status}")
    
    return results

if __name__ == "__main__":
    print("修复后的GUI翻页功能测试")
    print("=" * 50)
    
    choice = input("选择测试:\n1. GUI翻页功能测试\n2. 兼容模式对比测试\n3. 两个都测试\n请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        result = asyncio.run(test_fixed_gui_pagination())
        print(f"\n🎯 测试完成！")
        if result['success']:
            print(f"✅ GUI翻页功能修复成功: {result['pages']}页, {result['articles']}篇文章")
        else:
            print(f"❌ GUI翻页功能仍有问题: {result.get('error', '未知错误')}")
    
    elif choice == "2":
        results = asyncio.run(test_compatibility_modes())
        print(f"\n🎯 兼容模式测试完成！")
        
        standard_success = results.get('标准模式', {}).get('button_found', False)
        compatible_success = results.get('翻页兼容模式', {}).get('button_found', False)
        
        if compatible_success and not standard_success:
            print("✅ 翻页兼容模式修复了问题！")
        elif standard_success and compatible_success:
            print("✅ 两种模式都正常工作")
        else:
            print("❌ 仍有问题需要进一步调试")
    
    else:
        # 两个都测试
        print("1️⃣ 兼容模式对比测试:")
        compatibility_results = asyncio.run(test_compatibility_modes())
        
        print("\n2️⃣ GUI翻页功能测试:")
        gui_result = asyncio.run(test_fixed_gui_pagination())
        
        print(f"\n🎯 综合测试完成！")
        
        compatible_works = compatibility_results.get('翻页兼容模式', {}).get('button_found', False)
        gui_works = gui_result['success']
        
        if compatible_works and gui_works:
            print("🎉 修复成功！GUI翻页功能已恢复正常")
        elif compatible_works and not gui_works:
            print("⚠️ 兼容模式有效，但GUI集成仍有问题")
        else:
            print("❌ 修复不完整，需要进一步调试")
