#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的动态翻页模块
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from core.crawler import launch_browser
from core.PaginationHandler import PaginationHandler

async def test_fixed_dynamic_pagination():
    """测试修复后的动态翻页模块"""
    print("🧪 测试修复后的动态翻页模块")
    print("=" * 60)
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
            print(f"📋 访问网站: {url}")
            
            await page.goto(url, timeout=60000)
            await page.wait_for_load_state('networkidle', timeout=30000)
            
            print("✅ 页面加载完成")
            print(f"📋 页面标题: {await page.title()}")
            
            # 等待页面完全加载
            await page.wait_for_timeout(5000)
            
            # 滚动到页面底部
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(3000)
            
            # 创建PaginationHandler实例
            handler = PaginationHandler(page)
            
            print("\n🔍 测试替代翻页按钮检测...")
            
            # 测试替代按钮检测功能
            alternative_buttons = await handler._find_alternative_pagination_buttons()
            
            if alternative_buttons:
                print(f"✅ 找到 {len(alternative_buttons)} 个替代翻页按钮:")
                
                for i, button in enumerate(alternative_buttons):
                    try:
                        tag_name = await button.evaluate("el => el.tagName.toLowerCase()")
                        text = await button.text_content()
                        href = await button.get_attribute('href')
                        onclick = await button.get_attribute('onclick')
                        
                        if tag_name == 'input':
                            value = await button.get_attribute('value')
                            input_type = await button.get_attribute('type')
                            print(f"  [{i+1}] INPUT按钮: type='{input_type}', value='{value}'")
                        else:
                            print(f"  [{i+1}] {tag_name.upper()}元素: 文本='{text}', href='{href}', onclick='{onclick}'")
                        
                        # 检查按钮状态
                        is_visible = await button.is_visible()
                        is_enabled = await button.is_enabled()
                        print(f"      状态: 可见={is_visible}, 可用={is_enabled}")
                        
                    except Exception as e:
                        print(f"  [{i+1}] 获取按钮信息失败: {e}")
                
                # 尝试点击第一个找到的按钮
                if alternative_buttons:
                    print(f"\n🎯 尝试点击第一个翻页按钮...")
                    
                    button = alternative_buttons[0]
                    
                    try:
                        # 获取点击前的状态
                        current_url = page.url
                        current_content = await page.content()
                        
                        print(f"📋 点击前URL: {current_url}")
                        
                        # 滚动到按钮位置
                        await button.scroll_into_view_if_needed()
                        await page.wait_for_timeout(1000)
                        
                        # 高亮显示按钮
                        await button.evaluate("el => el.style.border = '3px solid red'")
                        await page.wait_for_timeout(1000)
                        
                        # 点击按钮
                        await button.click()
                        print("✅ 点击成功")
                        
                        # 等待页面变化
                        await page.wait_for_timeout(5000)
                        
                        # 检查变化
                        new_url = page.url
                        new_content = await page.content()
                        
                        print(f"📋 点击后URL: {new_url}")
                        
                        if new_url != current_url:
                            print("🎉 URL发生变化，翻页成功！")
                        elif new_content != current_content:
                            print("🎉 页面内容发生变化，AJAX翻页成功！")
                        else:
                            print("⚠️ 没有检测到明显变化")
                        
                        # 检查是否有第2页的迹象
                        success_indicators = ["第2页", "2/12", "第2/12页", "2 / 12"]
                        翻页成功 = False
                        
                        for indicator in success_indicators:
                            if indicator in new_content:
                                print(f"🎉 确认翻页成功！找到指示器: {indicator}")
                                翻页成功 = True
                                break
                        
                        if 翻页成功:
                            # 截图保存成功状态
                            await page.screenshot(path="pagination_success.png", full_page=True)
                            print("📸 已保存成功截图: pagination_success.png")
                            
                            print("🎉 动态翻页模块修复成功！")
                            return True
                        else:
                            print("⚠️ 点击了按钮但没有检测到翻页成功的迹象")
                            
                            # 截图保存当前状态
                            await page.screenshot(path="after_click.png", full_page=True)
                            print("📸 已保存点击后截图: after_click.png")
                        
                    except Exception as click_error:
                        print(f"❌ 点击失败: {click_error}")
                
            else:
                print("❌ 未找到任何替代翻页按钮")
                print("💡 这可能意味着:")
                print("   1. 页面确实没有翻页功能")
                print("   2. 翻页按钮使用了特殊的实现方式")
                print("   3. 需要特定操作才能显示翻页按钮")
            
            # 现在测试完整的动态翻页功能
            print("\n🧪 测试完整的动态翻页功能...")
            
            # 配置文章提取参数
            extract_config = {
                'list_container_selector': 'body',
                'article_item_selector': 'a[href*="/tagz/system/"]',
                'url_mode': 'absolute'
            }
            
            # 测试不同的选择器
            test_selectors = [
                "input[value='下一页']",  # 新增的input选择器
                "a.next:not(.lose)",     # 原始选择器
                "a:has-text('下一页')",   # 文本选择器
            ]
            
            for selector in test_selectors:
                print(f"\n🎯 测试选择器: {selector}")
                
                try:
                    # 重新加载页面，确保状态一致
                    await page.goto(url, timeout=30000)
                    await page.wait_for_load_state('networkidle', timeout=15000)
                    await page.wait_for_timeout(3000)
                    
                    # 创建新的handler实例
                    handler = PaginationHandler(page)
                    
                    # 使用修复后的点击翻页功能
                    pages_processed = await handler.click_pagination(
                        next_button_selector=selector,
                        max_pages=2,  # 只测试2页
                        timeout=10000,
                        wait_after_click=3000,
                        disabled_check=True,
                        extract_articles_config=extract_config,
                        auto_detect_pagination=True
                    )
                    
                    print(f"✅ 处理了 {pages_processed} 页")
                    
                    # 获取收集的文章
                    articles = handler.get_all_articles()
                    print(f"📊 总共收集了 {len(articles)} 篇文章")
                    
                    if pages_processed > 1:
                        print(f"🎉 选择器 '{selector}' 翻页成功！")
                        return True
                    else:
                        print(f"⚠️ 选择器 '{selector}' 只处理了1页")
                    
                except Exception as e:
                    print(f"❌ 测试选择器 '{selector}' 失败: {e}")
                    continue
            
            print("\n📊 所有测试完成")
            return False
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            return False
            
        finally:
            # 保持浏览器打开一段时间
            print("\n⏳ 保持浏览器打开10秒...")
            await page.wait_for_timeout(10000)
            
            await context.close()
            await browser.close()

if __name__ == "__main__":
    success = asyncio.run(test_fixed_dynamic_pagination())
    if success:
        print("🎉 动态翻页模块修复测试成功！")
    else:
        print("❌ 动态翻页模块修复测试失败")
        print("💡 请检查截图了解具体情况")
