#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试编码处理修复
"""

import requests
from bs4 import BeautifulSoup

def test_encoding_detection():
    """测试编码检测功能"""
    print("🔤 测试编码检测功能...")
    
    # 测试不同编码的网站
    test_urls = [
        "https://httpbin.org/html",  # 英文网站
        "https://example.com",       # 简单网站
    ]
    
    for url in test_urls:
        print(f"\n🌐 测试URL: {url}")
        
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            print(f"📡 响应状态: {response.status_code}")
            print(f"🔤 原始编码: {response.encoding}")
            
            # 智能检测编码
            if response.encoding is None or response.encoding == 'ISO-8859-1':
                try:
                    import chardet
                    detected = chardet.detect(response.content)
                    if detected['encoding'] and detected['confidence'] > 0.7:
                        response.encoding = detected['encoding']
                        print(f"🔍 检测到编码: {detected['encoding']} (置信度: {detected['confidence']:.2f})")
                    else:
                        response.encoding = 'utf-8'
                        print("🔤 使用默认编码: utf-8")
                except ImportError:
                    print("⚠️ chardet模块不可用，使用默认编码")
                    response.encoding = 'utf-8'
            
            # 测试解析
            try:
                soup = BeautifulSoup(response.text, 'html.parser')
                print("✅ 页面解析成功")
                
                # 尝试提取标题
                title_element = soup.find('title')
                if title_element:
                    title = title_element.get_text(strip=True)
                    print(f"📝 页面标题: {title}")
                
            except Exception as e:
                print(f"❌ 解析失败: {e}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")

def test_text_cleaning():
    """测试文本清理功能"""
    print("\n🧹 测试文本清理功能...")
    
    # 测试包含特殊字符的文本
    test_texts = [
        "正常文本",
        "包含\n换行\r回车\t制表符的文本",
        "   多个   空格   的   文本   ",
        "混合\n\r\t特殊字符   的文本",
        "中文测试：这是一段中文文本",
    ]
    
    for text in test_texts:
        print(f"\n原文: {repr(text)}")
        
        # 清理文本
        cleaned = text.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
        cleaned = ' '.join(cleaned.split())
        
        print(f"清理后: {repr(cleaned)}")

def test_file_encoding():
    """测试文件编码"""
    print("\n💾 测试文件编码...")
    
    test_data = [
        ["URL", "标题", "内容", "状态"],
        ["https://example.com", "测试标题", "这是测试内容，包含中文字符", "成功"],
        ["https://test.com", "Another Title", "English content with special chars: àáâãäå", "成功"]
    ]
    
    # 测试CSV保存
    import csv
    import os
    
    csv_file = "test_encoding.csv"
    try:
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            for row in test_data:
                writer.writerow(row)
        
        print(f"✅ CSV文件保存成功: {csv_file}")
        
        # 读取验证
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            for i, row in enumerate(reader):
                print(f"第{i+1}行: {row}")
        
        # 清理测试文件
        os.remove(csv_file)
        
    except Exception as e:
        print(f"❌ CSV测试失败: {e}")
    
    # 测试Excel保存
    try:
        import openpyxl
        
        excel_file = "test_encoding.xlsx"
        wb = openpyxl.Workbook()
        ws = wb.active
        
        for row in test_data:
            ws.append(row)
        
        wb.save(excel_file)
        print(f"✅ Excel文件保存成功: {excel_file}")
        
        # 读取验证
        wb = openpyxl.load_workbook(excel_file)
        ws = wb.active
        for row in ws.iter_rows(values_only=True):
            print(f"Excel行: {row}")
        
        # 清理测试文件
        os.remove(excel_file)
        
    except ImportError:
        print("⚠️ openpyxl模块不可用，跳过Excel测试")
    except Exception as e:
        print(f"❌ Excel测试失败: {e}")

def main():
    """主测试函数"""
    print("编码处理修复测试")
    print("=" * 40)
    
    try:
        test_encoding_detection()
        test_text_cleaning()
        test_file_encoding()
        
        print("\n" + "=" * 40)
        print("🎉 编码处理测试完成！")
        
        print("\n📋 修复内容:")
        print("✅ 智能编码检测 - 自动检测网页编码")
        print("✅ 多重备用方案 - chardet检测 + 常见编码尝试")
        print("✅ 错误处理增强 - 编码错误时的备用解析")
        print("✅ 文本清理 - 移除特殊字符和多余空格")
        print("✅ 文件编码 - CSV使用UTF-8-BOM，Excel正确处理")
        
        print("\n💡 现在失败URL处理应该能够:")
        print("- 正确处理各种编码的网页")
        print("- 避免字符解码警告")
        print("- 正确保存中文和特殊字符")
        print("- 提供详细的编码处理日志")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
