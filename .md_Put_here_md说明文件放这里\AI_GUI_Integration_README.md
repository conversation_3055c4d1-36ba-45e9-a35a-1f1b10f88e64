# AI模块与GUI集成使用说明

## 概述

新的AI模块已成功集成到GUI中，提供智能选择器分析功能，并与模组管理器联动，避免重复配置。

## 主要功能

### 🤖 AI智能分析
- **两步分析**：自动分析列表页和文章页选择器
- **前5个候选**：AI生成多个候选选择器，按可能性排序
- **实时测试**：使用Playwright实际测试每个选择器
- **智能重试**：当初始选择器失败时自动尝试其他策略

### 🔧 LLM API配置
- **多模型支持**：支持DeepSeek、GPT、Claude等多种模型
- **API测试**：内置API连接测试功能
- **配置管理**：自动保存和加载配置

### 🔗 模组联动
- **域名检查**：自动检查是否已存在相同域名的模组配置
- **避免重复**：如果已有模组配置，则跳过AI分析
- **智能提示**：显示现有模组信息

## 使用步骤

### 1. 配置LLM API

1. 打开GUI，切换到"高级配置"标签页
2. 在"AI智能分析配置"组中点击"配置LLM API"
3. 填写API配置：
   - **API Key**: 您的API密钥
   - **Base URL**: API服务地址（默认：https://api.deepseek.com/v1）
   - **模型名称**: 选择或自定义模型名称
   - **Temperature**: 控制输出随机性（推荐：0.3）
   - **Max Tokens**: 最大输出长度（推荐：1500）
4. 点击"测试API连接"验证配置
5. 点击"保存"完成配置

### 2. 使用AI分析

1. 在"基础配置"中输入要分析的列表页URL
2. 切换到"高级配置"标签页
3. 确认AI分析状态显示为"已配置"
4. 点击"AI智能分析"按钮
5. 等待分析完成（通常需要1-3分钟）

### 3. 处理分析结果

分析完成后，系统会：
1. 自动填充GUI表单中的选择器
2. 询问是否保存为新的配置组
3. 显示置信度分数

## 功能详解

### AI分析流程

#### 步骤1：列表页分析
- AI分析页面结构，生成候选选择器
- 实际测试每个选择器组合
- 找到能提取最多链接的选择器
- 如果失败，启用智能重试机制

#### 步骤2：文章页分析
- 获取第一个文章链接
- AI分析文章页结构
- 测试内容、标题、日期、来源选择器
- 生成最终配置

### 重试机制

当AI推荐的选择器失败时，系统会自动尝试：
```
li a
.article-item a
.news-item a
.list-item a
ul li a
.content a
article a
.post a
div[class*='item'] a
div[class*='list'] a
```

### 模组联动

系统会自动检查以下情况：
- 是否已存在相同域名的模组配置
- 如果存在，显示模组名称并跳过分析
- 如果不存在，允许进行AI分析

## 配置文件

### LLM配置文件 (llm_config.json)
```json
{
  "api_key": "your-api-key",
  "base_url": "https://api.deepseek.com/v1",
  "model": "deepseek-chat",
  "temperature": 0.3,
  "max_tokens": 1500,
  "enable_ai": true
}
```

### AI分析结果文件
分析结果会自动保存为 `ai_analysis_YYYYMMDD_HHMMSS.json` 格式。

## 故障排除

### 常见问题

1. **AI分析按钮不可用**
   - 检查LLM API是否正确配置
   - 确认API Key有效
   - 测试API连接

2. **分析失败**
   - 检查网络连接
   - 确认目标网站可访问
   - 查看错误信息

3. **选择器测试失败**
   - 网站结构可能发生变化
   - 尝试手动调整选择器
   - 检查网站是否有反爬虫措施

### 调试技巧

1. **查看详细日志**
   - GUI右侧日志区域显示详细进度
   - 查看AI分析的每个步骤

2. **手动验证**
   - 使用浏览器开发者工具验证选择器
   - 检查页面元素结构

3. **配置调整**
   - 根据置信度分数判断配置质量
   - 手动微调AI生成的选择器

## 最佳实践

### 1. URL选择
- 使用具有代表性的列表页URL
- 确保页面包含足够的文章链接
- 避免使用动态加载的页面

### 2. 配置管理
- 为每个网站创建独立的配置组
- 使用有意义的配置组名称
- 定期备份重要配置

### 3. 性能优化
- 合理设置并发数和间隔时间
- 根据网站特点调整模式（safe/balance/fast）
- 监控爬取成功率

## 技术架构

### 核心组件
- **AIAnalyzerWithTesting**: AI分析器核心类
- **EnhancedAIConfigManager**: GUI集成管理器
- **LLMConfigDialog**: LLM配置对话框
- **SelectorsTestManager**: 选择器测试管理器

### 数据流
```
用户输入URL → 检查模组配置 → AI分析 → 选择器测试 → 结果应用 → 保存配置
```

### 集成点
- GUI高级配置页面
- 模组管理器
- 配置管理器
- 选择器测试模块

## 更新日志

### v2.0 (当前版本)
- ✅ 集成AI选择器分析器
- ✅ 添加LLM API配置界面
- ✅ 实现模组联动检查
- ✅ 智能重试机制
- ✅ 置信度评分系统
- ✅ 自动配置保存

### 计划功能
- 🔄 支持更多LLM模型
- 🔄 批量网站分析
- 🔄 选择器优化建议
- 🔄 历史分析记录管理

## 支持

如有问题或建议，请：
1. 查看日志信息
2. 检查配置文件
3. 尝试重新配置API
4. 联系技术支持
