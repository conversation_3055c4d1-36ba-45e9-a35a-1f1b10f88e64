# 🎯 自定义字段选择器使用指南

## 📋 问题背景

在使用灵活字段配置系统时，您可能会遇到以下问题：
- 预定义的选择器无法匹配目标网站的HTML结构
- 不同网站使用不同的CSS类名和元素结构
- 需要为特定网站定制字段提取规则

## 🔧 解决方案

我们提供了**自定义字段选择器**功能，让您可以：
1. 为每个字段自定义CSS选择器
2. 测试选择器的有效性
3. 保存和管理自定义配置

## 🚀 功能入口

在GUI的 **"字段配置"** 标签页中，点击 **"自定义选择器"** 按钮。

## 📖 使用步骤

### 步骤1: 打开自定义选择器对话框

1. 进入 **"字段配置"** 标签页
2. 点击 **"自定义选择器"** 按钮
3. 对话框将显示所有可用字段的选择器配置

### 步骤2: 编辑字段选择器

#### 基础字段标签页
包含8个基础字段：
- **title (标题)**: 文章标题选择器
- **content (内容)**: 文章正文选择器
- **dateget (发布日期)**: 发布日期选择器
- **source (来源)**: 来源/作者选择器
- 等等...

#### 扩展字段标签页
包含14个扩展字段：
- **likes (点赞数)**: 点赞数量选择器
- **views (阅读量)**: 阅读量选择器
- **price (价格)**: 价格信息选择器
- **sales (成交量)**: 成交量选择器
- 等等...

#### 编辑选择器
1. 在文本框中输入CSS选择器
2. 多个选择器用逗号分隔
3. 系统会按顺序尝试这些选择器
4. 第一个匹配的选择器将被使用

### 步骤3: 测试选择器

1. 在主窗口输入要测试的URL
2. 在自定义选择器对话框中点击 **"测试选择器"**
3. 系统会自动访问页面并测试所有选择器
4. 查看测试结果，确认选择器是否有效

### 步骤4: 保存配置

1. 确认选择器配置无误后
2. 点击 **"确定"** 按钮保存配置
3. 配置将自动应用到后续的爬取任务

## 🎯 选择器编写指南

### CSS选择器语法

#### 基本选择器
```css
.class-name          /* 类选择器 */
#element-id          /* ID选择器 */
tag-name             /* 标签选择器 */
[attribute]          /* 属性选择器 */
[attribute="value"]  /* 属性值选择器 */
```

#### 组合选择器
```css
.parent .child       /* 后代选择器 */
.parent > .child     /* 直接子元素选择器 */
.class1.class2       /* 多类选择器 */
.class1, .class2     /* 多选择器（用逗号分隔） */
```

#### 伪类选择器
```css
:first-child         /* 第一个子元素 */
:last-child          /* 最后一个子元素 */
:nth-child(n)        /* 第n个子元素 */
:contains("text")    /* 包含特定文本（BeautifulSoup支持） */
```

### 实际示例

#### 示例1: 微博点赞数
```css
.toolbar_item .woo-like-count,
.card-act .woo-like-count,
[data-like-count],
.like-count
```

#### 示例2: 淘宝商品价格
```css
.price-current .price-symbol + .price-integer,
.tm-price-current,
.tb-rmb-num,
[data-price]
```

#### 示例3: 新闻文章阅读量
```css
.read-count,
.view-num,
.pv-count,
[data-views],
.article-info .views
```

#### 示例4: 文章发布日期
```css
.publish-time,
.article-date,
.time,
[datetime],
.date-info
```

## 🧪 测试功能详解

### 测试流程
1. **启动浏览器**: 使用Playwright启动无头浏览器
2. **访问页面**: 加载指定的测试URL
3. **应用选择器**: 对每个字段的每个选择器进行测试
4. **收集结果**: 统计匹配元素数量和示例数据
5. **生成报告**: 显示详细的测试结果

### 测试结果解读

#### 成功状态 ✅
- **绿色显示**: 选择器找到了匹配元素
- **元素数量**: 显示找到的元素数量
- **示例数据**: 显示前3个元素的文本内容

#### 警告状态 ⚠️
- **橙色显示**: 选择器语法正确但未找到匹配元素
- **可能原因**: 页面结构不匹配、元素动态加载等

#### 错误状态 ❌
- **红色显示**: 选择器语法错误或执行异常
- **错误信息**: 显示具体的错误原因

### 优化建议

#### 1. 选择器优先级
- 将最可能匹配的选择器放在前面
- 从具体到通用的顺序排列
- 避免过于宽泛的选择器

#### 2. 容错处理
- 提供多个备选选择器
- 考虑不同页面布局的变化
- 包含通用的fallback选择器

#### 3. 性能优化
- 避免复杂的嵌套选择器
- 优先使用类选择器和ID选择器
- 减少不必要的选择器数量

## 💡 最佳实践

### 1. 分析目标网站
```html
<!-- 分析HTML结构 -->
<div class="article-meta">
    <span class="like-count">123</span>
    <span class="view-count">456</span>
    <time class="publish-date">2024-01-01</time>
</div>
```

### 2. 编写选择器
```css
/* 点赞数选择器 */
.article-meta .like-count,
.like-count,
[data-likes]

/* 阅读量选择器 */
.article-meta .view-count,
.view-count,
[data-views]

/* 发布日期选择器 */
.article-meta .publish-date,
.publish-date,
time[datetime]
```

### 3. 测试验证
1. 使用测试功能验证选择器
2. 检查示例数据是否正确
3. 确认优先级顺序合理

### 4. 持续优化
1. 根据实际爬取结果调整选择器
2. 定期测试选择器的有效性
3. 为新发现的网站结构添加选择器

## 🔄 配置管理

### 导出配置
1. 在字段配置标签页点击 **"导出配置"**
2. 保存包含自定义选择器的配置文件
3. 可以在不同项目间共享配置

### 导入配置
1. 点击 **"导入配置"** 按钮
2. 选择之前导出的配置文件
3. 自动应用包含的选择器配置

### 重置配置
1. 在自定义选择器对话框中点击 **"重置为默认"**
2. 恢复所有字段的默认选择器
3. 清除所有自定义配置

## ⚠️ 注意事项

### 1. 选择器兼容性
- 使用标准CSS选择器语法
- 避免浏览器特定的选择器
- 测试在不同页面上的兼容性

### 2. 动态内容处理
- 某些内容可能通过JavaScript动态加载
- 考虑使用更通用的选择器
- 必要时调整页面等待时间

### 3. 数据类型匹配
- 确保选择器提取的数据类型正确
- 数字字段应提取纯数字内容
- 日期字段应包含日期信息

### 4. 性能影响
- 过多的选择器会影响爬取速度
- 定期清理无效的选择器
- 优化选择器的执行效率

## 🎉 总结

自定义字段选择器功能让您能够：

1. **精确定位**: 为每个字段定制专门的选择器
2. **灵活适配**: 适应不同网站的HTML结构
3. **实时测试**: 验证选择器的有效性
4. **配置管理**: 保存和共享自定义配置
5. **持续优化**: 根据实际效果调整配置

通过这个功能，您可以大大提高字段提取的准确性和成功率，让爬虫能够适应各种不同的网站结构。
