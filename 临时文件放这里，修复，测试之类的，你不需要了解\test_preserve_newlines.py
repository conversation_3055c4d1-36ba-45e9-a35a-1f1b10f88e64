#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试换行符保留功能的脚本
验证增强型正文过滤是否正确保留换行符
"""

import sys
import os

def test_newline_preservation():
    """测试换行符保留功能"""
    print("=" * 80)
    print("🧪 测试换行符保留功能")
    print("=" * 80)
    
    try:
        from core.txt_clear import enhanced_content_filter
        print("✅ 增强型过滤函数导入成功")
        
        # 测试文本：包含多种换行符场景
        test_content = """<div class="article">
            <h1>文章标题</h1>
            
            <p>这是第一段内容。</p>
            
            <p>这是第二段内容，
            包含换行符。</p>
            
            <div class="meta">发布时间：2025-01-11</div>
            
            <p>这是第三段内容。</p>
            
            
            <p>这是第四段内容，前面有多个空行。</p>
            
            <div class="footer">版权所有 © 2025</div>
            
            <p>这是最后一段内容。</p>
        </div>"""
        
        print("📝 原始测试内容:")
        print("-" * 60)
        print(repr(test_content))
        print("-" * 60)
        print("显示效果:")
        print(test_content)
        print("-" * 60)
        
        # 使用增强型过滤处理
        filtered_content = enhanced_content_filter(test_content)
        
        print(f"\n✨ 增强型过滤后的内容:")
        print("-" * 60)
        print("原始字符串表示:")
        print(repr(filtered_content))
        print("-" * 60)
        print("显示效果:")
        print(filtered_content)
        print("-" * 60)
        
        # 验证换行符保留
        newline_count = filtered_content.count('\n')
        print(f"\n📊 换行符统计:")
        print(f"   过滤后换行符数量: {newline_count}")
        
        # 检查是否保留了段落结构
        lines = filtered_content.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        empty_lines = [line for line in lines if not line.strip()]
        
        print(f"   非空行数量: {len(non_empty_lines)}")
        print(f"   空行数量: {len(empty_lines)}")
        
        # 验证内容保留
        expected_content = [
            "文章标题",
            "第一段内容",
            "第二段内容",
            "第三段内容", 
            "第四段内容",
            "最后一段内容"
        ]
        
        found_content = []
        for content in expected_content:
            if content in filtered_content:
                found_content.append(content)
        
        print(f"   保留的有用内容: {len(found_content)}/{len(expected_content)}")
        
        # 验证无用内容清除
        unwanted_content = ["发布时间", "版权所有"]
        found_unwanted = []
        for unwanted in unwanted_content:
            if unwanted in filtered_content:
                found_unwanted.append(unwanted)
        
        print(f"   清除的无用内容: {len(unwanted_content) - len(found_unwanted)}/{len(unwanted_content)}")
        
        # 判断测试结果
        if (newline_count > 0 and 
            len(found_content) >= len(expected_content) * 0.8 and 
            len(found_unwanted) == 0):
            print("✅ 换行符保留测试通过")
            return True
        else:
            print("❌ 换行符保留测试失败")
            return False
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_paragraph_structure():
    """测试段落结构保留"""
    print("\n" + "=" * 80)
    print("🧪 测试段落结构保留")
    print("=" * 80)
    
    try:
        from core.txt_clear import enhanced_content_filter
        print("✅ 增强型过滤函数导入成功")
        
        # 模拟真实文章结构
        article_content = """<article>
<h1>重要政策解读</h1>

<p>近日，相关部门发布了重要政策文件。</p>

<p>该政策主要包括以下几个方面：</p>

<p>1. 第一个方面的内容
详细说明了具体措施。</p>

<p>2. 第二个方面的内容
进一步完善了相关规定。</p>

<div class="meta">
发布时间：2025-01-11 14:30:00
编辑：张三
</div>

<p>政策的实施将对以下领域产生影响：</p>

<ul>
<li>经济发展</li>
<li>社会治理</li>
<li>民生保障</li>
</ul>

<p>总的来说，这项政策具有重要意义。</p>

<div class="footer">
版权所有 © 2025 政府网站
联系我们 | 免责声明
</div>
</article>"""
        
        print("📄 模拟文章内容:")
        print(article_content[:200] + "...")
        
        # 处理内容
        processed_content = enhanced_content_filter(article_content)
        
        print(f"\n📋 处理后的内容:")
        print("-" * 60)
        print(processed_content)
        print("-" * 60)
        
        # 分析段落结构
        paragraphs = [p.strip() for p in processed_content.split('\n\n') if p.strip()]
        print(f"\n📊 段落结构分析:")
        print(f"   识别的段落数量: {len(paragraphs)}")
        
        for i, paragraph in enumerate(paragraphs, 1):
            print(f"   段落 {i}: {paragraph[:50]}{'...' if len(paragraph) > 50 else ''}")
        
        # 验证结构保留
        if len(paragraphs) >= 3:  # 至少应该有3个段落
            print("✅ 段落结构保留测试通过")
            return True
        else:
            print("❌ 段落结构保留测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_different_newline_scenarios():
    """测试不同换行符场景"""
    print("\n" + "=" * 80)
    print("🧪 测试不同换行符场景")
    print("=" * 80)
    
    try:
        from core.txt_clear import enhanced_content_filter
        print("✅ 增强型过滤函数导入成功")
        
        # 测试不同的换行符场景
        test_cases = [
            {
                "name": "单个换行符",
                "input": "第一行\n第二行\n第三行",
                "expected_newlines": 2
            },
            {
                "name": "双换行符（段落分隔）",
                "input": "第一段\n\n第二段\n\n第三段",
                "expected_newlines": 4
            },
            {
                "name": "多个连续换行符",
                "input": "第一段\n\n\n\n第二段",
                "expected_newlines": 2  # 应该被合并
            },
            {
                "name": "混合内容",
                "input": "<p>段落1</p>\n\n<div class=\"meta\">发布时间：2025-01-11</div>\n\n<p>段落2</p>",
                "expected_content": ["段落1", "段落2"]
            }
        ]
        
        all_passed = True
        for i, case in enumerate(test_cases, 1):
            result = enhanced_content_filter(case["input"])
            newline_count = result.count('\n')
            
            print(f"\n测试 {i}: {case['name']}")
            print(f"   输入: {repr(case['input'])}")
            print(f"   输出: {repr(result)}")
            print(f"   换行符数量: {newline_count}")
            
            if "expected_newlines" in case:
                if newline_count >= case["expected_newlines"] - 1:  # 允许一定的灵活性
                    print(f"   ✅ 换行符保留正常")
                else:
                    print(f"   ❌ 换行符保留异常 (期望≥{case['expected_newlines']-1}, 实际{newline_count})")
                    all_passed = False
            
            if "expected_content" in case:
                found_content = sum(1 for content in case["expected_content"] if content in result)
                if found_content == len(case["expected_content"]):
                    print(f"   ✅ 内容保留完整")
                else:
                    print(f"   ❌ 内容保留不完整 ({found_content}/{len(case['expected_content'])})")
                    all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试换行符保留功能")
    
    results = []
    
    # 基本换行符保留测试
    results.append(test_newline_preservation())
    
    # 段落结构保留测试
    results.append(test_paragraph_structure())
    
    # 不同换行符场景测试
    results.append(test_different_newline_scenarios())
    
    # 总结结果
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！换行符保留功能正常！")
        print("\n💡 功能特点:")
        print("   • 保留原始文本的换行符结构")
        print("   • 保持段落之间的分隔")
        print("   • 限制过多的连续空行")
        print("   • 清除无用内容的同时保持格式")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
