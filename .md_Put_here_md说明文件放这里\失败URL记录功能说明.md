# 失败URL记录功能说明

## 功能概述

失败URL记录功能是爬虫系统的重要组成部分，当文章内容为空或保存失败时，系统会自动将失败的URL记录到单独的CSV/Excel文件中，便于后续分析和重试。

## 主要功能

### 1. 智能内容检测
- **空内容检测**: 自动检测文章内容是否为空
- **HTML标签过滤**: 去除HTML标签后检查实际文本内容
- **最小长度验证**: 内容长度少于10个字符视为空内容
- **空白字符处理**: 自动去除各种空白字符和无意义字符

### 2. 失败原因分类
系统会记录详细的失败原因：
- **文章内容为空**: 爬取到的内容为空或无效
- **Excel写入失败**: Excel文件写入过程中出错
- **CSV写入失败**: CSV文件写入过程中出错
- **网络连接失败**: 无法访问目标URL
- **解析错误**: 页面解析过程中出错
- **保存异常**: 其他保存过程中的异常

### 3. 失败文件管理
- **自动命名**: 失败文件自动以 `{原文件名}_failed.csv/xlsx` 命名
- **统一存储**: 失败记录保存在与成功文件相同的目录中
- **格式支持**: 支持CSV和Excel两种格式
- **增量记录**: 支持追加模式，不会覆盖已有记录

## 文件格式

### 失败记录文件结构

| 字段名 | 说明 | 示例 |
|--------|------|------|
| failed_time | 失败时间 | 2025-01-15 14:30:25 |
| failed_url | 失败的URL | https://example.com/article1 |
| title | 文章标题 | 测试文章标题 |
| reason | 失败原因 | 文章内容为空 |
| status | 处理状态 | 待重试 |

### 文件命名规则

```
成功文件: export_filename.csv
失败文件: export_filename_failed.csv

成功文件: export_filename.xlsx  
失败文件: export_filename_failed.xlsx

默认失败文件: failed_urls.csv / failed_urls.xlsx
```

## 使用场景

### 1. 内容质量控制
- 识别返回空内容的URL
- 发现网站结构变化导致的解析失败
- 监控爬取质量和成功率

### 2. 错误诊断
- 分析失败原因分布
- 定位网站访问问题
- 优化选择器配置

### 3. 重试机制
- 基于失败记录进行重试
- 针对不同失败原因采用不同策略
- 提高整体爬取成功率

## 技术实现

### 1. 内容检测算法

```python
def is_content_empty(content_text):
    """检查内容是否为空"""
    if not content_text:
        return True
    
    # 去除空白字符
    cleaned_content = content_text.strip()
    if not cleaned_content:
        return True
    
    # 移除HTML标签
    import re
    text_only = re.sub(r'<[^>]+>', '', cleaned_content)
    text_only = re.sub(r'[\s\n\r\t\u00a0\u3000]+', '', text_only)
    
    # 长度检查
    return len(text_only) < 10
```

### 2. 失败记录保存

```python
def save_failed_url(link, reason, save_dir, export_filename=None, 
                   article_title="", file_format="CSV"):
    """保存失败URL到文件"""
    # 构建文件路径
    if export_filename:
        if file_format.upper() == "EXCEL":
            failed_filename = f"{export_filename}_failed.xlsx"
        else:
            failed_filename = f"{export_filename}_failed.csv"
        failed_file_path = os.path.join(save_dir, failed_filename)
    else:
        # 使用默认文件名
        failed_file_path = os.path.join(save_dir, "failed_urls.csv")
    
    # 准备数据
    now_str = time.strftime('%Y-%m-%d %H:%M:%S')
    failed_data_row = [now_str, link, article_title, reason, "待重试"]
    failed_headers = ['failed_time', 'failed_url', 'title', 'reason', 'status']
    
    # 保存到文件
    # ... 保存逻辑
```

### 3. 异步支持

系统同时支持同步和异步环境：
- `save_failed_url()`: 同步版本
- `save_failed_url_async()`: 异步版本，使用线程池执行

## 配置选项

### 1. 内容检测配置
- **最小内容长度**: 默认10个字符，可调整
- **HTML标签处理**: 自动去除，可配置
- **空白字符处理**: 自动处理各种空白字符

### 2. 文件保存配置
- **文件格式**: CSV或Excel
- **文件命名**: 自定义前缀或使用默认名称
- **保存目录**: 与成功文件相同目录

## 结果统计

爬取完成后，系统会显示详细的统计信息：

```
爬取任务完成!
总计: 100篇
成功: 85篇
失败: 15篇

失败URL已保存到:
/path/to/articles/export_filename_failed.csv

失败原因统计:
• 文章内容为空: 12篇
• 网络连接失败: 2篇
• 解析错误: 1篇

处理时间: 5分30秒
```

## 最佳实践

### 1. 定期检查失败记录
- 分析失败原因分布
- 识别网站结构变化
- 优化爬取配置

### 2. 重试策略
- 对于网络失败，可以直接重试
- 对于内容为空，需要检查选择器配置
- 对于解析错误，需要分析网站结构

### 3. 质量监控
- 设置失败率阈值
- 监控失败原因变化
- 及时调整爬取策略

## 故障排除

### 常见问题

**Q: 为什么有些明显有内容的页面被标记为空内容？**
A: 可能是选择器配置不正确，导致无法正确提取内容。检查内容选择器设置。

**Q: 失败文件没有创建怎么办？**
A: 检查保存目录的写入权限，确保程序有足够的权限创建文件。

**Q: 如何批量重试失败的URL？**
A: 可以读取失败文件中的URL列表，重新配置爬虫进行重试。

**Q: 失败记录太多怎么办？**
A: 分析失败原因，优化选择器配置，或者检查网站是否有反爬虫措施。

## 技术优势

1. **自动化**: 无需手动干预，自动记录失败情况
2. **详细记录**: 记录失败时间、URL、原因等详细信息
3. **格式兼容**: 支持CSV和Excel格式，便于后续处理
4. **异步支持**: 在异步环境中也能正常工作
5. **线程安全**: 支持多线程并发写入
6. **智能检测**: 准确识别各种空内容情况

通过失败URL记录功能，您可以更好地监控爬取质量，快速定位问题，并提高整体爬取成功率！
