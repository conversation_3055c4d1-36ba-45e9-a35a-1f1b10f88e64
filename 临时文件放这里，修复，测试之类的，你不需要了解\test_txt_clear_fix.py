#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 txt_clear 修复
验证所有 text_cleaner 函数都能正常工作
"""

def test_text_cleaner_import():
    """测试 text_cleaner 模块导入"""
    print("🔧 测试 text_cleaner 模块导入")
    print("="*50)
    
    try:
        from utils import text_cleaner
        print("✅ text_cleaner 模块导入成功")
        
        # 检查所有必需的函数
        required_functions = [
            'clean_title',
            'clean_html_content', 
            'normalize_date',
            'normalize_source',
            'filter_content',
            'clean_text'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if hasattr(text_cleaner, func_name):
                print(f"  ✅ {func_name} 函数存在")
            else:
                print(f"  ❌ {func_name} 函数缺失")
                missing_functions.append(func_name)
        
        if missing_functions:
            print(f"❌ 缺失函数: {missing_functions}")
            return False
        else:
            print("✅ 所有必需函数都存在")
            return True
            
    except Exception as e:
        print(f"❌ text_cleaner 导入失败: {e}")
        return False

def test_text_cleaner_functions():
    """测试 text_cleaner 函数功能"""
    print("\n🧪 测试 text_cleaner 函数功能")
    print("="*50)
    
    try:
        from utils import text_cleaner
        
        # 测试 clean_title
        test_title = "  测试标题\n\r  "
        cleaned_title = text_cleaner.clean_title(test_title)
        print(f"✅ clean_title: '{test_title}' -> '{cleaned_title}'")
        
        # 测试 normalize_date
        test_date = "2024年1月1日"
        normalized_date = text_cleaner.normalize_date(test_date)
        print(f"✅ normalize_date: '{test_date}' -> '{normalized_date}'")
        
        # 测试 normalize_source
        test_source = "来源：测试网站 2024-01-01"
        normalized_source = text_cleaner.normalize_source(test_source)
        print(f"✅ normalize_source: '{test_source}' -> '{normalized_source}'")
        
        # 测试 clean_html_content
        test_html = "<p>测试内容</p><script>alert('test')</script>"
        cleaned_html = text_cleaner.clean_html_content(test_html)
        print(f"✅ clean_html_content: HTML清理成功，长度: {len(cleaned_html)}")
        
        # 测试 filter_content
        test_content = "正常内容\n广告内容\n更多正常内容"
        filtered_content = text_cleaner.filter_content(test_content)
        print(f"✅ filter_content: 内容过滤成功，长度: {len(filtered_content)}")
        
        # 测试 clean_text
        test_text = "<div>测试文本</div>"
        cleaned_text = text_cleaner.clean_text(test_text)
        print(f"✅ clean_text: '{test_text}' -> '{cleaned_text}'")
        
        return True
        
    except Exception as e:
        print(f"❌ text_cleaner 函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crawler_import():
    """测试爬虫模块导入（验证 txt_clear 修复）"""
    print("\n🕷️ 测试爬虫模块导入")
    print("="*50)
    
    try:
        from core import crawler
        print("✅ 爬虫模块导入成功")
        
        # 检查关键函数
        key_functions = ['save_article', 'save_article_async', 'crawl_articles_async']
        for func_name in key_functions:
            if hasattr(crawler, func_name):
                print(f"  ✅ {func_name} 函数存在")
            else:
                print(f"  ❌ {func_name} 函数缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 爬虫模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_no_txt_clear_references():
    """检查是否还有 txt_clear 引用"""
    print("\n🔍 检查 txt_clear 引用")
    print("="*50)
    
    import os
    import re
    
    # 检查主要文件
    files_to_check = [
        'core/crawler.py',
        'core/failed_url_processor.py',
        'gui/main_window.py',
        'gui/crawler_thread.py'
    ]
    
    txt_clear_found = False
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找 txt_clear 引用
                txt_clear_matches = re.findall(r'txt_clear\.\w+', content)
                if txt_clear_matches:
                    print(f"❌ {file_path} 中发现 txt_clear 引用: {txt_clear_matches}")
                    txt_clear_found = True
                else:
                    print(f"✅ {file_path} 无 txt_clear 引用")
                    
            except Exception as e:
                print(f"⚠️ 检查文件失败 {file_path}: {e}")
    
    if not txt_clear_found:
        print("✅ 所有主要文件都已修复 txt_clear 引用")
        return True
    else:
        print("❌ 仍有文件包含 txt_clear 引用")
        return False

def test_application_startup():
    """测试应用启动（简化版本）"""
    print("\n🚀 测试应用组件导入")
    print("="*50)
    
    try:
        # 测试关键组件导入
        from gui.main_window import CrawlerGUI
        print("✅ GUI主窗口导入成功")
        
        from gui.crawler_thread import CrawlerThread
        print("✅ 爬虫线程导入成功")
        
        from modules.manager import module_manager
        print("✅ 模组管理器导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用组件导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 txt_clear 修复验证测试")
    print("="*80)
    
    tests = [
        ("text_cleaner模块导入", test_text_cleaner_import),
        ("text_cleaner函数功能", test_text_cleaner_functions),
        ("爬虫模块导入", test_crawler_import),
        ("txt_clear引用检查", test_no_txt_clear_references),
        ("应用组件导入", test_application_startup)
    ]
    
    results = {}
    passed = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "="*80)
    print("🎯 测试结果汇总")
    print("="*80)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("\n🎉 txt_clear 修复验证通过！")
        print("✅ 所有 txt_clear 引用已修复为 text_cleaner")
        print("✅ text_cleaner 模块功能正常")
        print("✅ 爬虫模块导入正常")
        print("✅ 应用组件导入正常")
        print("\n🚀 现在可以正常使用应用，不会再出现 txt_clear 未定义错误")
    else:
        print(f"\n⚠️ 仍有 {len(tests) - passed} 个问题需要解决")
    
    return passed == len(tests)

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
