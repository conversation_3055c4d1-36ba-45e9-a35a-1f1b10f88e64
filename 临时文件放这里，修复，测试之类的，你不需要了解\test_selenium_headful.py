#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Selenium有头模式测试翻页功能
"""

import time
import sys
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_selenium_headful():
    """使用Selenium有头模式测试翻页功能"""
    print("🧪 使用Selenium有头模式测试翻页功能")
    print("=" * 60)
    
    # 配置Chrome选项
    chrome_options = Options()
    # 不使用无头模式，显示真实浏览器
    # chrome_options.add_argument("--headless")  # 注释掉无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 设置用户代理，模拟真实浏览器
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    driver = None
    
    try:
        # 启动Chrome浏览器
        print("🚀 启动Chrome浏览器（有头模式）...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 设置窗口大小
        driver.set_window_size(1920, 1080)
        
        # 执行脚本隐藏webdriver特征
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
        print(f"📋 访问网站: {url}")
        
        # 访问页面
        driver.get(url)
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(5)
        
        print("✅ 页面加载完成")
        print(f"📋 当前页面标题: {driver.title}")
        print(f"📋 当前页面URL: {driver.current_url}")
        
        # 滚动到页面底部，确保所有内容都加载
        print("📜 滚动到页面底部...")
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        
        # 查找所有可能的翻页元素
        print("\n🔍 查找翻页相关元素...")
        
        # 1. 查找包含"下一页"文本的所有元素
        try:
            elements_with_next = driver.find_elements(By.XPATH, "//*[contains(text(), '下一页')]")
            if elements_with_next:
                print(f"  ✅ 找到 {len(elements_with_next)} 个包含'下一页'的元素:")
                for i, elem in enumerate(elements_with_next):
                    try:
                        tag_name = elem.tag_name
                        text = elem.text
                        is_displayed = elem.is_displayed()
                        is_enabled = elem.is_enabled()
                        
                        print(f"    [{i+1}] 标签: {tag_name}")
                        print(f"        文本: '{text}'")
                        print(f"        可见: {is_displayed}")
                        print(f"        可用: {is_enabled}")
                        
                        # 获取元素属性
                        try:
                            class_name = elem.get_attribute('class')
                            id_attr = elem.get_attribute('id')
                            onclick = elem.get_attribute('onclick')
                            href = elem.get_attribute('href')
                            
                            print(f"        class: '{class_name}'")
                            print(f"        id: '{id_attr}'")
                            print(f"        onclick: '{onclick}'")
                            print(f"        href: '{href}'")
                        except Exception as e:
                            print(f"        获取属性失败: {e}")
                        
                        print()
                        
                    except Exception as e:
                        print(f"    [{i+1}] 获取元素信息失败: {e}")
            else:
                print("  ❌ 未找到包含'下一页'的元素")
        except Exception as e:
            print(f"  ❌ 查找'下一页'元素失败: {e}")
        
        # 2. 查找页码相关元素
        print("🔍 查找页码相关元素...")
        try:
            # 查找包含页码信息的元素
            page_info_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '/') and contains(text(), '页')]")
            if page_info_elements:
                print(f"  ✅ 找到 {len(page_info_elements)} 个页码信息元素:")
                for i, elem in enumerate(page_info_elements):
                    try:
                        text = elem.text
                        print(f"    [{i+1}] 页码信息: '{text}'")
                    except Exception as e:
                        print(f"    [{i+1}] 获取页码信息失败: {e}")
            else:
                print("  ❌ 未找到页码信息元素")
        except Exception as e:
            print(f"  ❌ 查找页码元素失败: {e}")
        
        # 3. 查找所有input按钮
        print("\n🔍 查找所有input按钮...")
        try:
            input_buttons = driver.find_elements(By.TAG_NAME, "input")
            if input_buttons:
                print(f"  ✅ 找到 {len(input_buttons)} 个input元素:")
                for i, elem in enumerate(input_buttons):
                    try:
                        input_type = elem.get_attribute('type')
                        input_value = elem.get_attribute('value')
                        input_name = elem.get_attribute('name')
                        is_displayed = elem.is_displayed()
                        is_enabled = elem.is_enabled()
                        
                        print(f"    [{i+1}] type: '{input_type}', value: '{input_value}', name: '{input_name}'")
                        print(f"        可见: {is_displayed}, 可用: {is_enabled}")
                        
                        # 如果是翻页相关的按钮，尝试点击
                        if input_value and any(keyword in input_value for keyword in ['下一页', 'next', '确定']):
                            print(f"        🎯 这可能是翻页按钮！")
                            
                            if is_displayed and is_enabled:
                                print(f"        🎯 尝试点击按钮...")
                                
                                # 获取点击前的URL
                                current_url = driver.current_url
                                print(f"        📋 点击前URL: {current_url}")
                                
                                try:
                                    # 滚动到元素位置
                                    driver.execute_script("arguments[0].scrollIntoView(true);", elem)
                                    time.sleep(1)
                                    
                                    # 点击元素
                                    elem.click()
                                    print("        ✅ 点击成功")
                                    
                                    # 等待页面变化
                                    time.sleep(5)
                                    
                                    # 检查URL是否改变
                                    new_url = driver.current_url
                                    print(f"        📋 点击后URL: {new_url}")
                                    
                                    if new_url != current_url:
                                        print("        🎉 页面URL发生了变化，翻页成功！")
                                        
                                        # 检查页面内容
                                        new_title = driver.title
                                        print(f"        📋 新页面标题: {new_title}")
                                        
                                        # 查找页码信息确认翻页
                                        page_source = driver.page_source
                                        if "第2页" in page_source or "2/" in page_source:
                                            print("        🎉 确认翻页成功，已到达第2页！")
                                            return True
                                        else:
                                            print("        ⚠️ URL变化了但可能不是翻页")
                                    else:
                                        print("        ⚠️ URL没有变化，可能是AJAX翻页")
                                        
                                        # 检查页面内容是否变化
                                        page_source = driver.page_source
                                        if "第2页" in page_source or "2/" in page_source:
                                            print("        🎉 AJAX翻页成功，已到达第2页！")
                                            return True
                                        else:
                                            print("        ❌ 页面内容没有变化")
                                    
                                except Exception as click_error:
                                    print(f"        ❌ 点击失败: {click_error}")
                            else:
                                print(f"        ⚠️ 按钮不可见或不可用")
                        
                        print()
                        
                    except Exception as e:
                        print(f"    [{i+1}] 获取input信息失败: {e}")
            else:
                print("  ❌ 未找到input元素")
        except Exception as e:
            print(f"  ❌ 查找input元素失败: {e}")
        
        # 4. 检查iframe
        print("\n🔍 检查iframe...")
        try:
            iframes = driver.find_elements(By.TAG_NAME, "iframe")
            if iframes:
                print(f"  ✅ 找到 {len(iframes)} 个iframe")
                for i, iframe in enumerate(iframes):
                    src = iframe.get_attribute('src')
                    print(f"    [{i+1}] src: {src}")
                    
                    # 尝试切换到iframe
                    try:
                        driver.switch_to.frame(iframe)
                        iframe_source = driver.page_source
                        if "下一页" in iframe_source:
                            print(f"    🎉 iframe {i+1} 中找到'下一页'!")
                        driver.switch_to.default_content()  # 切换回主页面
                    except Exception as e:
                        print(f"    ⚠️ 无法访问iframe {i+1}: {e}")
                        driver.switch_to.default_content()  # 确保切换回主页面
            else:
                print("  ❌ 未找到iframe")
        except Exception as e:
            print(f"  ❌ 检查iframe失败: {e}")
        
        print("\n📊 测试完成")
        
        # 保持浏览器打开，方便手动检查
        print("\n⏳ 保持浏览器打开30秒，请手动检查页面...")
        print("💡 您可以手动尝试点击翻页按钮，观察是否有效")
        time.sleep(30)
        
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
        
    finally:
        if driver:
            print("🔚 关闭浏览器")
            driver.quit()

if __name__ == "__main__":
    success = test_selenium_headful()
    if success:
        print("🎉 翻页测试成功！")
    else:
        print("❌ 翻页测试失败")
