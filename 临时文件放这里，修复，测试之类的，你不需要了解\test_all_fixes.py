#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有修复的问题
"""

def test_problem_1():
    """测试问题1: ModuleEditDialog缺少content_type_combo属性"""
    print("🔍 测试问题1: ModuleEditDialog属性...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        import sys
        
        # 创建QApplication（如果不存在）
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        from crawler_gui_new import ModuleEditDialog
        
        # 创建对话框实例
        dialog = ModuleEditDialog()
        
        # 检查关键属性是否存在
        required_attrs = [
            'content_type_combo',
            'title_type_combo', 
            'date_type_combo',
            'source_type_combo',
            'max_workers_spin',
            'collect_links_check',
            'file_format_combo'
        ]
        
        missing_attrs = []
        for attr in required_attrs:
            if not hasattr(dialog, attr):
                missing_attrs.append(attr)
        
        if missing_attrs:
            print(f"❌ 缺少属性: {missing_attrs}")
            return False
        else:
            print("✅ 所有必需属性都存在")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_problem_2():
    """测试问题2: 模组配置加载"""
    print("\n🔍 测试问题2: 模组配置加载...")
    
    try:
        # 测试模组管理器
        from module_manager import module_manager
        
        # 测试加载模组
        modules = module_manager.list_modules()
        print(f"✅ 成功加载 {len(modules)} 个模组: {modules}")
        
        # 测试获取模组信息
        if modules:
            first_module = modules[0]
            info = module_manager.get_module_info(first_module)
            if info:
                print(f"✅ 成功获取模组信息: {first_module}")
                return True
            else:
                print(f"❌ 无法获取模组信息: {first_module}")
                return False
        else:
            print("❌ 没有可用的模组")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_problem_3():
    """测试问题3: 进度条功能"""
    print("\n🔍 测试问题3: 进度条功能...")
    
    try:
        from gui_crawler_thread import CrawlerThread
        
        # 检查进度信号是否存在
        if hasattr(CrawlerThread, 'progress_signal'):
            print("✅ CrawlerThread 有 progress_signal")
        else:
            print("❌ CrawlerThread 缺少 progress_signal")
            return False
        
        # 检查GUI中的进度回调
        from crawler_gui_new import CrawlerGUI
        if hasattr(CrawlerGUI, 'on_progress_update'):
            print("✅ CrawlerGUI 有 on_progress_update 方法")
            return True
        else:
            print("❌ CrawlerGUI 缺少 on_progress_update 方法")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_problem_4():
    """测试问题4: Excel文件支持"""
    print("\n🔍 测试问题4: Excel文件支持...")
    
    try:
        # 检查文件对话框过滤器
        # 这个需要在实际GUI中测试，这里只检查代码存在性
        with open("crawler_gui_new.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        if "*.xlsx" in content and "Excel文件" in content:
            print("✅ 文件对话框支持Excel格式")
            return True
        else:
            print("❌ 文件对话框不支持Excel格式")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_problem_5():
    """测试问题5: Playwright优化配置"""
    print("\n🔍 测试问题5: Playwright优化配置...")
    
    try:
        # 检查优化配置是否可导入
        from playwright_optimized_config import OptimizedPlaywrightConfig
        print("✅ OptimizedPlaywrightConfig 导入成功")
        
        # 检查crawler是否引用了优化配置
        with open("crawler.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        if "OptimizedPlaywrightConfig" in content and "OPTIMIZED_CONFIG_AVAILABLE" in content:
            print("✅ crawler.py 已引用优化配置")
            return True
        else:
            print("❌ crawler.py 未正确引用优化配置")
            return False
            
    except ImportError:
        print("❌ OptimizedPlaywrightConfig 导入失败")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("所有问题修复验证测试")
    print("=" * 50)
    
    tests = [
        ("问题1: ModuleEditDialog属性", test_problem_1),
        ("问题2: 模组配置加载", test_problem_2),
        ("问题3: 进度条功能", test_problem_3),
        ("问题4: Excel文件支持", test_problem_4),
        ("问题5: Playwright优化配置", test_problem_5),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个问题已修复")
    
    if passed == len(results):
        print("\n🎉 所有问题都已修复！")
        print("\n修复内容:")
        print("✅ 问题1: 添加了ModuleEditDialog缺失的UI组件")
        print("✅ 问题2: 模组配置系统正常工作")
        print("✅ 问题3: 进度条功能已连接")
        print("✅ 问题4: 支持Excel格式的失败文件")
        print("✅ 问题5: 集成了Playwright优化配置")
        
        print("\n现在可以:")
        print("1. 正常使用模组配置功能")
        print("2. 查看实时爬取进度")
        print("3. 处理Excel格式的失败文件")
        print("4. 享受优化的Playwright性能")
        
    else:
        print("\n⚠️ 部分问题仍需修复")

if __name__ == "__main__":
    main()
