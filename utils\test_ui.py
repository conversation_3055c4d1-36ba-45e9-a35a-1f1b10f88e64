#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI布局测试脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_layout():
    """测试UI布局"""
    try:
        from gui.main_window import CrawlerGUI
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        window.show()
        
        print("✅ UI布局测试成功启动")
        print("📋 UI优化内容:")
        print("  - 使用分割器实现可调整的左右布局")
        print("  - 重新组织标签页结构，添加图标")
        print("  - 优化配置管理组，使用网格布局")
        print("  - 改进URL设置组，分组显示")
        print("  - 优化选择器设置组，分列表页和文章页")
        print("  - 增强控制面板，添加状态显示")
        print("  - 改进日志显示区，添加过滤和导出功能")
        print("  - 保留原有CSS样式，提升用户体验")
        
        # 不启动事件循环，只是测试创建
        return True
        
    except Exception as e:
        print(f"❌ UI布局测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ui_layout()
    if success:
        print("\n🎉 UI布局优化完成！")
        print("主要改进:")
        print("1. 📱 响应式布局 - 使用分割器支持窗口大小调整")
        print("2. 🎨 视觉优化 - 标签页添加图标，分组更清晰")
        print("3. 📋 内容组织 - 重新整理配置项的逻辑分组")
        print("4. 🎮 交互改进 - 增强控制面板和日志功能")
        print("5. 💾 保留样式 - 维持原有CSS样式的一致性")
    else:
        print("\n❌ UI布局测试失败，请检查错误信息")
