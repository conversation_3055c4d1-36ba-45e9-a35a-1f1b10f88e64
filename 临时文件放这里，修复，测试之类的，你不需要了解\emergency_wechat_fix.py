#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急修复微信公众号爬取问题
"""

import asyncio
import csv
import os
from playwright.async_api import async_playwright
import time

async def emergency_crawl_wechat(url, max_retries=3):
    """紧急爬取微信公众号文章"""
    print(f"\n🔍 处理: {url}")
    
    for attempt in range(max_retries):
        try:
            async with async_playwright() as p:
                # 使用更强的反检测配置
                browser = await p.chromium.launch(
                    headless=False,  # 非无头模式
                    args=[
                        "--no-sandbox",
                        "--disable-setuid-sandbox", 
                        "--disable-dev-shm-usage",
                        "--disable-blink-features=AutomationControlled",
                        "--exclude-switches=enable-automation",
                        "--disable-extensions",
                        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                    ]
                )
                
                context = await browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                )
                
                # 强化反检测
                await context.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                    window.chrome = { runtime: {} };
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5],
                    });
                """)
                
                page = await context.new_page()
                
                try:
                    print(f"📱 尝试 {attempt + 1}/{max_retries}")
                    
                    # 访问页面
                    response = await page.goto(url, timeout=90000, wait_until="networkidle")
                    print(f"📊 状态码: {response.status}")
                    
                    # 等待页面完全加载
                    await page.wait_for_load_state('networkidle', timeout=45000)
                    await asyncio.sleep(8)  # 长时间等待
                    
                    # 检查URL是否被重定向
                    current_url = page.url
                    if "mp.weixin.qq.com" not in current_url:
                        print(f"⚠️ 重定向到: {current_url}")
                        return None
                    
                    # 获取页面标题
                    title = await page.title()
                    print(f"📝 标题: {title}")
                    
                    # 尝试多种内容选择器
                    content_selectors = [
                        "#js_content",
                        ".rich_media_content",
                        "#img-content", 
                        ".rich_media_area_primary",
                        "[data-role='outer']",
                        ".rich_media_wrp",
                        ".rich_media_area_primary .rich_media_content",
                        "div[data-role='outer'] div[data-role='inner']"
                    ]
                    
                    content = ""
                    for selector in content_selectors:
                        try:
                            element = await page.query_selector(selector)
                            if element:
                                text = await element.inner_text()
                                if text and len(text.strip()) > 100:
                                    content = text.strip()
                                    print(f"✅ 使用选择器: {selector}")
                                    print(f"📄 内容长度: {len(content)} 字符")
                                    break
                        except:
                            continue
                    
                    if not content:
                        print("⚠️ 所有选择器都未找到内容")
                        
                        # 尝试获取整个页面的文本内容
                        try:
                            body_text = await page.evaluate("document.body.innerText")
                            if body_text and len(body_text.strip()) > 500:
                                content = body_text.strip()
                                print(f"✅ 使用整页内容: {len(content)} 字符")
                        except:
                            pass
                    
                    if content and len(content) > 100:
                        # 提取其他信息
                        source = ""
                        publish_time = ""
                        
                        # 尝试提取来源
                        source_selectors = [
                            ".rich_media_meta_nickname",
                            "#js_name", 
                            ".profile_nickname"
                        ]
                        
                        for selector in source_selectors:
                            try:
                                element = await page.query_selector(selector)
                                if element:
                                    source = await element.inner_text()
                                    if source:
                                        break
                            except:
                                continue
                        
                        # 尝试提取发布时间
                        time_selectors = [
                            "#publish_time",
                            ".rich_media_meta_text",
                            "#post-date"
                        ]
                        
                        for selector in time_selectors:
                            try:
                                element = await page.query_selector(selector)
                                if element:
                                    publish_time = await element.inner_text()
                                    if publish_time:
                                        break
                            except:
                                continue
                        
                        result = {
                            'url': url,
                            'title': title,
                            'content': content,
                            'source': source,
                            'publish_time': publish_time,
                            'success': True
                        }
                        
                        print(f"✅ 成功提取内容!")
                        return result
                    
                    else:
                        print(f"❌ 内容为空或过短: {len(content) if content else 0}")
                        if attempt < max_retries - 1:
                            print("⏳ 等待后重试...")
                            await asyncio.sleep(10)
                            continue
                
                finally:
                    await browser.close()
                    
        except Exception as e:
            print(f"❌ 尝试 {attempt + 1} 失败: {e}")
            if attempt < max_retries - 1:
                await asyncio.sleep(5)
    
    return None

async def process_failed_wechat_urls():
    """处理失败的微信公众号URL"""
    failed_file = "articles/重试结果_failed.csv"
    
    if not os.path.exists(failed_file):
        print(f"❌ 文件不存在: {failed_file}")
        return
    
    # 读取失败的URL
    urls = []
    with open(failed_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row.get('failed_url'):
                urls.append(row['failed_url'])
    
    print(f"📋 找到 {len(urls)} 个失败的URL")
    
    # 只处理前5个进行测试
    test_urls = urls[:5]
    print(f"🧪 测试处理前 {len(test_urls)} 个URL")
    
    results = []
    success_count = 0
    
    for i, url in enumerate(test_urls):
        print(f"\n📊 进度: {i+1}/{len(test_urls)}")
        
        result = await emergency_crawl_wechat(url)
        
        if result:
            results.append(result)
            success_count += 1
            
            # 保存成功的文章
            safe_title = "".join(c for c in result['title'] if c.isalnum() or c in (' ', '-', '_')).strip()
            if not safe_title:
                safe_title = f"article_{int(time.time())}"
            
            filename = f"emergency_{safe_title}.txt"
            filepath = os.path.join("articles", filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"标题: {result['title']}\n")
                f.write(f"来源: {result['source']}\n")
                f.write(f"发布时间: {result['publish_time']}\n")
                f.write(f"URL: {result['url']}\n")
                f.write("-" * 50 + "\n")
                f.write(result['content'])
            
            print(f"💾 已保存: {filename}")
        
        # 间隔等待
        if i < len(test_urls) - 1:
            print("⏳ 等待10秒...")
            await asyncio.sleep(10)
    
    print(f"\n🎉 处理完成!")
    print(f"📊 成功: {success_count}/{len(test_urls)}")
    print(f"📈 成功率: {(success_count/len(test_urls)*100):.1f}%")
    
    # 保存结果摘要
    with open("articles/emergency_results.txt", 'w', encoding='utf-8') as f:
        f.write(f"紧急修复结果\n")
        f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总计: {len(test_urls)}\n")
        f.write(f"成功: {success_count}\n")
        f.write(f"失败: {len(test_urls) - success_count}\n")
        f.write(f"成功率: {(success_count/len(test_urls)*100):.1f}%\n\n")
        
        for result in results:
            f.write(f"✅ {result['title']}\n")
            f.write(f"   URL: {result['url']}\n")
            f.write(f"   内容长度: {len(result['content'])} 字符\n\n")

if __name__ == "__main__":
    print("🚨 微信公众号紧急修复工具")
    print("=" * 40)
    
    try:
        asyncio.run(process_failed_wechat_urls())
    except KeyboardInterrupt:
        print("\n⏹️ 被用户中断")
    except Exception as e:
        print(f"\n❌ 出错: {e}")
