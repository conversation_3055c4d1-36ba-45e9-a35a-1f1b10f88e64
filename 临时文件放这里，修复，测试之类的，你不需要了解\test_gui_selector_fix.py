#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的GUI选择器测试功能
验证页面关闭问题是否已解决，以及AI分析功能是否正常
"""

import sys
import os
import asyncio

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_selector_dialog_import():
    """测试选择器对话框导入"""
    print("🧪 测试选择器对话框导入...")
    
    try:
        from gui.selector_test_dialog import SelectorTestDialog, SelectorTestThread, AIAnalysisThread
        print("✅ 选择器对话框类导入成功")
        print("✅ 选择器测试线程导入成功")
        print("✅ AI分析线程导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_ai_analysis_thread():
    """测试AI分析线程功能"""
    print("\n🤖 测试AI分析线程功能...")
    
    try:
        from gui.selector_test_dialog import AIAnalysisThread
        
        # 创建AI分析线程
        thread = AIAnalysisThread("https://www.baidu.com", "content")
        print("✅ AI分析线程创建成功")
        
        # 检查默认选择器功能
        default_selectors = thread.get_default_selectors("content")
        print(f"✅ 默认内容选择器: {len(default_selectors)} 个")
        print(f"   示例: {default_selectors[:3]}")
        
        title_selectors = thread.get_default_selectors("title")
        print(f"✅ 默认标题选择器: {len(title_selectors)} 个")
        print(f"   示例: {title_selectors[:3]}")
        
        return True
    except Exception as e:
        print(f"❌ AI分析线程测试失败: {e}")
        return False

async def test_ai_analysis_async():
    """测试AI分析异步功能"""
    print("\n🔬 测试AI分析异步功能...")
    
    try:
        from gui.selector_test_dialog import AIAnalysisThread
        
        # 创建线程实例
        thread = AIAnalysisThread("https://httpbin.org/html", "content")
        
        # 直接调用异步方法进行测试
        print("🚀 开始异步AI分析...")
        result = await thread.ai_analysis_async()
        
        print("✅ AI分析完成")
        print(f"📋 字段名称: {result.get('field_name', 'N/A')}")
        print(f"🎯 置信度: {result.get('confidence', 0):.1%}")
        print(f"📊 推荐选择器数量: {len(result.get('recommended_selectors', []))}")
        
        if result.get('recommended_selectors'):
            print(f"🔍 推荐选择器示例: {result['recommended_selectors'][:3]}")
        
        if result.get('test_results'):
            test_success = result['test_results'].get('success', False)
            print(f"🧪 测试结果: {'✅ 成功' if test_success else '❌ 失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI分析异步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_selector_test_thread():
    """测试选择器测试线程"""
    print("\n🧪 测试选择器测试线程...")
    
    try:
        from gui.selector_test_dialog import SelectorTestThread
        
        # 测试数据
        test_selectors = {
            "content": ["body", ".content", "main"],
            "title": ["title", "h1", ".title"]
        }
        
        # 创建测试线程
        thread = SelectorTestThread("https://httpbin.org/html", test_selectors)
        print("✅ 选择器测试线程创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 选择器测试线程测试失败: {e}")
        return False

async def test_selector_test_async():
    """测试选择器测试异步功能"""
    print("\n🔬 测试选择器测试异步功能...")
    
    try:
        from gui.selector_test_dialog import SelectorTestThread
        
        # 测试数据
        test_selectors = {
            "content": ["body", ".content"],
            "title": ["title", "h1"]
        }
        
        # 创建线程实例
        thread = SelectorTestThread("https://httpbin.org/html", test_selectors)
        
        # 直接调用异步方法进行测试
        print("🚀 开始异步选择器测试...")
        result = await thread.test_selectors_async()
        
        print("✅ 选择器测试完成")
        print(f"📊 测试字段数量: {len(result)}")
        
        for field_name, field_results in result.items():
            success_count = sum(1 for r in field_results if r['status'] == 'success')
            print(f"  {field_name}: {success_count}/{len(field_results)} 个选择器成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 选择器测试异步功能失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成...")
    
    try:
        # 检查是否可以创建QApplication
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ QApplication 创建成功")
        
        # 测试对话框创建（不显示）
        from gui.selector_test_dialog import SelectorTestDialog
        
        test_selectors = {
            "content": ["body", ".content"],
            "title": ["title", "h1"]
        }
        
        # 创建对话框但不显示
        dialog = SelectorTestDialog(None, "https://httpbin.org/html", test_selectors)
        print("✅ 选择器测试对话框创建成功")
        
        # 检查AI分析按钮是否存在
        ai_buttons = [child for child in dialog.findChildren(type(dialog)) 
                     if hasattr(child, 'text') and '🤖' in str(child.text())]
        
        if ai_buttons:
            print("✅ AI分析按钮已添加")
        else:
            print("⚠️ AI分析按钮未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 GUI选择器测试功能修复验证")
    print("验证页面关闭问题修复和AI分析功能")
    print("=" * 60)
    
    results = []
    
    # 基础导入测试
    results.append(test_selector_dialog_import())
    
    # AI分析线程测试
    results.append(test_ai_analysis_thread())
    
    # 选择器测试线程测试
    results.append(test_selector_test_thread())
    
    # GUI集成测试
    results.append(test_gui_integration())
    
    # 异步功能测试
    try:
        print("\n🔬 开始异步功能测试...")
        
        # AI分析异步测试
        ai_result = await test_ai_analysis_async()
        results.append(ai_result)
        
        # 选择器测试异步测试
        selector_result = await test_selector_test_async()
        results.append(selector_result)
        
    except Exception as e:
        print(f"❌ 异步功能测试失败: {e}")
        results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r)
    total_count = len(results)
    success_rate = success_count / total_count * 100
    
    print(f"✅ 成功: {success_count}/{total_count} ({success_rate:.1f}%)")
    print(f"❌ 失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 所有测试通过！GUI选择器功能已修复并增强！")
        print("🤖 AI分析功能已成功集成")
        print("🛡️ 页面关闭问题已解决")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步检查")
    
    return success_count == total_count

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
