#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试选择器验证修复
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_selector_validation():
    """测试选择器验证功能"""
    print("🧪 === 测试选择器验证修复 ===")
    
    try:
        from gui.utils import ConfigValidator
        
        # 测试配置1：正常的列表格式
        test_config1 = {
            'list_container_selector': '.main',
            'article_item_selector': 'li a',
            'title_selectors': ['h1', '.title', '#article-title'],
            'content_selectors': ['.TRS_Editor', '.article_cont', '.content'],
            'date_selectors': ['.date', '.publish-time'],
            'source_selectors': ['.source', '.author']
        }
        
        print("\n1️⃣ 测试正常列表格式配置:")
        errors1 = ConfigValidator.validate_selectors(test_config1)
        if errors1:
            print(f"❌ 验证失败: {errors1}")
        else:
            print("✅ 验证通过")
        
        # 测试配置2：字符串格式（应该自动转换）
        test_config2 = {
            'list_container_selector': '.main',
            'article_item_selector': 'li a',
            'title_selectors': 'h1, .title, #article-title',
            'content_selectors': '.TRS_Editor, .article_cont, .content',
            'date_selectors': '.date, .publish-time',
            'source_selectors': '.source, .author'
        }
        
        print("\n2️⃣ 测试字符串格式配置:")
        errors2 = ConfigValidator.validate_selectors(test_config2)
        if errors2:
            print(f"❌ 验证失败: {errors2}")
        else:
            print("✅ 验证通过")
        
        # 测试配置3：空配置
        test_config3 = {
            'list_container_selector': '',
            'article_item_selector': '',
            'title_selectors': [],
            'content_selectors': [],
            'date_selectors': [],
            'source_selectors': []
        }
        
        print("\n3️⃣ 测试空配置:")
        errors3 = ConfigValidator.validate_selectors(test_config3)
        if errors3:
            print(f"❌ 验证失败: {errors3}")
        else:
            print("✅ 验证通过（空配置是允许的）")
        
        # 测试配置4：包含无效选择器
        test_config4 = {
            'list_container_selector': '.main',
            'article_item_selector': 'li a',
            'title_selectors': ['h1', '', '.title'],  # 包含空字符串
            'content_selectors': ['.TRS_Editor', 'invalid>>selector', '.content'],  # 包含无效选择器
            'date_selectors': ['.date'],
            'source_selectors': ['.source']
        }
        
        print("\n4️⃣ 测试包含无效选择器的配置:")
        errors4 = ConfigValidator.validate_selectors(test_config4)
        if errors4:
            print(f"⚠️ 发现错误（预期的）: {errors4}")
        else:
            print("✅ 验证通过")
        
        print("\n" + "="*50)
        print("🎯 验证修复测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validate_selector_function():
    """测试单个选择器验证函数"""
    print("\n🔍 === 测试单个选择器验证函数 ===")
    
    try:
        from gui.utils import validate_selector
        
        test_cases = [
            ('.TRS_Editor', '类选择器'),
            ('#content', 'ID选择器'),
            ('div.article_con', '元素+类选择器'),
            ('div[class*="content"]', '属性选择器'),
            ('h1, .title', '多选择器'),
            ('div > p', '子选择器'),
            ('', '空选择器'),
            ('invalid>>selector', '无效选择器')
        ]
        
        for selector, description in test_cases:
            valid, error = validate_selector(selector, description)
            status = "✅" if valid else "❌"
            print(f"{status} {description}: '{selector}' - {error if error else '有效'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 单个选择器验证测试失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_selector_validation()
    success2 = test_validate_selector_function()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！选择器验证修复成功。")
    else:
        print("\n❌ 部分测试失败，需要进一步检查。")
