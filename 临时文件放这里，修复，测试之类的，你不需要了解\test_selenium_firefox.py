#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Selenium Firefox驱动测试翻页功能
"""

import time
import sys
import os

def test_selenium_firefox():
    """使用Selenium Firefox驱动测试翻页功能"""
    print("🧪 使用Selenium Firefox驱动测试翻页功能")
    print("=" * 60)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.firefox.options import Options
        from selenium.webdriver.firefox.service import Service
        from webdriver_manager.firefox import GeckoDriverManager
        
        print("✅ Selenium导入成功")
        
        # 配置Firefox选项
        firefox_options = Options()
        # 有头模式 - 不添加headless参数
        firefox_options.add_argument("--disable-blink-features=AutomationControlled")
        
        print("🚀 启动Firefox浏览器...")
        
        # 使用webdriver_manager自动下载和管理GeckoDriver
        service = Service(GeckoDriverManager().install())
        driver = webdriver.Firefox(service=service, options=firefox_options)
        
        print("✅ Firefox浏览器启动成功")
        
        try:
            # 设置窗口大小
            driver.set_window_size(1920, 1080)
            
            url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
            print(f"📋 访问网站: {url}")
            
            driver.get(url)
            print("⏳ 等待页面加载...")
            time.sleep(8)  # 等待更长时间
            
            print("✅ 页面加载完成")
            print(f"📋 页面标题: {driver.title}")
            print(f"📋 当前URL: {driver.current_url}")
            
            # 滚动到页面底部，确保所有内容都加载
            print("📜 滚动到页面底部...")
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            
            # 再次滚动，确保动态内容加载
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            
            # 查找翻页相关元素
            print("\n🔍 查找翻页相关元素...")
            
            # 1. 查找包含"下一页"文本的所有元素
            try:
                elements_with_next = driver.find_elements(By.XPATH, "//*[contains(text(), '下一页')]")
                if elements_with_next:
                    print(f"  ✅ 找到 {len(elements_with_next)} 个包含'下一页'的元素:")
                    for i, elem in enumerate(elements_with_next):
                        try:
                            tag_name = elem.tag_name
                            text = elem.text
                            is_displayed = elem.is_displayed()
                            is_enabled = elem.is_enabled()
                            
                            print(f"    [{i+1}] 标签: {tag_name}")
                            print(f"        文本: '{text}'")
                            print(f"        可见: {is_displayed}")
                            print(f"        可用: {is_enabled}")
                            
                            # 获取元素属性
                            try:
                                class_name = elem.get_attribute('class')
                                id_attr = elem.get_attribute('id')
                                onclick = elem.get_attribute('onclick')
                                href = elem.get_attribute('href')
                                
                                print(f"        class: '{class_name}'")
                                print(f"        id: '{id_attr}'")
                                print(f"        onclick: '{onclick}'")
                                print(f"        href: '{href}'")
                            except Exception as e:
                                print(f"        获取属性失败: {e}")
                            
                            print()
                            
                        except Exception as e:
                            print(f"    [{i+1}] 获取元素信息失败: {e}")
                else:
                    print("  ❌ 未找到包含'下一页'的元素")
            except Exception as e:
                print(f"  ❌ 查找'下一页'元素失败: {e}")
            
            # 2. 查找页码相关元素
            print("🔍 查找页码相关元素...")
            try:
                # 查找包含页码信息的元素
                page_info_patterns = [
                    "//*[contains(text(), '/') and contains(text(), '页')]",
                    "//*[contains(text(), '第') and contains(text(), '页')]",
                    "//*[contains(text(), '1/12')]",
                    "//*[contains(text(), '第1页')]"
                ]
                
                for pattern in page_info_patterns:
                    page_info_elements = driver.find_elements(By.XPATH, pattern)
                    if page_info_elements:
                        print(f"  ✅ 找到 {len(page_info_elements)} 个页码信息元素 (模式: {pattern}):")
                        for i, elem in enumerate(page_info_elements):
                            try:
                                text = elem.text
                                print(f"    [{i+1}] 页码信息: '{text}'")
                            except Exception as e:
                                print(f"    [{i+1}] 获取页码信息失败: {e}")
                        break
                else:
                    print("  ❌ 未找到页码信息元素")
            except Exception as e:
                print(f"  ❌ 查找页码元素失败: {e}")
            
            # 3. 查找所有input按钮
            print("\n🔍 查找所有input按钮...")
            try:
                input_buttons = driver.find_elements(By.TAG_NAME, "input")
                if input_buttons:
                    print(f"  ✅ 找到 {len(input_buttons)} 个input元素:")
                    for i, elem in enumerate(input_buttons):
                        try:
                            input_type = elem.get_attribute('type')
                            input_value = elem.get_attribute('value')
                            input_name = elem.get_attribute('name')
                            is_displayed = elem.is_displayed()
                            is_enabled = elem.is_enabled()
                            
                            print(f"    [{i+1}] type: '{input_type}', value: '{input_value}', name: '{input_name}'")
                            print(f"        可见: {is_displayed}, 可用: {is_enabled}")
                            
                            # 如果是翻页相关的按钮，尝试点击
                            if input_value and any(keyword in str(input_value) for keyword in ['下一页', 'next', '确定', '>']):
                                print(f"        🎯 这可能是翻页按钮！")
                                
                                if is_displayed and is_enabled:
                                    print(f"        🎯 尝试点击按钮...")
                                    
                                    # 获取点击前的URL和页面源码
                                    current_url = driver.current_url
                                    current_source = driver.page_source
                                    print(f"        📋 点击前URL: {current_url}")
                                    
                                    try:
                                        # 滚动到元素位置
                                        driver.execute_script("arguments[0].scrollIntoView(true);", elem)
                                        time.sleep(1)
                                        
                                        # 点击元素
                                        elem.click()
                                        print("        ✅ 点击成功")
                                        
                                        # 等待页面变化
                                        time.sleep(5)
                                        
                                        # 检查URL是否改变
                                        new_url = driver.current_url
                                        new_source = driver.page_source
                                        print(f"        📋 点击后URL: {new_url}")
                                        
                                        if new_url != current_url:
                                            print("        🎉 页面URL发生了变化，翻页成功！")
                                            
                                            # 检查页面内容
                                            new_title = driver.title
                                            print(f"        📋 新页面标题: {new_title}")
                                            
                                            # 查找页码信息确认翻页
                                            if "第2页" in new_source or "2/" in new_source or "2/12" in new_source:
                                                print("        🎉 确认翻页成功，已到达第2页！")
                                                
                                                # 截图保存
                                                driver.save_screenshot("firefox_pagination_success.png")
                                                print("        📸 已保存成功截图: firefox_pagination_success.png")
                                                
                                                return True
                                            else:
                                                print("        ⚠️ URL变化了但可能不是翻页")
                                        elif new_source != current_source:
                                            print("        ⚠️ URL没有变化，但页面内容可能变化了（AJAX翻页）")
                                            
                                            # 检查页面内容是否变化
                                            if "第2页" in new_source or "2/" in new_source or "2/12" in new_source:
                                                print("        🎉 AJAX翻页成功，已到达第2页！")
                                                
                                                # 截图保存
                                                driver.save_screenshot("firefox_ajax_pagination_success.png")
                                                print("        📸 已保存AJAX翻页成功截图: firefox_ajax_pagination_success.png")
                                                
                                                return True
                                            else:
                                                print("        ❌ 页面内容没有明显变化")
                                        else:
                                            print("        ❌ 没有检测到任何变化")
                                        
                                    except Exception as click_error:
                                        print(f"        ❌ 点击失败: {click_error}")
                                else:
                                    print(f"        ⚠️ 按钮不可见或不可用")
                            
                            print()
                            
                        except Exception as e:
                            print(f"    [{i+1}] 获取input信息失败: {e}")
                else:
                    print("  ❌ 未找到input元素")
            except Exception as e:
                print(f"  ❌ 查找input元素失败: {e}")
            
            # 4. 检查页面源码中是否包含翻页相关内容
            print("\n🔍 检查页面源码...")
            page_source = driver.page_source
            
            pagination_keywords = ["下一页", "第1页", "第2页", "/12页", "确定", "1/12"]
            found_in_source = []
            
            for keyword in pagination_keywords:
                if keyword in page_source:
                    found_in_source.append(keyword)
            
            if found_in_source:
                print(f"  ✅ 页面源码中找到翻页关键词: {found_in_source}")
            else:
                print("  ❌ 页面源码中未找到翻页关键词")
                print("  💡 这可能意味着页面确实没有翻页功能，或者需要特殊操作才能显示")
            
            # 截图保存当前状态
            driver.save_screenshot("firefox_current_page.png")
            print(f"\n📸 已保存当前页面截图: firefox_current_page.png")
            
            print("\n📊 测试完成")
            
            # 保持浏览器打开，方便手动检查
            print("\n⏳ 保持Firefox浏览器打开30秒，请手动检查页面...")
            print("💡 您可以手动尝试点击翻页按钮，观察是否有效")
            time.sleep(30)
            
            return False
            
        finally:
            print("🔚 关闭Firefox浏览器")
            driver.quit()
            
    except ImportError as e:
        print(f"❌ 导入Selenium失败: {e}")
        print("💡 请安装Selenium和Firefox驱动管理器:")
        print("   pip install selenium webdriver-manager")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_selenium_firefox()
    if success:
        print("🎉 Firefox翻页测试成功！")
    else:
        print("❌ Firefox翻页测试失败，但您可以查看截图了解页面状态")
