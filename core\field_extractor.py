#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
灵活字段提取器模块
支持可配置的字段列表，动态增减字段，自定义提取逻辑
"""

import re
import time
import logging
from typing import Dict, List, Any, Optional, Callable
from bs4 import BeautifulSoup
from playwright.async_api import Page

logger = logging.getLogger(__name__)

class FieldExtractor:
    """灵活字段提取器"""
    
    def __init__(self):
        """初始化字段提取器"""
        self.field_configs = {}
        self.custom_extractors = {}
        self._setup_default_fields()
    
    def _setup_default_fields(self):
        """设置默认字段配置"""
        # 基础字段
        self.field_configs = {
            'dateget': {
                'name': '发布日期',
                'type': 'text',
                'selectors': ['.date', '.publish-date', '.article-date', 'time', '.time'],
                'required': False,
                'default': '',
                'extractor': 'date_extractor'
            },
            'source': {
                'name': '来源',
                'type': 'text', 
                'selectors': ['.source', '.author', '.publisher', '.from'],
                'required': False,
                'default': '本站',
                'extractor': 'text_extractor'
            },
            'title': {
                'name': '标题',
                'type': 'text',
                'selectors': ['h1', '.title', '.article-title', 'h2', '.news-title'],
                'required': True,
                'default': '',
                'extractor': 'text_extractor'
            },
            'articlelink': {
                'name': '文章链接',
                'type': 'url',
                'selectors': [],
                'required': True,
                'default': '',
                'extractor': 'url_extractor'
            },
            'content': {
                'name': '内容',
                'type': 'html',
                'selectors': ['.content', '.article-content', '.main-text'],
                'required': True,
                'default': '',
                'extractor': 'content_extractor'
            },
            'classid': {
                'name': '分类ID',
                'type': 'text',
                'selectors': [],
                'required': False,
                'default': '',
                'extractor': 'static_extractor'
            },
            'city': {
                'name': '城市',
                'type': 'text',
                'selectors': [],
                'required': False,
                'default': '上海',
                'extractor': 'static_extractor'
            },
            'getdate': {
                'name': '采集时间',
                'type': 'datetime',
                'selectors': [],
                'required': False,
                'default': '',
                'extractor': 'timestamp_extractor'
            }
        }
        
        # 扩展字段示例
        self.extended_fields = {
            'likes': {
                'name': '点赞数',
                'type': 'number',
                'selectors': ['.like-count', '.praise-num', '[data-likes]'],
                'required': False,
                'default': 0,
                'extractor': 'number_extractor'
            },
            'views': {
                'name': '阅读量',
                'type': 'number', 
                'selectors': ['.view-count', '.read-num', '[data-views]'],
                'required': False,
                'default': 0,
                'extractor': 'number_extractor'
            },
            'price': {
                'name': '价格',
                'type': 'currency',
                'selectors': ['.price', '.cost', '[data-price]'],
                'required': False,
                'default': 0.0,
                'extractor': 'currency_extractor'
            },
            'sales': {
                'name': '成交量',
                'type': 'number',
                'selectors': ['.sales-count', '.sold-num', '[data-sales]'],
                'required': False,
                'default': 0,
                'extractor': 'number_extractor'
            },
            'comments': {
                'name': '评论数',
                'type': 'number',
                'selectors': ['.comment-count', '.reply-num', '[data-comments]'],
                'required': False,
                'default': 0,
                'extractor': 'number_extractor'
            },
            'tags': {
                'name': '标签',
                'type': 'list',
                'selectors': ['.tags', '.keywords', '.labels'],
                'required': False,
                'default': [],
                'extractor': 'list_extractor'
            },
            'rating': {
                'name': '评分',
                'type': 'float',
                'selectors': ['.rating', '.score', '[data-rating]'],
                'required': False,
                'default': 0.0,
                'extractor': 'float_extractor'
            }
        }
    
    def add_field(self, field_name: str, config: Dict[str, Any]):
        """添加自定义字段"""
        self.field_configs[field_name] = config
        logger.info(f"添加字段: {field_name} - {config.get('name', field_name)}")
    
    def remove_field(self, field_name: str):
        """移除字段"""
        if field_name in self.field_configs:
            del self.field_configs[field_name]
            logger.info(f"移除字段: {field_name}")
    
    def get_active_fields(self) -> List[str]:
        """获取当前激活的字段列表"""
        return list(self.field_configs.keys())
    
    def get_headers(self) -> List[str]:
        """获取表头列表"""
        return list(self.field_configs.keys())
    
    def add_custom_extractor(self, name: str, extractor_func: Callable):
        """添加自定义提取器函数"""
        self.custom_extractors[name] = extractor_func
        logger.info(f"添加自定义提取器: {name}")
    
    async def extract_fields(self, page: Page, url: str, content_html: str, 
                           static_values: Dict[str, Any] = None) -> List[Any]:
        """
        提取所有配置的字段值
        
        Args:
            page: Playwright页面对象
            url: 文章URL
            content_html: 页面HTML内容
            static_values: 静态值字典
            
        Returns:
            按字段顺序的值列表
        """
        if static_values is None:
            static_values = {}
            
        soup = BeautifulSoup(content_html, 'html.parser')
        extracted_values = []
        
        for field_name, config in self.field_configs.items():
            try:
                value = await self._extract_single_field(
                    field_name, config, page, url, soup, static_values
                )
                extracted_values.append(value)
            except Exception as e:
                logger.warning(f"提取字段 {field_name} 失败: {e}")
                extracted_values.append(config.get('default', ''))
        
        return extracted_values
    
    async def _extract_single_field(self, field_name: str, config: Dict[str, Any], 
                                   page: Page, url: str, soup: BeautifulSoup,
                                   static_values: Dict[str, Any]) -> Any:
        """提取单个字段值"""
        extractor_name = config.get('extractor', 'text_extractor')
        
        # 检查是否有静态值
        if field_name in static_values:
            return static_values[field_name]
        
        # 使用自定义提取器
        if extractor_name in self.custom_extractors:
            return await self.custom_extractors[extractor_name](
                field_name, config, page, url, soup
            )
        
        # 使用内置提取器
        if hasattr(self, extractor_name):
            extractor_func = getattr(self, extractor_name)
            return await extractor_func(field_name, config, page, url, soup)
        
        # 默认文本提取器
        return await self.text_extractor(field_name, config, page, url, soup)
    
    async def text_extractor(self, field_name: str, config: Dict[str, Any], 
                           page: Page, url: str, soup: BeautifulSoup) -> str:
        """文本提取器"""
        selectors = config.get('selectors', [])
        
        for selector in selectors:
            try:
                element = soup.select_one(selector)
                if element:
                    text = element.get_text(strip=True)
                    if text:
                        return text
            except Exception as e:
                logger.debug(f"选择器 {selector} 提取失败: {e}")
        
        return config.get('default', '')
    
    async def number_extractor(self, field_name: str, config: Dict[str, Any],
                             page: Page, url: str, soup: BeautifulSoup) -> int:
        """数字提取器"""
        text = await self.text_extractor(field_name, config, page, url, soup)
        
        # 提取数字
        numbers = re.findall(r'\d+', text.replace(',', ''))
        if numbers:
            return int(numbers[0])
        
        return config.get('default', 0)
    
    async def float_extractor(self, field_name: str, config: Dict[str, Any],
                            page: Page, url: str, soup: BeautifulSoup) -> float:
        """浮点数提取器"""
        text = await self.text_extractor(field_name, config, page, url, soup)
        
        # 提取浮点数
        numbers = re.findall(r'\d+\.?\d*', text.replace(',', ''))
        if numbers:
            return float(numbers[0])
        
        return config.get('default', 0.0)
    
    async def currency_extractor(self, field_name: str, config: Dict[str, Any],
                               page: Page, url: str, soup: BeautifulSoup) -> float:
        """货币提取器"""
        text = await self.text_extractor(field_name, config, page, url, soup)
        
        # 移除货币符号，提取数字
        cleaned_text = re.sub(r'[￥$€£¥]', '', text)
        numbers = re.findall(r'\d+\.?\d*', cleaned_text.replace(',', ''))
        if numbers:
            return float(numbers[0])
        
        return config.get('default', 0.0)
    
    async def list_extractor(self, field_name: str, config: Dict[str, Any],
                           page: Page, url: str, soup: BeautifulSoup) -> List[str]:
        """列表提取器"""
        selectors = config.get('selectors', [])
        items = []
        
        for selector in selectors:
            try:
                elements = soup.select(selector)
                for element in elements:
                    text = element.get_text(strip=True)
                    if text and text not in items:
                        items.append(text)
            except Exception as e:
                logger.debug(f"选择器 {selector} 提取失败: {e}")
        
        return items if items else config.get('default', [])
    
    async def date_extractor(self, field_name: str, config: Dict[str, Any],
                           page: Page, url: str, soup: BeautifulSoup) -> str:
        """日期提取器"""
        text = await self.text_extractor(field_name, config, page, url, soup)
        
        # 日期格式标准化
        date_patterns = [
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}/\d{2}/\d{2}',
            r'\d{4}年\d{1,2}月\d{1,2}日'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group()
        
        return config.get('default', '')
    
    async def url_extractor(self, field_name: str, config: Dict[str, Any],
                          page: Page, url: str, soup: BeautifulSoup) -> str:
        """URL提取器"""
        return url
    
    async def content_extractor(self, field_name: str, config: Dict[str, Any],
                              page: Page, url: str, soup: BeautifulSoup) -> str:
        """内容提取器（需要外部传入）"""
        # 这个方法需要外部传入内容，这里返回默认值
        return config.get('default', '')
    
    async def static_extractor(self, field_name: str, config: Dict[str, Any],
                             page: Page, url: str, soup: BeautifulSoup) -> str:
        """静态值提取器"""
        return config.get('default', '')
    
    async def timestamp_extractor(self, field_name: str, config: Dict[str, Any],
                                page: Page, url: str, soup: BeautifulSoup) -> str:
        """时间戳提取器"""
        return time.strftime('%Y-%m-%d %H:%M:%S')


# 全局字段提取器实例
field_extractor = FieldExtractor()


def get_field_extractor() -> FieldExtractor:
    """获取全局字段提取器实例"""
    return field_extractor


def configure_fields(field_config: Dict[str, Dict[str, Any]]):
    """配置字段"""
    extractor = get_field_extractor()
    
    # 清空现有配置
    extractor.field_configs.clear()
    
    # 添加新配置
    for field_name, config in field_config.items():
        extractor.add_field(field_name, config)
    
    logger.info(f"已配置 {len(field_config)} 个字段")


def add_extended_fields(field_names: List[str]):
    """添加扩展字段"""
    extractor = get_field_extractor()
    
    for field_name in field_names:
        if field_name in extractor.extended_fields:
            extractor.add_field(field_name, extractor.extended_fields[field_name])
        else:
            logger.warning(f"未知的扩展字段: {field_name}")
