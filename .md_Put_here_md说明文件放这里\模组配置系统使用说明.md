# 模组配置系统使用说明

## 概述

模组配置系统是一个智能的爬虫配置管理系统，能够根据URL自动选择合适的爬虫配置，解决不同网站需要不同选择器配置的问题。

## 主要功能

### 1. 自动配置选择
- 根据URL模式自动匹配合适的模组配置
- 支持域名匹配和URL正则表达式匹配
- 优先级排序，确保最合适的配置被选中

### 2. 失败URL重试
- 自动处理失败的CSV文件
- 使用模组配置重新处理失败的URL
- 支持批量重试和进度跟踪

### 3. 配置管理
- 可视化的配置管理界面
- 支持添加、修改、删除模组配置
- 配置文件自动保存和加载

## 文件结构

```
crawler 2/
├── module_configs.json          # 模组配置文件
├── module_manager.py           # 模组管理器核心
├── failed_url_processor.py     # 失败URL处理器
├── module_config_manager.py    # 配置管理界面
├── crawler.py                  # 主爬虫（已集成模组系统）
└── 模组配置系统使用说明.md      # 本文档
```

## 快速开始

### 1. 查看现有配置

```python
from module_manager import module_manager

# 列出所有模组
modules = module_manager.list_modules()
print("可用模组:", modules)

# 测试URL匹配
url = "https://mp.weixin.qq.com/s/2EGonPvQhmqoEKszLUxZCg"
module_name = module_manager.match_url(url)
print(f"URL {url} 匹配到模组: {module_name}")
```

### 2. 使用配置管理界面

```bash
python module_config_manager.py
```

这将启动交互式配置管理界面，提供以下功能：
- 查看所有模组
- 查看模组详情
- 添加新模组
- 测试URL匹配
- 处理失败URL文件

### 3. 处理失败URL

```python
from failed_url_processor import process_failed_csv

# 处理失败的CSV文件
result = process_failed_csv(
    failed_csv_path="articles/上海人大_代表风采_failed.csv",
    save_dir="articles",
    export_filename="重试结果",
    file_format="CSV",
    classid="3802",
    retry=3,
    interval=1.0,
    max_workers=3
)

print(f"处理结果: {result}")
```

## 模组配置格式

### 配置文件结构 (module_configs.json)

```json
{
    "微信公众号": {
        "name": "微信公众号",
        "description": "处理微信公众号文章的模组配置",
        "domain_patterns": [
            "mp.weixin.qq.com"
        ],
        "url_patterns": [
            ".*mp\\.weixin\\.qq\\.com/s/.*"
        ],
        "config": {
            "title_selectors": [
                "#activity-name",
                ".rich_media_title"
            ],
            "content_selectors": [
                "#js_content",
                ".rich_media_content"
            ],
            "date_selectors": [
                "#publish_time",
                ".rich_media_meta_text"
            ],
            "source_selectors": [
                ".rich_media_meta_nickname",
                "#js_name"
            ],
            "mode": "safe",
            "collect_links": true,
            "retry": 3,
            "interval": 1.0
        }
    }
}
```

### 配置字段说明

#### 基本信息
- `name`: 模组名称
- `description`: 模组描述

#### 匹配规则
- `domain_patterns`: 域名匹配模式，支持通配符 `*`
- `url_patterns`: URL正则表达式匹配模式

#### 选择器配置
- `title_selectors`: 标题选择器列表
- `content_selectors`: 内容选择器列表
- `date_selectors`: 日期选择器列表
- `source_selectors`: 来源选择器列表
- `*_selector_type`: 选择器类型 (CSS/XPath)

#### 爬取设置
- `mode`: 爬取模式 (balance/safe/fast)
- `collect_links`: 是否收集链接
- `retry`: 重试次数
- `interval`: 重试间隔
- `headless`: 是否无头模式
- `page_load_strategy`: 页面加载策略

## 使用场景

### 场景1: 微信公众号文章爬取失败

**问题**: 微信公众号文章使用原配置无法正确提取内容

**解决方案**:
1. 系统自动识别微信公众号URL
2. 使用专门的微信公众号模组配置
3. 应用合适的选择器和爬取策略

### 场景2: 批量重试失败URL

**问题**: 有大量失败的URL需要重新处理

**解决方案**:
```python
# 使用失败URL处理器
from failed_url_processor import process_failed_csv

result = process_failed_csv(
    failed_csv_path="articles/上海人大_代表风采_failed.csv",
    save_dir="articles",
    export_filename="重试结果",
    max_workers=5
)
```

### 场景3: 添加新网站支持

**问题**: 需要支持新的网站类型

**解决方案**:
1. 使用配置管理界面添加新模组
2. 配置域名模式和URL模式
3. 设置合适的选择器
4. 测试URL匹配效果

## 最佳实践

### 1. 模组优先级设置
- 将特定网站的模组放在前面
- 通用模组放在后面作为回退
- 定期检查匹配优先级

### 2. 选择器配置
- 提供多个备选选择器
- 从最特定到最通用排序
- 定期验证选择器有效性

### 3. 失败处理
- 定期处理失败URL文件
- 分析失败原因，优化配置
- 设置合理的重试次数和间隔

### 4. 性能优化
- 根据网站特性调整爬取模式
- 设置合适的并发数
- 监控爬取成功率

## 故障排除

### 常见问题

**Q: URL没有匹配到正确的模组**
A: 检查域名模式和URL模式是否正确，确保匹配优先级设置合理

**Q: 模组配置不生效**
A: 确认配置文件格式正确，重新加载配置或重启程序

**Q: 失败URL重试仍然失败**
A: 检查网站是否有反爬措施，调整爬取策略或增加延迟

**Q: 配置管理界面无法启动**
A: 检查依赖模块是否正确安装，确认文件权限

### 调试技巧

1. **启用详细日志**:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **测试URL匹配**:
```python
from module_manager import match_module_for_url
module_name = match_module_for_url("your_url_here")
print(f"匹配结果: {module_name}")
```

3. **验证配置**:
```python
from module_manager import get_config_for_url
config = get_config_for_url("your_url_here")
print(f"配置详情: {config}")
```

## 扩展开发

### 添加新的匹配规则
可以在 `module_manager.py` 中扩展匹配逻辑，支持更复杂的匹配规则。

### 自定义处理器
可以继承 `FailedUrlProcessor` 类，实现自定义的失败URL处理逻辑。

### 集成到现有系统
模组系统设计为可插拔的，可以轻松集成到现有的爬虫系统中。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的模组配置管理
- 实现URL自动匹配
- 提供失败URL重试功能
- 包含配置管理界面

---

如有问题或建议，请联系开发团队。
