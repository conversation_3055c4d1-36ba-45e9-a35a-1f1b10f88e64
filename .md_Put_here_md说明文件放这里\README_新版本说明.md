# 新版 crawler.py 使用说明

## 概述

新版 `crawler.py` 已成功将原有的 `crawler.py` 和 `PaginationHandler.py` 合并，并用 **Playwright** 完全替代了 **Selenium**。所有功能已测试通过，程序冗余已减少。

## 主要改进

### ✅ 已完成的改进

1. **技术栈升级**
   - 🔄 用 Playwright 替代 Selenium
   - ⚡ 支持异步操作，性能更好
   - 🌐 更好的浏览器兼容性

2. **代码合并**
   - 📦 将 `PaginationHandler.py` 功能合并到 `crawler.py`
   - 🗂️ 统一的模块结构
   - 🔧 减少了代码冗余

3. **功能增强**
   - 🚀 异步爬取函数 `crawl_articles_async()`
   - 🔄 动态翻页处理器 `PaginationHandler`
   - 📊 更好的日志记录
   - 🛡️ 向后兼容性保持

4. **测试验证**
   - ✅ 所有基本功能测试通过
   - ✅ 实际爬取功能测试通过
   - ✅ 文件操作测试通过
   - ✅ 向后兼容性测试通过

## 文件结构

```
📁 项目目录/
├── 📄 crawler.py              # 新版本（已替换原版本）
├── 📄 crawler_backup.py       # 原版本备份
├── 📄 crawler_new.py          # 开发版本
├── 📄 PaginationHandler.py    # 原独立模块（功能已合并）
├── 📄 test_crawler_new.py     # 基本功能测试
├── 📄 test_real_crawl.py      # 实际爬取测试
├── 📄 final_test.py           # 最终功能测试
└── 📄 README_新版本说明.md    # 本说明文档
```

## 主要功能

### 1. 智能模式切换

新版本支持三种模式：

```python
from crawler import save_article

# 快速模式：仅使用 requests+bs4
result = save_article(
    link="https://example.com/article",
    save_dir="./articles",
    page_title="测试",
    content_selectors=[".content"],
    mode="fast"  # 快速但可能被反爬
)

# 安全模式：仅使用 Playwright 异步
result = save_article(
    link="https://example.com/article",
    save_dir="./articles",
    page_title="测试",
    content_selectors=[".content"],
    mode="safe"  # 安全但较慢
)

# 平衡模式：requests 优先，失败后 Playwright
result = save_article(
    link="https://example.com/article",
    save_dir="./articles",
    page_title="测试",
    content_selectors=[".content"],
    mode="balance"  # 默认模式，智能切换
)
```

### 2. 纯异步 Playwright 函数

```python
import asyncio
from crawler import save_article_async

async def async_save_example():
    result = await save_article_async(
        link="https://example.com/article",
        save_dir="./articles",
        page_title="测试",
        content_selectors=[".content"],
        title_selectors=["h1", ".title", "title"],
        date_selectors=[".date", ".time"],
        source_selectors=[".source", ".author"]
    )
    return result

# 运行异步函数
result = asyncio.run(async_save_example())
```

### 3. 异步爬取函数

```python
import asyncio
from crawler import crawl_articles_async

async def main():
    result = await crawl_articles_async(
        input_url="https://example.com",
        base_url="https://example.com",
        max_pages=5,
        list_container_selector=".main",
        article_item_selector=".article-item a",
        content_selectors=[".content", ".article-body"],
        headless=False,  # 是否无头模式
        browser_type="chromium",  # 浏览器类型
        export_filename="test_crawl",
        file_format="CSV"
    )
    print(f"爬取结果: {result}")

# 运行异步函数
asyncio.run(main())
```

### 2. 动态翻页处理

```python
import asyncio
from crawler import PaginationHandler, launch_browser
from playwright.async_api import async_playwright

async def dynamic_pagination_example():
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        handler = PaginationHandler(page)
        
        await page.goto("https://example.com")
        
        # 点击翻页
        pages_processed = await handler.click_pagination(
            next_button_selector="a.next:not(.disabled)",
            max_pages=10,
            wait_after_click=2000
        )
        
        # 获取收集的文章
        articles = handler.get_all_articles()
        print(f"处理了 {pages_processed} 页，收集了 {len(articles)} 篇文章")
        
        await context.close()
        await browser.close()

asyncio.run(dynamic_pagination_example())
```

### 3. 向后兼容

```python
# 原有的同步调用方式仍然支持，但会提示使用异步版本
from crawler import crawl_articles

result = crawl_articles(
    input_url="https://example.com",
    base_url="https://example.com",
    max_pages=5
)
# 返回: {"error": "请使用 crawl_articles_async 函数进行异步调用"}
```

## 安装要求

确保已安装以下依赖：

```bash
pip install playwright beautifulsoup4 requests openpyxl
playwright install chromium  # 安装浏览器
```

## 使用建议

1. **新项目**: 直接使用 `crawl_articles_async()` 异步函数
2. **现有项目**: 逐步迁移到异步版本，利用向后兼容性
3. **动态翻页**: 使用 `PaginationHandler` 类处理复杂的翻页逻辑
4. **调试**: 使用 `debug_pagination_elements()` 方法调试翻页元素

## 性能优势

- ⚡ **更快的页面加载**: Playwright 比 Selenium 更高效
- 🔄 **异步处理**: 支持并发操作
- 💾 **更少的内存占用**: 优化的代码结构
- 🛡️ **更稳定**: 更好的错误处理和重试机制

## 注意事项

1. **异步编程**: 主要函数现在是异步的，需要使用 `asyncio.run()` 或在异步环境中调用
2. **浏览器依赖**: 需要安装 Playwright 浏览器
3. **配置迁移**: 某些 Selenium 特定的配置需要调整为 Playwright 格式

## 故障排除

如果遇到问题，请：

1. 检查 Playwright 是否正确安装: `playwright install`
2. 运行测试脚本验证功能: `python final_test.py`
3. 查看日志输出获取详细错误信息
4. 如需回滚，使用备份文件: `copy crawler_backup.py crawler.py`

## 总结

✅ **任务完成**: 成功将 crawler 与 PaginationHandler 合并，用 Playwright 替代 Selenium
✅ **功能测试**: 所有功能测试通过
✅ **代码优化**: 减少了程序冗余，提高了性能
✅ **向后兼容**: 保持了与现有代码的兼容性

新版本已准备就绪，可以投入使用！

## 🆕 最新功能更新

### 异步 Playwright 安全模式

新增了完整的异步 Playwright 安全模式支持：

#### 1. 智能模式切换

- **fast 模式**: 仅使用 requests+bs4，速度快但可能被反爬
- **safe 模式**: 仅使用 Playwright 异步，安全但较慢
- **balance 模式**: requests 优先，失败后自动切换到 Playwright（默认）

#### 2. 纯异步函数

- `save_article_async()`: 纯异步 Playwright 版本
- 完全异步操作，性能更好
- 支持复杂的 JavaScript 渲染页面

#### 3. 多选择器支持

- 标题、日期、来源、内容都支持多选择器
- 按优先级依次尝试，提高提取成功率
- 向后兼容单选择器参数

#### 4. 智能事件循环处理

- 自动检测是否在事件循环中运行
- 智能选择同步或异步执行方式
- 避免 "asyncio.run() cannot be called from a running event loop" 错误

### 使用示例

```python
# 1. 智能模式切换
from crawler import save_article

# 快速模式
save_article(link="...", mode="fast", ...)

# 安全模式
save_article(link="...", mode="safe", ...)

# 平衡模式（默认）
save_article(link="...", mode="balance", ...)

# 2. 纯异步模式
import asyncio
from crawler import save_article_async

async def main():
    result = await save_article_async(
        link="https://example.com/article",
        save_dir="./articles",
        page_title="测试",
        content_selectors=[".content", ".article"],
        title_selectors=["h1", ".title"],
        date_selectors=[".date", ".time"],
        source_selectors=[".source", ".author"]
    )
    return result

result = asyncio.run(main())
```

### 性能对比

| 模式 | 速度 | 安全性 | 适用场景 |
|------|------|--------|----------|
| fast | ⭐⭐⭐⭐⭐ | ⭐⭐ | 简单页面，无反爬 |
| safe | ⭐⭐ | ⭐⭐⭐⭐⭐ | 复杂页面，强反爬 |
| balance | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 通用场景（推荐） |

### 测试验证

所有新功能已通过完整测试：
- ✅ 智能模式切换测试
- ✅ 异步 Playwright 安全模式测试
- ✅ 多选择器功能测试
- ✅ 事件循环兼容性测试
- ✅ 文件操作测试

运行 `python test_async_safe_mode.py` 可验证所有功能。
