#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试珠海政协网站样式标签误识别问题
"""

import requests
from bs4 import BeautifulSoup
import re
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from utils.text_cleaner import clean_html_tags, clean_html_before_text_extraction

def test_zhzx_style_issue():
    """测试珠海政协网站的样式标签问题"""
    
    url = "http://www.zhzx.gov.cn/zxyw/202306/t20230609_58927130.html"
    
    print(f"🔍 正在分析网页: {url}")
    print("=" * 80)
    
    try:
        # 获取网页内容
        response = requests.get(url, timeout=10)
        response.encoding = 'utf-8'
        html_content = response.text
        
        print("✅ 网页获取成功")
        print(f"📄 HTML长度: {len(html_content)} 字符")
        
        # 使用BeautifulSoup解析
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 1. 查找所有style标签
        print("\n🎨 === STYLE标签分析 ===")
        style_tags = soup.find_all('style')
        print(f"找到 {len(style_tags)} 个style标签")
        
        for i, style in enumerate(style_tags):
            style_content = style.get_text()
            print(f"\nStyle标签 {i+1}:")
            print(f"长度: {len(style_content)} 字符")
            print(f"内容预览: {style_content[:200]}...")
            
            # 检查是否包含TRS_Editor相关样式
            if 'TRS_Editor' in style_content:
                print("⚠️ 包含TRS_Editor样式定义")
        
        # 2. 查找可能的正文容器
        print("\n📝 === 正文容器分析 ===")
        
        # 常见的正文选择器
        content_selectors = [
            ".TRS_Editor",
            "div.view.TRS_UEDITOR.trs_paper_default.trs_web",
            ".article_cont",
            "div[class*='content']",
            "div[class*='article']",
            "div.zhengwen"
        ]
        
        for selector in content_selectors:
            elements = soup.select(selector)
            if elements:
                print(f"✅ 选择器 '{selector}' 找到 {len(elements)} 个元素")
                for j, elem in enumerate(elements):
                    text_content = elem.get_text(strip=True)
                    print(f"  元素 {j+1}: 长度 {len(text_content)} 字符")
                    if text_content:
                        print(f"  内容预览: {text_content[:100]}...")
            else:
                print(f"❌ 选择器 '{selector}' 未找到元素")
        
        # 3. 测试当前的clean_html_tags函数
        print("\n🧹 === 文本清理测试 ===")

        # 找到包含正文的元素（手动查找）
        # 根据网页结构，正文应该在某个特定的div中
        main_content = soup.find('div', class_='column_800')
        if main_content:
            print("✅ 找到主要内容区域")

            # 获取原始HTML
            raw_html = str(main_content)
            print(f"原始HTML长度: {len(raw_html)} 字符")

            # 测试新的HTML清理函数
            print("\n--- 测试新的HTML清理函数 ---")
            cleaned_html = clean_html_before_text_extraction(raw_html)
            print(f"HTML清理后长度: {len(cleaned_html)} 字符")

            # 从清理后的HTML提取文本
            cleaned_soup = BeautifulSoup(cleaned_html, 'html.parser')
            extracted_text = cleaned_soup.get_text(separator='\n', strip=True)
            print(f"提取的文本长度: {len(extracted_text)} 字符")

            # 检查是否还包含样式内容
            if 'TRS_Editor' in extracted_text:
                print("⚠️ 提取的文本仍包含TRS_Editor样式内容")

                # 找到样式内容的位置
                style_start = extracted_text.find('TRS_Editor')
                style_context = extracted_text[max(0, style_start-50):style_start+200]
                print(f"样式内容上下文: ...{style_context}...")
            else:
                print("✅ 样式内容已成功清理")

            print(f"\n提取的文本内容预览:")
            print(extracted_text[:500])
            print("...")

            # 对比旧方法
            print("\n--- 对比旧方法 ---")
            old_text = clean_html_tags(raw_html)
            print(f"旧方法清理后长度: {len(old_text)} 字符")
            if 'TRS_Editor' in old_text:
                print("⚠️ 旧方法仍包含TRS_Editor样式内容")
            else:
                print("✅ 旧方法也能清理样式内容")
        
        # 4. 分析问题原因
        print("\n🔍 === 问题分析 ===")
        
        # 查找所有包含TRS_Editor的文本节点
        all_text = soup.get_text()
        if 'TRS_Editor' in all_text:
            print("⚠️ 页面文本中包含TRS_Editor")
            
            # 使用正则查找具体位置
            pattern = r'\.TRS_Editor[^}]*}'
            matches = re.findall(pattern, all_text)
            if matches:
                print(f"找到 {len(matches)} 个TRS_Editor样式定义:")
                for match in matches[:3]:  # 只显示前3个
                    print(f"  {match}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_zhzx_style_issue()
