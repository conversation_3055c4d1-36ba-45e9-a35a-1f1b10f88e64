#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI AI配置助手模块
从 crawler_gui_2.py 中拆分出来的AI相关功能
"""

from PyQt5.QtCore import QThread, pyqtSignal


class AIAutoConfigHelper:
    """AI智能配置工具类，负责AI分析和详情页URL提取"""
    
    @staticmethod
    def _parse_ai_result(ai_result: str) -> dict:
        """通用解析AI返回的键值对结果"""
        result_dict = {}
        for line in ai_result.splitlines():
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                # 跳过空值结果
                if value.lower() not in ('none', 'null', ''):
                    result_dict[key] = value
        return result_dict

    @staticmethod
    def analyze_list_page(url: str) -> dict:
        """分析列表页面，提取选择器"""
        try:
            from AI_wed_find_agent import analyze_page_with_selenium, analyze_container_with_deepseek_list
            page_data = analyze_page_with_selenium(url)
            ai_result = analyze_container_with_deepseek_list(page_data)
            return AIAutoConfigHelper._parse_ai_result(ai_result)
        except Exception as e:
            print(f"AI分析列表页失败: {e}")
            # 返回基础选择器作为降级方案
            return {
                'list_container_selector': 'body',
                'article_item_selector': 'a[href*="article"]'
            }

    @staticmethod
    def extract_first_article_url(
        list_url: str,
        article_item_selector: str,
        list_container_selector: str = "body",
        list_container_type: str = "CSS",
        article_item_type: str = "CSS",
        url_mode: str = "absolute",
        base_url: str = None
    ) -> str:
        """从列表页提取第一个文章URL"""
        import crawler
        
        # 使用新版本的异步方法
        import asyncio
        from playwright.async_api import async_playwright
        
        async def extract_async():
            try:
                async with async_playwright() as p:
                    browser, context, page = await crawler.launch_browser(p, headless=True)
                    
                    try:
                        _, _, article_links, _ = await crawler.get_article_links_playwright(
                            page, list_url,
                            list_container_selector or "body",
                            article_item_selector,
                            list_container_type,
                            article_item_type
                        )
                        
                        for href in article_links[:3]:  # 最多尝试前3个链接
                            full_url = crawler.get_full_link(
                                href, 
                                list_url, 
                                base_url or list_url, 
                                url_mode
                            )
                            if full_url:
                                return full_url
                    finally:
                        await context.close()
                        await browser.close()
            except Exception as e:
                print(f"异步提取文章URL失败: {e}")
            return None
        
        try:
            return asyncio.run(extract_async())
        except Exception as e:
            print(f"提取文章URL失败: {e}")
            return None

    @staticmethod
    def analyze_detail_page(article_url: str) -> dict:
        """分析详情页面，提取内容选择器"""
        try:
            from AI_wed_find_agent import analyze_page_with_selenium, analyze_container_with_deepseek_words
            detail_data = analyze_page_with_selenium(article_url)
            detail_result = analyze_container_with_deepseek_words(detail_data)
            return AIAutoConfigHelper._parse_ai_result(detail_result)
        except Exception as e:
            print(f"AI分析详情页失败: {e}")
            return {}  # 返回空字典避免上层崩溃


class AIConfigThread(QThread):
    """AI配置线程，用于异步执行AI分析任务"""
    
    result_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(str)  # 进度信息信号

    def __init__(self, url: str):
        super().__init__()
        self.url = url

    def run(self):
        """执行AI分析任务"""
        try:
            self.progress_signal.emit("开始分析列表页...")
            
            # 1. 分析列表页 (带降级方案)
            result_dict = AIAutoConfigHelper.analyze_list_page(self.url)
            
            self.progress_signal.emit("列表页分析完成，开始提取详情页URL...")
            
            # 2. 提取详情页URL (增加选择器存在性检查)
            article_selector = result_dict.get('article_item_selector', '')
            if not article_selector:
                raise ValueError("未获取到文章项选择器")
                
            article_url = AIAutoConfigHelper.extract_first_article_url(
                self.url, 
                article_selector,
                result_dict.get('list_container_selector')  # 传递可选容器选择器
            )
            
            if article_url:
                self.progress_signal.emit(f"开始分析详情页: {article_url}")
                detail_dict = AIAutoConfigHelper.analyze_detail_page(article_url)
                
                # 智能合并结果：仅补充缺失的关键字段
                for key in ['title_selector', 'content_selectors', 'date_selector', 'source_selector']:
                    if detail_dict.get(key) and not result_dict.get(key):
                        result_dict[key] = detail_dict[key]
                result_dict['__article_url'] = article_url
                
                self.progress_signal.emit("详情页分析完成")
            else:
                self.progress_signal.emit("未能提取到详情页URL，仅使用列表页分析结果")
                
            self.result_signal.emit(result_dict)
            
        except Exception as e:
            self.error_signal.emit(f"AI配置失败: {str(e)}")


class AIConfigManager:
    """AI配置管理器，提供统一的AI配置接口"""
    
    def __init__(self):
        self.ai_thread = None
    
    def start_ai_analysis(self, url: str, result_callback, error_callback, progress_callback=None):
        """
        启动AI分析
        
        Args:
            url: 要分析的URL
            result_callback: 结果回调函数
            error_callback: 错误回调函数
            progress_callback: 进度回调函数（可选）
        """
        if self.ai_thread and self.ai_thread.isRunning():
            error_callback("AI分析正在进行中，请等待完成")
            return False
        
        self.ai_thread = AIConfigThread(url)
        self.ai_thread.result_signal.connect(result_callback)
        self.ai_thread.error_signal.connect(error_callback)
        
        if progress_callback:
            self.ai_thread.progress_signal.connect(progress_callback)
        
        self.ai_thread.start()
        return True
    
    def is_running(self):
        """检查AI分析是否正在运行"""
        return self.ai_thread and self.ai_thread.isRunning()
    
    def stop_analysis(self):
        """停止AI分析"""
        if self.ai_thread and self.ai_thread.isRunning():
            self.ai_thread.terminate()
            self.ai_thread.wait()


# 工具函数
def parse_ai_config_to_gui_mapping():
    """返回AI配置字段到GUI控件的映射关系"""
    return {
        'list_container_selector': 'list_container_edit',
        'article_item_selector': 'article_item_edit',
        'title_selector': 'title_selector_edit',
        'content_selectors': 'content_selector_edit',  # AI返回复数，映射到单数控件
        'content_selector': 'content_selector_edit',   # 兼容单数返回
        'date_selector': 'date_selector_edit',
        'source_selector': 'source_selector_edit'
    }


def fill_gui_from_ai_result(gui_instance, result_dict):
    """
    将AI分析结果填充到GUI控件中
    
    Args:
        gui_instance: GUI实例
        result_dict: AI分析结果字典
    """
    mapping = parse_ai_config_to_gui_mapping()
    
    for ai_key, gui_attr in mapping.items():
        if ai_key in result_dict and hasattr(gui_instance, gui_attr):
            widget = getattr(gui_instance, gui_attr)
            if hasattr(widget, 'setText'):
                widget.setText(result_dict[ai_key])


if __name__ == "__main__":
    # 测试代码
    print("GUI AI助手模块已加载")
    print("主要功能:")
    print("- AIAutoConfigHelper: AI智能配置工具")
    print("- AIConfigThread: AI配置线程")
    print("- AIConfigManager: AI配置管理器")
