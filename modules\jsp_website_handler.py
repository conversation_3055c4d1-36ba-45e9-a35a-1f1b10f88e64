#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSP动态网站处理模块
专门针对政协、人大等政府机构的JSP网站的健壮性策略

特点：
1. JSP动态页面，需要等待JavaScript执行
2. 可能使用框架(iframe)
3. 翻页通常通过表单提交或JavaScript
4. 内容可能通过AJAX动态加载
5. 需要特殊的反检测策略
"""

import asyncio
import logging
import re
import time
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, urlparse
from playwright.async_api import Page, Browser, BrowserContext

# 导入统一的翻页工具
try:
    from core.pagination_utils import PaginationUtils
    PAGINATION_UTILS_AVAILABLE = True
except ImportError:
    PAGINATION_UTILS_AVAILABLE = False
    print("⚠️ 翻页工具不可用")

logger = logging.getLogger(__name__)

class JSPWebsiteHandler:
    """JSP网站处理器"""
    
    def __init__(self):
        self.name = "JSP网站处理器"
        self.description = "专门处理政协、人大等政府机构的JSP动态网站"
        self.supported_patterns = [
            r'.*\.jsp.*',
            r'.*政协.*',
            r'.*人大.*',
            r'.*gov\.cn.*',
            r'.*strWebSiteId.*',
            r'.*strColId.*'
        ]
        
    def is_supported_url(self, url: str) -> bool:
        """检查URL是否支持此处理器"""
        for pattern in self.supported_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return True
        return False
    
    async def analyze_page_structure(self, page: Page, url: str) -> Dict:
        """分析页面结构，识别文章列表和翻页元素"""
        logger.info(f"开始分析JSP页面结构: {url}")
        
        # 等待页面完全加载
        await self._wait_for_page_load(page)
        
        # 分析页面结构
        structure = await page.evaluate("""
            () => {
                const analysis = {
                    hasFrames: window.frames.length > 0,
                    frameCount: window.frames.length,
                    hasJQuery: typeof jQuery !== 'undefined',
                    jqueryVersion: typeof jQuery !== 'undefined' ? jQuery.fn.jquery : null,
                    
                    // 查找可能的文章列表容器
                    articleContainers: [],
                    
                    // 查找翻页元素
                    paginationElements: [],
                    
                    // 查找表单
                    forms: [],
                    
                    // 页面基本信息
                    title: document.title,
                    url: window.location.href,
                    readyState: document.readyState
                };
                
                // 查找可能的文章列表容器
                const potentialContainers = [
                    'table', 'tbody', 'ul', 'ol', 'div.list', 'div.content', 
                    'div.main', 'div.article-list', 'div.news-list'
                ];
                
                potentialContainers.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach((element, index) => {
                        const links = element.querySelectorAll('a');
                        const text = element.textContent.trim();
                        
                        if (links.length >= 2 && text.length > 100) {
                            analysis.articleContainers.push({
                                selector: selector,
                                index: index,
                                linkCount: links.length,
                                textLength: text.length,
                                className: element.className,
                                id: element.id,
                                tagName: element.tagName,
                                sample: text.substring(0, 200)
                            });
                        }
                    });
                });
                
                // 查找翻页元素
                const paginationSelectors = [
                    'a[href*="page"]', 'a[href*="Page"]', 'input[name*="page"]',
                    'button:contains("下一页")', 'a:contains("下一页")', 'a:contains("下页")',
                    'button:contains("上一页")', 'a:contains("上一页")', 'a:contains("上页")',
                    '.page', '.pager', '.pagination'
                ];
                
                paginationSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach((element, index) => {
                            analysis.paginationElements.push({
                                selector: selector,
                                index: index,
                                tagName: element.tagName,
                                text: element.textContent.trim(),
                                href: element.href || null,
                                onclick: element.onclick ? element.onclick.toString() : null
                            });
                        });
                    } catch (e) {
                        // 忽略CSS选择器错误
                    }
                });
                
                // 分析表单
                const forms = document.querySelectorAll('form');
                forms.forEach((form, index) => {
                    const inputs = form.querySelectorAll('input');
                    const buttons = form.querySelectorAll('button, input[type="submit"], input[type="button"]');
                    
                    analysis.forms.push({
                        index: index,
                        action: form.action,
                        method: form.method,
                        inputCount: inputs.length,
                        buttonCount: buttons.length,
                        inputs: Array.from(inputs).map(input => ({
                            name: input.name,
                            type: input.type,
                            value: input.value
                        }))
                    });
                });
                
                return analysis;
            }
        """)
        
        logger.info(f"页面结构分析完成: 找到{len(structure['articleContainers'])}个潜在文章容器")
        return structure
    
    async def extract_article_links(self, page: Page, url: str, structure: Dict = None) -> List[Dict]:
        """提取文章链接"""
        if not structure:
            structure = await self.analyze_page_structure(page, url)
        
        logger.info("开始提取文章链接...")
        
        # 如果有框架，尝试在框架中查找
        if structure['hasFrames']:
            return await self._extract_from_frames(page)
        
        # 在主页面中提取链接
        article_links = await page.evaluate("""
            () => {
                const links = [];
                const allLinks = document.querySelectorAll('a');
                
                allLinks.forEach(link => {
                    const href = link.href;
                    const text = link.textContent.trim();
                    
                    // 过滤条件：
                    // 1. 文本长度合理（5-100字符）
                    // 2. 不是JavaScript链接
                    // 3. 不是锚点链接
                    // 4. 不是导航链接
                    if (text.length >= 5 && text.length <= 100 &&
                        !href.includes('javascript:') &&
                        !href.includes('#') &&
                        !text.includes('首页') &&
                        !text.includes('返回') &&
                        !text.includes('更多') &&
                        !text.includes('下一页') &&
                        !text.includes('上一页')) {
                        
                        // 检查是否可能是文章链接
                        const isArticleLink = (
                            href.includes('.jsp') ||
                            href.includes('detail') ||
                            href.includes('show') ||
                            href.includes('view') ||
                            href.includes('article') ||
                            href.includes('news') ||
                            /\\d{4}/.test(href) ||  // 包含年份
                            /\\d{6,}/.test(href)    // 包含长数字ID
                        );
                        
                        if (isArticleLink) {
                            links.push({
                                href: href,
                                text: text,
                                isJsp: href.includes('.jsp'),
                                hasParams: href.includes('?'),
                                parentTag: link.parentElement.tagName,
                                parentClass: link.parentElement.className
                            });
                        }
                    }
                });
                
                return links;
            }
        """)
        
        logger.info(f"提取到 {len(article_links)} 个文章链接")
        return article_links
    
    async def handle_pagination(self, page: Page, current_page: int = 1, max_pages: int = 5) -> List[Dict]:
        """处理翻页逻辑"""
        logger.info(f"开始处理翻页，当前页: {current_page}, 最大页数: {max_pages}")
        
        all_articles = []
        
        for page_num in range(current_page, max_pages + 1):
            logger.info(f"处理第 {page_num} 页...")
            
            # 提取当前页的文章链接
            articles = await self.extract_article_links(page, page.url)
            all_articles.extend(articles)
            
            if page_num < max_pages:
                # 尝试翻到下一页
                success = await self._go_to_next_page(page, page_num)
                if not success:
                    logger.warning(f"无法翻到第 {page_num + 1} 页，停止翻页")
                    break
                
                # 等待页面加载
                await self._wait_for_page_load(page)
        
        logger.info(f"翻页完成，总共收集到 {len(all_articles)} 个文章链接")
        return all_articles
    
    async def extract_article_content(self, page: Page, article_url: str) -> Dict:
        """提取文章内容"""
        logger.info(f"开始提取文章内容: {article_url}")
        
        try:
            await page.goto(article_url, wait_until='networkidle', timeout=30000)
            await self._wait_for_page_load(page)
            
            # 多种内容选择器策略
            content_selectors = [
                '.article_cont', '.content', '.article', '.main-content',
                'div[class*="content"]', 'div[class*="article"]', 
                '.TRS_Editor', '.view', '.zhengwen', '.text',
                'div.view.TRS_UEDITOR.trs_paper_default.trs_web'
            ]
            
            # 多种标题选择器策略
            title_selectors = [
                'h1', 'h2', '.title', '.article-title', '.news-title',
                'div[class*="title"]', '.main-title', '.content-title'
            ]
            
            # 多种日期选择器策略
            date_selectors = [
                '.date', '.time', '.publish-time', '.create-time',
                'span[class*="date"]', 'span[class*="time"]',
                'div[class*="date"]', 'div[class*="time"]'
            ]
            
            article_data = await page.evaluate("""
                (selectors) => {
                    const { contentSelectors, titleSelectors, dateSelectors } = selectors;
                    
                    const result = {
                        title: '',
                        content: '',
                        date: '',
                        url: window.location.href
                    };
                    
                    // 提取标题
                    for (const selector of titleSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            result.title = element.textContent.trim();
                            break;
                        }
                    }
                    
                    // 提取内容
                    for (const selector of contentSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim().length > 100) {
                            result.content = element.textContent.trim();
                            break;
                        }
                    }
                    
                    // 提取日期
                    for (const selector of dateSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            const dateText = element.textContent.trim();
                            // 简单的日期格式检查
                            if (/\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}/.test(dateText) || 
                                /\\d{4}年\\d{1,2}月\\d{1,2}日/.test(dateText)) {
                                result.date = dateText;
                                break;
                            }
                        }
                    }
                    
                    return result;
                }
            """, {
                'contentSelectors': content_selectors,
                'titleSelectors': title_selectors,
                'dateSelectors': date_selectors
            })
            
            logger.info(f"文章内容提取完成: {article_data['title'][:50]}...")
            return article_data
            
        except Exception as e:
            logger.error(f"提取文章内容失败: {e}")
            return {
                'title': '',
                'content': '',
                'date': '',
                'url': article_url,
                'error': str(e)
            }
    
    async def _wait_for_page_load(self, page: Page, timeout: int = 10000):
        """等待页面完全加载"""
        try:
            # 等待网络空闲
            await page.wait_for_load_state('networkidle', timeout=timeout)
            
            # 额外等待JavaScript执行
            await asyncio.sleep(2)
            
            # 如果有jQuery，等待jQuery ready
            await page.evaluate("""
                () => {
                    return new Promise((resolve) => {
                        if (typeof jQuery !== 'undefined') {
                            jQuery(document).ready(() => resolve());
                        } else {
                            resolve();
                        }
                    });
                }
            """)
            
        except Exception as e:
            logger.warning(f"等待页面加载超时: {e}")
    
    async def _extract_from_frames(self, page: Page) -> List[Dict]:
        """从框架中提取链接"""
        logger.info("检测到框架，尝试从框架中提取链接...")
        
        try:
            frames = page.frames
            for frame in frames:
                if frame != page.main_frame:
                    # 在框架中查找链接
                    frame_links = await frame.evaluate("""
                        () => {
                            const links = [];
                            const allLinks = document.querySelectorAll('a');
                            
                            allLinks.forEach(link => {
                                const href = link.href;
                                const text = link.textContent.trim();
                                
                                if (text.length >= 5 && text.length <= 100 &&
                                    !href.includes('javascript:') &&
                                    !href.includes('#')) {
                                    
                                    links.push({
                                        href: href,
                                        text: text,
                                        isJsp: href.includes('.jsp'),
                                        hasParams: href.includes('?'),
                                        fromFrame: true
                                    });
                                }
                            });
                            
                            return links;
                        }
                    """)
                    
                    if frame_links:
                        logger.info(f"从框架中找到 {len(frame_links)} 个链接")
                        return frame_links
            
        except Exception as e:
            logger.error(f"从框架提取链接失败: {e}")
        
        return []

    async def _analyze_pagination_structure(self, page: Page, current_page: int) -> dict:
        """分析页面的翻页结构"""
        try:
            pagination_info = await page.evaluate(f"""
                () => {{
                    const info = {{
                        nextPageLinks: [],
                        pageNumbers: [],
                        jsFunction: null,
                        currentPage: {current_page}
                    }};

                    // 查找下一页链接
                    const nextLinks = document.querySelectorAll('a');
                    nextLinks.forEach(link => {{
                        const text = link.textContent.trim();
                        const href = link.getAttribute('href') || '';
                        const onclick = link.getAttribute('onclick') || '';

                        if (text.includes('下一页') || text.includes('下页') || text === '>') {{
                            info.nextPageLinks.push({{
                                text: text,
                                href: href,
                                onclick: onclick,
                                disabled: link.classList.contains('disabled') || link.classList.contains('gray')
                            }});
                        }}

                        // 查找页码链接
                        if (/^\\d+$/.test(text) && parseInt(text) === {current_page + 1}) {{
                            info.pageNumbers.push({{
                                pageNum: parseInt(text),
                                href: href,
                                onclick: onclick
                            }});
                        }}
                    }});

                    // 检查JavaScript函数
                    if (typeof goto === 'function') info.jsFunction = 'goto';
                    else if (typeof nextPage === 'function') info.jsFunction = 'nextPage';
                    else if (typeof goPage === 'function') info.jsFunction = 'goPage';

                    return info;
                }}
            """)

            logger.debug(f"翻页结构分析结果: {pagination_info}")
            return pagination_info

        except Exception as e:
            logger.error(f"翻页结构分析失败: {e}")
            return {}

    async def _go_to_next_page(self, page: Page, current_page: int) -> bool:
        """翻到下一页"""
        logger.info(f"尝试从第 {current_page} 页翻到下一页...")

        # 使用统一的翻页工具（如果可用）
        if PAGINATION_UTILS_AVAILABLE:
            logger.debug("使用统一翻页工具进行JSP翻页")
            return await PaginationUtils.smart_pagination(
                page=page,
                current_page=current_page,
                include_jsp_selectors=True,  # 包含JSP专用选择器
                wait_after_click=2000  # JSP网站需要更长等待时间
            )

        # 回退到原有逻辑（保持兼容性）
        logger.debug("回退到原有JSP翻页逻辑")

        # 首先分析页面的翻页结构
        pagination_info = await self._analyze_pagination_structure(page, current_page)

        # 策略1: 使用分析结果中的下一页链接（优先策略）
        if pagination_info.get('nextPageLinks'):
            for link_info in pagination_info['nextPageLinks']:
                if link_info.get('disabled'):
                    logger.debug(f"跳过禁用的下一页链接: {link_info['text']}")
                    continue

                try:
                    # 优先点击非禁用的下一页链接
                    if link_info['text'] in ['下一页', '下页', '>']:
                        selector = f'a:has-text("{link_info["text"]}")'
                        element = await page.query_selector(selector)
                        if element:
                            await element.click()
                            logger.info(f"通过分析结果翻页成功: {link_info['text']}")
                            return True
                except Exception as e:
                    logger.debug(f"分析结果翻页失败 {link_info['text']}: {e}")

        # 策略2: 传统的"下一页"链接查找（备用策略）
        next_page_selectors = [
            'a:has-text("下一页")', 'a:has-text("下页")', 'a:has-text(">")',
            'a[title*="下一页"]', 'a[title*="下页"]',
            'a[href*="javascript:goto"]',  # 专门针对JSP网站的goto函数
            'a[onclick*="goto"]'
        ]

        for selector in next_page_selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    # 检查链接是否可用（不是灰色或禁用状态）
                    href = await element.get_attribute('href')
                    onclick = await element.get_attribute('onclick')
                    class_name = await element.get_attribute('class')

                    # 跳过禁用的链接
                    if class_name and ('disabled' in class_name.lower() or 'gray' in class_name.lower()):
                        logger.debug(f"跳过禁用的链接: {selector}")
                        continue

                    await element.click()
                    logger.info(f"通过链接翻页成功: {selector} (href: {href}, onclick: {onclick})")
                    return True
            except Exception as e:
                logger.debug(f"翻页链接点击失败 {selector}: {e}")

        # 策略3: 使用分析结果中的页码链接
        if pagination_info.get('pageNumbers'):
            for page_info in pagination_info['pageNumbers']:
                try:
                    if page_info['pageNum'] == current_page + 1:
                        # 找到下一页的页码链接
                        selector = f'a:has-text("{page_info["pageNum"]}")'
                        element = await page.query_selector(selector)
                        if element:
                            await element.click()
                            logger.info(f"通过页码链接翻页成功: 第{page_info['pageNum']}页")
                            return True
                except Exception as e:
                    logger.debug(f"页码链接翻页失败: {e}")

        # 策略4: 查找页码输入框
        try:
            page_input = await page.query_selector('input[name*="page"], input[name*="Page"]')
            if page_input:
                await page_input.fill(str(current_page + 1))
                
                # 查找提交按钮
                submit_button = await page.query_selector('input[type="submit"], button[type="submit"]')
                if submit_button:
                    await submit_button.click()
                    logger.info("通过表单提交翻页成功")
                    return True
        except Exception as e:
            logger.debug(f"表单翻页失败: {e}")

        # 策略5: JavaScript翻页（使用分析结果）
        try:
            js_function = pagination_info.get('jsFunction')
            if js_function:
                logger.debug(f"使用分析结果中的JavaScript函数: {js_function}")

                if js_function == 'goto':
                    # goto函数用于跳转到指定页码
                    next_page_num = current_page + 1
                    await page.evaluate(f"goto({next_page_num})")
                    logger.info(f"通过JavaScript翻页成功: goto({next_page_num})")
                    return True
                elif js_function == 'nextPage':
                    # nextPage函数可能有两种用法
                    try:
                        await page.evaluate("nextPage()")
                        logger.info("通过JavaScript翻页成功: nextPage()")
                        return True
                    except:
                        await page.evaluate(f"nextPage({current_page + 1})")
                        logger.info(f"通过JavaScript翻页成功: nextPage({current_page + 1})")
                        return True
                else:
                    # 其他函数尝试传入下一页页码
                    next_page_num = current_page + 1
                    await page.evaluate(f"{js_function}({next_page_num})")
                    logger.info(f"通过JavaScript翻页成功: {js_function}({next_page_num})")
                    return True

            # 如果分析结果中没有函数，回退到传统检测
            available_functions = await page.evaluate("""
                () => {
                    const functions = [];
                    if (typeof goto === 'function') functions.push('goto');
                    if (typeof nextPage === 'function') functions.push('nextPage');
                    if (typeof goPage === 'function') functions.push('goPage');
                    if (typeof turnPage === 'function') functions.push('turnPage');
                    if (typeof page === 'function') functions.push('page');
                    return functions;
                }
            """)

            # 根据可用函数智能选择翻页策略
            for func_name in available_functions:
                try:
                    if func_name == 'goto':
                        # goto函数通常用于跳转到指定页码
                        next_page_num = current_page + 1
                        await page.evaluate(f"goto({next_page_num})")
                        logger.info(f"通过JavaScript翻页成功: goto({next_page_num})")
                        return True
                    elif func_name == 'nextPage':
                        # nextPage函数可能有两种用法：nextPage() 或 nextPage(pageNum)
                        # 先尝试无参数调用
                        try:
                            await page.evaluate("nextPage()")
                            logger.info("通过JavaScript翻页成功: nextPage()")
                            return True
                        except:
                            # 如果无参数失败，尝试传入下一页页码
                            await page.evaluate(f"nextPage({current_page + 1})")
                            logger.info(f"通过JavaScript翻页成功: nextPage({current_page + 1})")
                            return True
                    else:
                        # 其他函数尝试传入下一页页码
                        next_page_num = current_page + 1
                        await page.evaluate(f"{func_name}({next_page_num})")
                        logger.info(f"通过JavaScript翻页成功: {func_name}({next_page_num})")
                        return True
                except Exception as func_error:
                    logger.debug(f"JavaScript函数 {func_name} 调用失败: {func_error}")
                    continue

        except Exception as e:
            logger.debug(f"JavaScript翻页失败: {e}")
        
        logger.warning("所有翻页策略都失败了")
        return False

# 全局实例
jsp_handler = JSPWebsiteHandler()

# 测试函数
async def test_jsp_handler():
    """测试JSP处理器"""
    from playwright.async_api import async_playwright

    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"

    print(f"🧪 测试JSP处理器...")
    print(f"📋 测试URL: {test_url}")

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = await context.new_page()

        try:
            # 检查URL支持
            is_supported = jsp_handler.is_supported_url(test_url)
            print(f"🎯 URL支持检查: {'✅' if is_supported else '❌'}")

            if not is_supported:
                print("❌ URL不被JSP处理器支持")
                return

            # 访问页面
            await page.goto(test_url, wait_until='networkidle', timeout=30000)

            # 分析页面结构
            structure = await jsp_handler.analyze_page_structure(page, test_url)
            print(f"🏗️ 页面结构分析:")
            print(f"   - 框架数量: {structure['frameCount']}")
            print(f"   - jQuery版本: {structure['jqueryVersion'] or '未检测到'}")
            print(f"   - 文章容器: {len(structure['articleContainers'])}")
            print(f"   - 翻页元素: {len(structure['paginationElements'])}")
            print(f"   - 表单数量: {len(structure['forms'])}")

            # 提取文章链接
            article_links = await jsp_handler.extract_article_links(page, test_url, structure)
            print(f"🔗 文章链接提取:")
            print(f"   - 找到链接: {len(article_links)}")

            if article_links:
                print("   链接样本:")
                for i, link in enumerate(article_links[:5]):
                    print(f"     {i+1}. {link['text']} -> {link['href']}")

            # 如果找到文章链接，测试内容提取
            if article_links:
                print(f"\n📝 测试文章内容提取...")
                test_article = article_links[0]
                content = await jsp_handler.extract_article_content(page, test_article['href'])
                print(f"   - 标题: {content['title'][:50]}...")
                print(f"   - 内容长度: {len(content['content'])}")
                print(f"   - 日期: {content['date']}")

            print(f"\n⏸️ 按 Enter 键继续...")
            input()

        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()

        finally:
            await context.close()
            await browser.close()

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_jsp_handler())
