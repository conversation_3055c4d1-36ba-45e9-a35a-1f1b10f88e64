#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试4级分类功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.manager import <PERSON>fi<PERSON><PERSON><PERSON><PERSON>


def test_4_level_categories():
    """测试4级分类功能"""
    print("🧪 开始测试4级分类功能...")
    
    # 创建配置管理器
    config_manager = ConfigManager()
    
    # 测试获取父级分类
    print("\n📁 1级分类:")
    parent_categories = config_manager.get_parent_categories()
    for i, parent in enumerate(parent_categories, 1):
        print(f"  {i}. {parent}")
    
    # 测试获取次级分类
    print("\n📂 2级分类:")
    for parent in parent_categories:
        sub_categories = config_manager.get_sub_categories(parent)
        print(f"  {parent}:")
        for j, sub in enumerate(sub_categories, 1):
            print(f"    {j}. {sub}")
    
    # 测试获取第3级分类
    print("\n📄 3级分类:")
    for parent in parent_categories:
        sub_categories = config_manager.get_sub_categories(parent)
        for sub in sub_categories:
            third_categories = config_manager.get_child_categories(parent, sub)
            print(f"  {parent}/{sub}:")
            for k, third in enumerate(third_categories, 1):
                print(f"    {k}. {third}")
    
    # 测试获取第4级分类
    print("\n📋 4级分类:")
    for parent in parent_categories:
        sub_categories = config_manager.get_sub_categories(parent)
        for sub in sub_categories:
            third_categories = config_manager.get_child_categories(parent, sub)
            for third in third_categories:
                fourth_categories = config_manager.get_fourth_categories(parent, sub, third)
                if fourth_categories:
                    print(f"  {parent}/{sub}/{third}:")
                    for l, fourth in enumerate(fourth_categories, 1):
                        print(f"    {l}. {fourth}")
    
    # 测试获取配置组
    print("\n⚙️ 各4级分类下的配置组:")
    total_configs = 0
    for parent in parent_categories:
        sub_categories = config_manager.get_sub_categories(parent)
        for sub in sub_categories:
            third_categories = config_manager.get_child_categories(parent, sub)
            for third in third_categories:
                fourth_categories = config_manager.get_fourth_categories(parent, sub, third)
                for fourth in fourth_categories:
                    category_path = f"{parent}/{sub}/{third}/{fourth}"
                    configs = config_manager.get_configs_by_category_path(category_path)
                    if configs:
                        print(f"  {category_path}:")
                        for m, config in enumerate(configs, 1):
                            print(f"    {m}. {config}")
                            total_configs += 1
    
    print(f"\n📊 总计: {total_configs} 个配置组")
    
    # 测试文件名生成逻辑
    print("\n📝 测试文件名生成:")
    test_paths = [
        "政府机构/政协系统/上海政协/提案工作",
        "政府机构/人大系统/北京人大/监督纵横",
        "政府机构/政协系统/海南政协/委员工作"
    ]
    
    for path in test_paths:
        parts = path.split("/")
        if len(parts) == 4:
            parent, sub, third, fourth = parts
            filename = f"{third}_{fourth}"
            print(f"  {path} -> {filename}")
    
    # 测试添加新的4级分类
    print("\n➕ 测试添加新的4级分类...")
    success = config_manager.add_four_level_category("测试机构", "测试部门", "测试分支", "测试功能", "用于测试的4级分类")
    if success:
        print("  ✅ 成功添加: 测试机构/测试部门/测试分支/测试功能")
    else:
        print("  ❌ 添加失败")
    
    # 测试在新4级分类下创建配置组
    print("\n🆕 测试创建配置组...")
    test_config = {
        "input_url": "https://test.example.com",
        "title_selectors": [".title"],
        "content_selectors": [".content"],
        "mode": "balance"
    }
    success = config_manager.add_group("测试分支_测试功能", test_config, "测试机构/测试部门/测试分支/测试功能")
    if success:
        print("  ✅ 成功创建配置组: 测试分支_测试功能")
        print("  📄 文件名格式: 测试分支_测试功能 (符合3层_4层格式)")
    else:
        print("  ❌ 创建配置组失败")
    
    # 测试移动配置组
    print("\n🔄 测试移动配置组...")
    success = config_manager.move_config_to_category_path("测试分支_测试功能", "政府机构/人大系统/北京人大/监督纵横")
    if success:
        print("  ✅ 成功移动配置组到: 政府机构/人大系统/北京人大/监督纵横")
        
        # 验证移动结果
        old_configs = config_manager.get_configs_by_category_path("测试机构/测试部门/测试分支/测试功能")
        new_configs = config_manager.get_configs_by_category_path("政府机构/人大系统/北京人大/监督纵横")
        
        if "测试分支_测试功能" not in old_configs and "测试分支_测试功能" in new_configs:
            print("  ✅ 配置组移动验证成功")
        else:
            print("  ❌ 配置组移动验证失败")
    else:
        print("  ❌ 移动配置组失败")
    
    # 清理测试数据
    print("\n🧹 清理测试数据...")
    config_manager.delete_group("测试分支_测试功能")
    print("  ✅ 测试数据清理完成")
    
    # 显示最终状态
    print("\n📊 最终配置状态:")
    categories = config_manager.get_categories()
    total_configs = 0
    for parent_name, parent_data in categories.items():
        subcategories = parent_data.get("subcategories", {})
        parent_config_count = 0
        for sub_name, sub_data in subcategories.items():
            third_subcategories = sub_data.get("subcategories", {})
            for third_name, third_data in third_subcategories.items():
                fourth_subcategories = third_data.get("subcategories", {})
                for fourth_name, fourth_data in fourth_subcategories.items():
                    configs = fourth_data.get("configs", [])
                    parent_config_count += len(configs)
        print(f"  📁 {parent_name} ({parent_config_count} 个配置)")
        total_configs += parent_config_count
    
    print(f"\n总计: {len(categories)} 个1级分类, {total_configs} 个配置组")
    print("\n🎉 4级分类功能测试完成!")
    
    # 验证文件名格式
    print("\n📋 验证现有配置组的文件名格式:")
    groups = config_manager.config.get("groups", {})
    correct_format_count = 0
    total_count = 0
    
    for group_name, group_data in groups.items():
        total_count += 1
        category_path = group_data.get("category_path", "")
        if category_path:
            parts = category_path.split("/")
            if len(parts) == 4:
                parent, sub, third, fourth = parts
                expected_name = f"{third}_{fourth}"
                if group_name == expected_name or group_name.startswith(f"{third}_"):
                    correct_format_count += 1
                    print(f"  ✅ {group_name} -> {category_path}")
                else:
                    print(f"  ⚠️  {group_name} -> {category_path} (建议: {expected_name})")
            else:
                print(f"  ❓ {group_name} -> {category_path} (非4级路径)")
    
    print(f"\n📈 文件名格式统计: {correct_format_count}/{total_count} 个配置组符合'3层_4层'格式")


if __name__ == "__main__":
    test_4_level_categories()
