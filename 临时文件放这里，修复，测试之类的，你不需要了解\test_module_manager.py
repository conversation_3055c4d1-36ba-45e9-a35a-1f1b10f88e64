#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("测试模组管理器...")

try:
    from module_manager import module_manager
    print("✅ 模组管理器导入成功")
    
    # 测试加载模组
    modules = module_manager.list_modules()
    print(f"✅ 加载到 {len(modules)} 个模组: {modules}")
    
    # 测试获取模组信息
    for module_name in modules:
        info = module_manager.get_module_info(module_name)
        if info:
            print(f"✅ 模组 '{module_name}' 信息正常")
        else:
            print(f"❌ 模组 '{module_name}' 信息获取失败")
    
    # 测试URL匹配
    test_url = "https://mp.weixin.qq.com/s/test"
    matched = module_manager.match_url(test_url)
    print(f"✅ URL匹配测试: {test_url} -> {matched}")
    
    print("✅ 模组管理器测试完成")
    
except Exception as e:
    print(f"❌ 模组管理器测试失败: {e}")
    import traceback
    traceback.print_exc()
