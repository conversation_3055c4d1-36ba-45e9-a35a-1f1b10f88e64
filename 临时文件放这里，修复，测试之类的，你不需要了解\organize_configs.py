#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新组织配置文件
创建专门的配置文件夹并移动所有配置文件
"""

import os
import shutil
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_config_structure():
    """创建配置文件夹结构"""
    config_dirs = [
        'configs',                    # 主配置文件夹
        'configs/app',               # 应用配置
        'configs/modules',           # 模组配置
        'configs/ai',                # AI配置
        'configs/crawler',           # 爬虫配置
        'configs/testing',           # 测试配置
        'configs/backup'             # 配置备份
    ]
    
    for dir_path in config_dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            logger.info(f"创建配置目录: {dir_path}")

def move_config_files():
    """移动配置文件到对应目录"""
    
    # 配置文件映射
    config_mappings = {
        # 应用配置
        'configs/app': [
            'config.json',
            'myconfig.json'
        ],
        
        # 模组配置
        'configs/modules': [
            'module_configs.json'
        ],
        
        # AI配置
        'configs/ai': [
            'llm_config.json'
        ],
        
        # 爬虫配置
        'configs/crawler': [
            'crawler_config.json'
        ]
    }
    
    for target_dir, files in config_mappings.items():
        for file_name in files:
            if os.path.exists(file_name):
                target_path = os.path.join(target_dir, file_name)
                try:
                    shutil.move(file_name, target_path)
                    logger.info(f"移动配置文件: {file_name} -> {target_path}")
                except Exception as e:
                    logger.error(f"移动配置文件失败 {file_name}: {e}")

def create_config_manager():
    """创建配置管理器"""
    
    config_manager_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置管理器
管理所有配置文件的加载和保存
"""

import os
import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class UnifiedConfigManager:
    """统一配置管理器"""
    
    def __init__(self):
        self.config_base_dir = "configs"
        self.configs = {}
        self.load_all_configs()
    
    def get_config_path(self, config_type: str, config_name: str) -> str:
        """获取配置文件路径"""
        return os.path.join(self.config_base_dir, config_type, config_name)
    
    def load_config(self, config_type: str, config_name: str) -> Dict[str, Any]:
        """加载指定配置文件"""
        config_path = self.get_config_path(config_type, config_name)
        
        if not os.path.exists(config_path):
            logger.warning(f"配置文件不存在: {config_path}")
            return {}
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"加载配置文件: {config_path}")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败 {config_path}: {e}")
            return {}
    
    def save_config(self, config_type: str, config_name: str, config_data: Dict[str, Any]) -> bool:
        """保存配置文件"""
        config_path = self.get_config_path(config_type, config_name)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            logger.info(f"保存配置文件: {config_path}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败 {config_path}: {e}")
            return False
    
    def load_all_configs(self):
        """加载所有配置文件"""
        config_types = ['app', 'modules', 'ai', 'crawler', 'testing']
        
        for config_type in config_types:
            config_dir = os.path.join(self.config_base_dir, config_type)
            if os.path.exists(config_dir):
                for file_name in os.listdir(config_dir):
                    if file_name.endswith('.json'):
                        config_key = f"{config_type}.{file_name}"
                        self.configs[config_key] = self.load_config(config_type, file_name)
    
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用配置"""
        return self.configs.get('app.config.json', {})
    
    def get_module_configs(self) -> Dict[str, Any]:
        """获取模组配置"""
        return self.configs.get('modules.module_configs.json', {})
    
    def get_ai_config(self) -> Dict[str, Any]:
        """获取AI配置"""
        return self.configs.get('ai.llm_config.json', {})
    
    def get_crawler_config(self) -> Dict[str, Any]:
        """获取爬虫配置"""
        return self.configs.get('crawler.crawler_config.json', {})
    
    def backup_config(self, config_type: str, config_name: str) -> bool:
        """备份配置文件"""
        import datetime
        
        source_path = self.get_config_path(config_type, config_name)
        if not os.path.exists(source_path):
            return False
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{config_name}.{timestamp}.backup"
        backup_path = self.get_config_path('backup', backup_name)
        
        try:
            shutil.copy2(source_path, backup_path)
            logger.info(f"备份配置文件: {source_path} -> {backup_path}")
            return True
        except Exception as e:
            logger.error(f"备份配置文件失败: {e}")
            return False

# 全局配置管理器实例
unified_config_manager = UnifiedConfigManager()
'''
    
    with open('config/unified_manager.py', 'w', encoding='utf-8') as f:
        f.write(config_manager_content)
    logger.info("创建统一配置管理器: config/unified_manager.py")

def update_import_references():
    """更新配置文件的导入引用"""
    
    files_to_update = [
        'gui/main_window.py',
        'gui/config_manager.py',
        'modules/manager.py',
        'modules/config_manager.py',
        'ai/helper.py',
        'testing/selectors_test.py'
    ]
    
    # 路径映射
    path_mappings = {
        'config.json': 'configs/app/config.json',
        'myconfig.json': 'configs/app/myconfig.json',
        'module_configs.json': 'configs/modules/module_configs.json',
        'llm_config.json': 'configs/ai/llm_config.json',
        'crawler_config.json': 'configs/crawler/crawler_config.json'
    }
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 更新配置文件路径
                for old_path, new_path in path_mappings.items():
                    content = content.replace(f'"{old_path}"', f'"{new_path}"')
                    content = content.replace(f"'{old_path}'", f"'{new_path}'")
                
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    logger.info(f"更新配置路径引用: {file_path}")
                    
            except Exception as e:
                logger.error(f"更新配置路径引用失败 {file_path}: {e}")

def create_config_readme():
    """创建配置文件说明"""
    
    readme_content = '''# 配置文件说明

## 📁 配置文件夹结构

```
configs/
├── app/                     # 应用配置
│   ├── config.json         # 主应用配置
│   └── myconfig.json       # 用户配置
├── modules/                 # 模组配置
│   └── module_configs.json # 模组配置文件
├── ai/                      # AI配置
│   └── llm_config.json     # LLM配置
├── crawler/                 # 爬虫配置
│   └── crawler_config.json # 爬虫配置
├── testing/                 # 测试配置
│   └── test_configs.json   # 测试配置
└── backup/                  # 配置备份
    └── *.backup            # 自动备份文件
```

## 📋 配置文件说明

### 应用配置 (app/)
- **config.json**: 主应用配置，包含基础设置
- **myconfig.json**: 用户个人配置，包含用户偏好设置

### 模组配置 (modules/)
- **module_configs.json**: 所有模组的配置信息

### AI配置 (ai/)
- **llm_config.json**: LLM API配置，包含API密钥和设置

### 爬虫配置 (crawler/)
- **crawler_config.json**: 爬虫相关配置，包含请求设置等

### 测试配置 (testing/)
- **test_configs.json**: 测试相关配置

### 备份配置 (backup/)
- 自动备份的配置文件，按时间戳命名

## 🔧 使用方式

### 导入统一配置管理器
```python
from config.unified_manager import unified_config_manager

# 获取应用配置
app_config = unified_config_manager.get_app_config()

# 获取模组配置
module_configs = unified_config_manager.get_module_configs()

# 保存配置
unified_config_manager.save_config('app', 'config.json', new_config)

# 备份配置
unified_config_manager.backup_config('app', 'config.json')
```

## 📝 注意事项

1. 所有配置文件都使用UTF-8编码
2. 配置文件格式为JSON
3. 修改配置前会自动创建备份
4. 配置文件路径已统一管理，便于维护
'''
    
    with open('configs/README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    logger.info("创建配置说明文件: configs/README.md")

def main():
    """主函数"""
    logger.info("开始重新组织配置文件...")
    
    # 1. 创建配置文件夹结构
    create_config_structure()
    
    # 2. 移动配置文件
    move_config_files()
    
    # 3. 创建统一配置管理器
    create_config_manager()
    
    # 4. 更新导入引用
    update_import_references()
    
    # 5. 创建说明文件
    create_config_readme()
    
    logger.info("配置文件重新组织完成！")
    
    print("\\n📁 配置文件夹结构已创建:")
    print("├── configs/app/         # 应用配置")
    print("├── configs/modules/     # 模组配置") 
    print("├── configs/ai/          # AI配置")
    print("├── configs/crawler/     # 爬虫配置")
    print("├── configs/testing/     # 测试配置")
    print("└── configs/backup/      # 配置备份")
    
    print("\\n🔧 统一配置管理器已创建:")
    print("config/unified_manager.py")
    
    print("\\n📝 使用方式:")
    print("from config.unified_manager import unified_config_manager")

if __name__ == "__main__":
    main()
