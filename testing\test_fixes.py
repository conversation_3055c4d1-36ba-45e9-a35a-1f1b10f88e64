#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复的功能
1. 配置管理按钮
2. 模组配置选择器映射修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_config_manager_dialog():
    """测试配置管理对话框"""
    print("🧪 测试配置管理对话框")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import ConfigManagerDialog
        from config.manager import ConfigManager
        
        # 创建应用（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建一个模拟的父窗口对象
        class MockParent:
            def __init__(self):
                self.config_manager = type('obj', (object,), {'config_manager': config_manager})()
        
        parent = MockParent()
        
        # 创建配置管理对话框
        dialog = ConfigManagerDialog(parent)
        
        print("✅ 配置管理对话框创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置管理对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_selector_mapping():
    """测试选择器映射修复"""
    print("\n🧪 测试选择器映射修复")
    
    try:
        from gui.config_manager import GUIConfigManager
        
        # 创建GUI配置管理器
        gui_config_manager = GUIConfigManager()
        
        # 模拟GUI配置（列表格式）
        gui_config = {
            'input_url': 'https://test.example.com',
            'title_selectors': ['.title', 'h1', '.article-title'],
            'content_selectors': ['.content', '.article-content'],
            'date_selectors': ['.date', '.publish-time'],
            'source_selectors': ['.source', '.author'],
            'mode': 'balance',
            'max_pages': '5'
        }
        
        # 转换为爬虫配置
        crawler_config = gui_config_manager.prepare_crawler_config(gui_config)
        
        # 验证选择器格式
        print(f"标题选择器: {crawler_config['title_selectors']}")
        print(f"内容选择器: {crawler_config['content_selectors']}")
        print(f"日期选择器: {crawler_config['date_selectors']}")
        print(f"来源选择器: {crawler_config['source_selectors']}")
        
        # 验证是否为列表格式
        assert isinstance(crawler_config['title_selectors'], list), "标题选择器应该是列表"
        assert isinstance(crawler_config['content_selectors'], list), "内容选择器应该是列表"
        assert isinstance(crawler_config['date_selectors'], list), "日期选择器应该是列表"
        assert isinstance(crawler_config['source_selectors'], list), "来源选择器应该是列表"
        
        print("✅ 选择器映射测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 选择器映射测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_module_edit_dialog_mapping():
    """测试模组编辑对话框的选择器映射"""
    print("\n🧪 测试模组编辑对话框选择器映射")
    
    try:
        # 模拟GUI配置（列表格式）
        gui_config = {
            'title_selectors': ['.title', 'h1'],
            'content_selectors': ['.content', '.article'],
            'date_selectors': ['.date'],
            'source_selectors': ['.source']
        }
        
        # 测试列表到字符串的转换逻辑
        def convert_selectors(selectors):
            if isinstance(selectors, list):
                return ', '.join(selectors)
            return selectors
        
        # 转换选择器
        title_str = convert_selectors(gui_config['title_selectors'])
        content_str = convert_selectors(gui_config['content_selectors'])
        date_str = convert_selectors(gui_config['date_selectors'])
        source_str = convert_selectors(gui_config['source_selectors'])
        
        print(f"转换后的标题选择器: '{title_str}'")
        print(f"转换后的内容选择器: '{content_str}'")
        print(f"转换后的日期选择器: '{date_str}'")
        print(f"转换后的来源选择器: '{source_str}'")
        
        # 验证转换结果
        assert title_str == '.title, h1', f"标题选择器转换错误: {title_str}"
        assert content_str == '.content, .article', f"内容选择器转换错误: {content_str}"
        assert date_str == '.date', f"日期选择器转换错误: {date_str}"
        assert source_str == '.source', f"来源选择器转换错误: {source_str}"
        
        print("✅ 模组编辑对话框选择器映射测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模组编辑对话框选择器映射测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_update_manager_fix():
    """测试更新管理器修复"""
    print("\n🧪 测试更新管理器修复")
    
    try:
        from core.update_manager import UpdateManager
        from gui.config_manager import GUIConfigManager
        
        # 创建更新管理器
        update_manager = UpdateManager()
        
        # 创建GUI配置管理器
        gui_config_manager = GUIConfigManager()
        
        # 模拟配置
        config = {
            'input_url': 'https://test.example.com',
            'title_selectors': ['.title'],
            'content_selectors': ['.content'],
            'pagination_config': {'enabled': True}  # 这个参数应该被过滤掉
        }
        
        # 准备爬虫配置
        crawler_config = gui_config_manager.prepare_crawler_config(config)
        
        # 移除不支持的参数（模拟update_manager中的逻辑）
        unsupported_params = ['pagination_config']
        for param in unsupported_params:
            crawler_config.pop(param, None)
        
        # 验证pagination_config已被移除
        assert 'pagination_config' not in crawler_config, "pagination_config应该被移除"
        
        print("✅ 更新管理器修复测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 更新管理器修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复功能")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("配置管理对话框", test_config_manager_dialog()))
    test_results.append(("选择器映射", test_selector_mapping()))
    test_results.append(("模组编辑对话框映射", test_module_edit_dialog_mapping()))
    test_results.append(("更新管理器修复", test_update_manager_fix()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有修复功能测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
