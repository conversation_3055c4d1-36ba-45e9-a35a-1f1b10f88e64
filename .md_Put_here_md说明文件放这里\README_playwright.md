# Crawler Playwright 版本

这是基于原始 `crawler.py` 创建的 Playwright 版本，主要功能和接口保持一致，但使用 Playwright 替代 Selenium。

## 主要特性

- ✅ **保持原有接口**: 主要函数名称和参数与原版本相同
- ✅ **Playwright支持**: 使用现代化的Playwright库替代Selenium
- ✅ **多浏览器支持**: 支持Chromium、Firefox、WebKit
- ✅ **三种采集模式**: fast（requests+bs4）、safe（playwright）、balance（混合）
- ✅ **内容过滤**: 支持自定义过滤规则
- ✅ **链接收集**: 可选择是否收集图片和附件链接
- ✅ **分页支持**: 自动处理多页面爬取

## 安装依赖

```bash
# 安装Playwright
pip install playwright

# 安装浏览器（首次使用需要）
playwright install chromium
# 或者安装所有浏览器
playwright install
```

## 基本使用

```python
from crawler_playwright import crawl_articles

# 基本用法
result = crawl_articles(
    input_url="https://example.com/news",
    base_url="https://example.com",
    max_pages=5,
    browser="chromium",  # 可选: chromium, firefox, webkit
    mode="balance"       # 可选: fast, safe, balance
)

print(f"爬取结果: {result}")
```

## 主要函数

### 1. crawl_articles()
主要的爬取函数，参数与原版本相同：

```python
crawl_articles(
    input_url,                    # 起始URL
    base_url,                     # 基础URL
    max_pages=None,               # 最大页数
    list_container_selector=".main",  # 列表容器选择器
    article_item_selector="a",    # 文章项选择器
    content_selectors=[...],      # 内容选择器列表
    browser="chromium",           # 浏览器类型
    diver=None,                   # 浏览器配置
    mode="balance",               # 采集模式
    collect_links=True,           # 是否收集链接
    filters=None                  # 内容过滤规则
)
```

### 2. PlaywrightManager
浏览器管理类：

```python
from crawler_playwright import PlaywrightManager

manager = PlaywrightManager(
    browser_type="chromium",
    headless=True,
    window_size="1200,800"
)
page = manager.start()
# 使用page进行操作
manager.quit()
```

## 配置选项

### 浏览器配置 (diver参数)
```python
diver = {
    "headless": True,           # 无头模式
    "window_size": "1200,800",  # 窗口大小
    "page_load_strategy": "normal"  # 页面加载策略
}
```

### 采集模式 (mode参数)
- `"fast"`: 仅使用requests+BeautifulSoup，速度快但可能遇到JS渲染问题
- `"safe"`: 仅使用Playwright，兼容性好但速度较慢
- `"balance"`: 先尝试fast模式，失败后使用safe模式（推荐）

## 与原版本的差异

| 特性 | 原版本 (Selenium) | 新版本 (Playwright) |
|------|------------------|-------------------|
| 浏览器启动 | selenium_diver_change.get_driver() | PlaywrightManager() |
| 元素定位 | WebDriverWait + find_element | page.locator() + wait_for_selector |
| 页面操作 | driver.get() | page.goto() |
| 多标签页 | 手动管理window_handles | 自动管理新页面 |
| 性能 | 较慢 | 更快 |
| 稳定性 | 一般 | 更稳定 |

## 示例代码

```python
# 完整示例
from crawler_playwright import crawl_articles

config = {
    "input_url": "https://news.example.com",
    "base_url": "https://news.example.com",
    "max_pages": 3,
    "list_container_selector": ".news-list",
    "article_item_selector": ".news-item a",
    "content_selectors": [
        ".article-content",
        ".news-body",
        "main"
    ],
    "date_selector": ".publish-date",
    "source_selector": ".news-source",
    "title_selector": "h1.title",
    "browser": "chromium",
    "diver": {
        "headless": True,
        "window_size": "1920,1080"
    },
    "mode": "balance",
    "collect_links": True,
    "filters": ["广告", "推广"]  # 过滤包含这些词的内容
}

result = crawl_articles(**config)
print(f"成功爬取 {result['success']} 篇文章")
```

## 测试

运行内置测试：
```bash
python crawler_playwright.py
```

## 注意事项

1. **首次使用**: 需要先安装Playwright浏览器
2. **选择器**: CSS选择器和XPath都支持，但推荐使用CSS选择器
3. **性能**: balance模式在大多数情况下是最佳选择
4. **错误处理**: 内置重试机制和详细的错误信息
5. **文件保存**: 结果保存在 `articles/` 目录下的CSV文件中

## 故障排除

1. **导入错误**: 确保已安装playwright: `pip install playwright`
2. **浏览器错误**: 运行 `playwright install` 安装浏览器
3. **选择器错误**: 检查网页结构，调整选择器
4. **网络错误**: 检查网络连接和目标网站可访问性
