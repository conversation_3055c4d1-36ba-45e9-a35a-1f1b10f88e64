#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强型正文过滤功能的脚本
验证集成了非文本字段清洗的正文过滤效果
"""

import sys
import os

def test_enhanced_content_filter():
    """测试增强型正文过滤功能"""
    print("=" * 80)
    print("🧪 测试增强型正文过滤功能")
    print("=" * 80)
    
    try:
        from core.txt_clear import enhanced_content_filter, filter_content, clean_string_field
        print("✅ 增强型过滤函数导入成功")
        
        # 测试文本：包含各种需要清洗的内容
        test_content = """
        <div class="content">
            <h1>重要新闻标题</h1>
            <p>这是正文的第一段内容，包含有用信息。</p>
            
            <p>这是正文的第二段内容&nbsp;包含HTML实体。</p>
            
            <!-- 这是HTML注释 -->
            <script>alert('这是脚本');</script>
            
            <p>正文继续，包含特殊字符★☆和零宽字符\u200b。</p>
            
            <div class="navigation">首页 > 新闻中心 > 正文</div>
            
            <p>更多正文内容。</p>
            
            <div class="footer">
                版权所有 © 2025 测试网站
                联系我们 | 免责声明
            </div>
            
            <div class="social">
                分享到：微信 微博
                扫一扫二维码关注我们
            </div>
            
            <div class="meta">
                发布时间：2025-01-11 14:30:00
                编辑：张三
                责任编辑：李四
            </div>
            
            <div class="comments">
                评论 (25) | 点赞 (100) | 阅读 (1500)
            </div>
            
            <div class="related">
                相关阅读：
                推荐文章1
                推荐文章2
            </div>
        </div>
        """
        
        print("📝 原始测试内容:")
        print("-" * 60)
        print(test_content[:200] + "..." if len(test_content) > 200 else test_content)
        print("-" * 60)
        
        # 测试传统过滤
        traditional_result = filter_content(test_content)
        print(f"\n🔧 传统过滤结果 (长度: {len(traditional_result)}):")
        print("-" * 60)
        print(traditional_result[:300] + "..." if len(traditional_result) > 300 else traditional_result)
        print("-" * 60)
        
        # 测试非文本字段清洗
        string_clean_result = clean_string_field(traditional_result)
        print(f"\n🧹 非文本字段清洗结果 (长度: {len(string_clean_result)}):")
        print("-" * 60)
        print(string_clean_result[:300] + "..." if len(string_clean_result) > 300 else string_clean_result)
        print("-" * 60)
        
        # 测试增强型过滤
        enhanced_result = enhanced_content_filter(test_content)
        print(f"\n✨ 增强型过滤结果 (长度: {len(enhanced_result)}):")
        print("-" * 60)
        print(enhanced_result)
        print("-" * 60)
        
        # 验证清洗效果
        print(f"\n📊 清洗效果对比:")
        print(f"   原始内容长度: {len(test_content)}")
        print(f"   传统过滤后: {len(traditional_result)} (-{len(test_content) - len(traditional_result)})")
        print(f"   非文本清洗后: {len(string_clean_result)} (-{len(traditional_result) - len(string_clean_result)})")
        print(f"   增强型过滤后: {len(enhanced_result)} (-{len(test_content) - len(enhanced_result)})")
        
        # 检查是否包含不应该存在的内容
        unwanted_content = [
            'script', 'HTML', '版权所有', '分享到', '发布时间', '编辑：', '评论', '相关阅读'
        ]
        
        found_unwanted = []
        for unwanted in unwanted_content:
            if unwanted.lower() in enhanced_result.lower():
                found_unwanted.append(unwanted)
        
        if found_unwanted:
            print(f"⚠️ 仍包含不需要的内容: {found_unwanted}")
        else:
            print("✅ 成功清除所有不需要的内容")
        
        # 检查是否保留了有用内容
        useful_content = ['重要新闻标题', '正文的第一段', '正文的第二段', '更多正文内容']
        found_useful = []
        for useful in useful_content:
            if useful in enhanced_result:
                found_useful.append(useful)
        
        print(f"✅ 保留的有用内容: {len(found_useful)}/{len(useful_content)} 项")
        
        if len(found_useful) >= len(useful_content) * 0.8:  # 至少保留80%的有用内容
            print("✅ 增强型过滤测试通过")
            return True
        else:
            print("❌ 增强型过滤测试失败：丢失了太多有用内容")
            return False
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_specific_cleaning_functions():
    """测试特定清洗功能"""
    print("\n" + "=" * 80)
    print("🧪 测试特定清洗功能")
    print("=" * 80)
    
    try:
        from core.txt_clear import clean_content_specific
        print("✅ 特定清洗函数导入成功")
        
        # 测试各种特定内容的清洗
        test_cases = [
            {
                "name": "导航信息清洗",
                "input": "首页 > 新闻中心 > 正文\n这是正文内容。",
                "should_contain": "这是正文内容",
                "should_not_contain": "首页"
            },
            {
                "name": "版权信息清洗",
                "input": "正文内容。\n版权所有 © 2025 测试网站",
                "should_contain": "正文内容",
                "should_not_contain": "版权所有"
            },
            {
                "name": "社交分享清洗",
                "input": "正文内容。\n分享到：微信 微博",
                "should_contain": "正文内容",
                "should_not_contain": "分享到"
            },
            {
                "name": "编辑信息清洗",
                "input": "正文内容。\n编辑：张三\n责任编辑：李四",
                "should_contain": "正文内容",
                "should_not_contain": "编辑："
            },
            {
                "name": "时间戳清洗",
                "input": "正文内容。\n发布时间：2025-01-11 14:30:00",
                "should_contain": "正文内容",
                "should_not_contain": "发布时间"
            }
        ]
        
        all_passed = True
        for i, case in enumerate(test_cases, 1):
            result = clean_content_specific(case["input"])
            
            contains_should = case["should_contain"] in result
            not_contains_should_not = case["should_not_contain"] not in result
            
            if contains_should and not_contains_should_not:
                print(f"✅ 测试 {i}: {case['name']}")
            else:
                print(f"❌ 测试 {i}: {case['name']}")
                print(f"   输入: {case['input']}")
                print(f"   输出: {result}")
                print(f"   应包含 '{case['should_contain']}': {contains_should}")
                print(f"   不应包含 '{case['should_not_contain']}': {not_contains_should_not}")
                all_passed = False
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_integration_with_crawler():
    """测试与爬虫的集成"""
    print("\n" + "=" * 80)
    print("🧪 测试与爬虫的集成")
    print("=" * 80)
    
    try:
        # 模拟爬虫中的使用场景
        from core.txt_clear import enhanced_content_filter
        
        # 模拟从网页抓取的原始HTML内容
        raw_html_content = """
        <html>
        <head><title>测试页面</title></head>
        <body>
            <div class="header">网站导航</div>
            <div class="breadcrumb">首页 > 新闻 > 正文</div>
            
            <article>
                <h1>这是文章标题</h1>
                <div class="meta">
                    发布时间：2025-01-11 14:30:00
                    作者：记者张三
                </div>
                
                <div class="content">
                    <p>这是文章的第一段正文内容，包含重要信息。</p>
                    <p>这是文章的第二段正文内容&nbsp;包含更多信息。</p>
                    <p>这是文章的第三段正文内容，继续描述事件。</p>
                </div>
                
                <div class="tags">标签：政策 经济 发展</div>
                <div class="share">分享到：微信 微博 QQ</div>
                <div class="comments">评论 (10) 点赞 (50)</div>
            </article>
            
            <footer>
                <p>版权所有 © 2025 新闻网站</p>
                <p>联系我们 | 免责声明 | 隐私政策</p>
            </footer>
        </body>
        </html>
        """
        
        print("📄 模拟原始HTML内容 (部分):")
        print(raw_html_content[:300] + "...")
        
        # 使用增强型过滤处理
        filtered_content = enhanced_content_filter(raw_html_content)
        
        print(f"\n✨ 增强型过滤后的内容:")
        print("-" * 60)
        print(filtered_content)
        print("-" * 60)
        
        # 验证结果
        expected_content = [
            "这是文章标题",
            "第一段正文内容",
            "第二段正文内容", 
            "第三段正文内容"
        ]
        
        unwanted_content = [
            "网站导航", "首页", "发布时间", "分享到", "版权所有", "联系我们"
        ]
        
        found_expected = sum(1 for content in expected_content if content in filtered_content)
        found_unwanted = sum(1 for content in unwanted_content if content in filtered_content)
        
        print(f"\n📊 集成测试结果:")
        print(f"   保留期望内容: {found_expected}/{len(expected_content)}")
        print(f"   清除不需要内容: {len(unwanted_content) - found_unwanted}/{len(unwanted_content)}")
        
        if found_expected >= len(expected_content) * 0.8 and found_unwanted == 0:
            print("✅ 与爬虫集成测试通过")
            return True
        else:
            print("❌ 与爬虫集成测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试增强型正文过滤功能")
    
    results = []
    
    # 基本功能测试
    results.append(test_enhanced_content_filter())
    
    # 特定清洗功能测试
    results.append(test_specific_cleaning_functions())
    
    # 集成测试
    results.append(test_integration_with_crawler())
    
    # 总结结果
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！增强型正文过滤功能正常！")
        print("\n💡 功能特点:")
        print("   • 集成传统过滤和非文本字段清洗")
        print("   • 智能清除导航、版权、社交分享等无关内容")
        print("   • 保留核心正文内容")
        print("   • 自动回退机制，确保兼容性")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
