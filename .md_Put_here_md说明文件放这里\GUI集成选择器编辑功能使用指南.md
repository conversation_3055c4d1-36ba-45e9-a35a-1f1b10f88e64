# 🎯 GUI集成选择器编辑功能使用指南

## 📋 概述

为了提供更好的用户体验，我们将自定义字段选择器功能直接集成到了主界面中，用户无需打开单独的对话框就可以编辑字段选择器。

## 🚀 功能位置

在GUI主界面的 **"字段配置"** 标签页中，您会看到两个子标签页：
1. **字段选择**: 原有的字段预设和自定义字段选择功能
2. **选择器编辑**: 新增的选择器编辑功能

## 🔧 界面结构

### 字段配置标签页结构
```
字段配置
├── 字段选择 (子标签页)
│   ├── 字段配置开关
│   ├── 字段预设选择
│   ├── 自定义字段选择
│   ├── 字段预览
│   └── 字段操作
└── 选择器编辑 (子标签页)
    ├── 选择器编辑开关
    ├── 选择器编辑区域
    └── 选择器操作
```

## 🎛️ 选择器编辑功能

### 1. 选择器编辑开关
- **位置**: 选择器编辑子标签页顶部
- **控件**: "启用选择器编辑" 复选框
- **默认状态**: 关闭（使用默认选择器）
- **状态指示**: 
  - 🔒 使用默认选择器（关闭时）
  - 🔓 选择器编辑已启用（开启时）

### 2. 选择器编辑区域
- **分类显示**: 按基础字段和扩展字段分类
- **字段信息**: 显示字段名称、类型、描述
- **选择器编辑**: 每个字段都有专门的编辑框
- **单独测试**: 每个字段都有独立的测试按钮

### 3. 选择器操作
- **重置为默认**: 重置所有选择器为默认值
- **测试选择器**: 测试当前所有选择器配置
- **保存选择器**: 保存当前选择器配置

## 📖 使用步骤

### 步骤1: 进入选择器编辑
1. 打开GUI主界面
2. 点击 **"字段配置"** 标签页
3. 点击 **"选择器编辑"** 子标签页

### 步骤2: 启用选择器编辑
1. 勾选 **"启用选择器编辑"** 复选框
2. 状态指示器变为 🔓 选择器编辑已启用
3. 选择器编辑区域和操作按钮变为可用状态

### 步骤3: 编辑字段选择器
1. 在编辑区域中找到要编辑的字段
2. 在选择器编辑框中输入CSS选择器
3. 多个选择器用逗号分隔
4. 点击字段旁边的 **"测试"** 按钮验证单个字段

### 步骤4: 保存配置
1. 编辑完成后点击 **"保存选择器"** 按钮
2. 系统会保存所有字段的选择器配置
3. 配置将自动应用到后续的爬取任务

## 🎯 字段选择器编辑

### 基础字段编辑
包含8个基础字段的选择器编辑：
- **title (标题)**: 文章标题选择器
- **content (内容)**: 文章正文选择器
- **dateget (发布日期)**: 发布日期选择器
- **source (来源)**: 来源/作者选择器
- **articlelink (文章链接)**: 文章链接选择器
- **classid (分类ID)**: 分类标识选择器
- **city (城市)**: 城市信息选择器
- **getdate (采集时间)**: 采集时间选择器

### 扩展字段编辑
包含14个扩展字段的选择器编辑：
- **likes (点赞数)**: 点赞数量选择器
- **views (阅读量)**: 阅读量选择器
- **comments (评论数)**: 评论数量选择器
- **shares (分享数)**: 分享次数选择器
- **price (价格)**: 价格信息选择器
- **sales (成交量)**: 成交量选择器
- **rating (评分)**: 评分选择器
- **tags (标签)**: 标签列表选择器
- **category (分类)**: 分类选择器
- **word_count (字数)**: 字数统计选择器
- **reading_time (阅读时长)**: 阅读时长选择器
- **update_time (更新时间)**: 更新时间选择器
- **author_avatar (作者头像)**: 作者头像选择器
- **publish_platform (发布平台)**: 发布平台选择器

## 💡 选择器编写技巧

### 基本语法
```css
.class-name          /* 类选择器 */
#element-id          /* ID选择器 */
tag-name             /* 标签选择器 */
[attribute]          /* 属性选择器 */
[attribute="value"]  /* 属性值选择器 */
```

### 多选择器示例
```css
/* 点赞数选择器 */
.like-count, .praise-num, [data-likes], .zan-count

/* 阅读量选择器 */
.view-count, .read-num, [data-views], .pv-count

/* 价格选择器 */
.price, .cost, [data-price], .money, .amount
```

### 优先级策略
1. **具体到通用**: 先写具体的选择器，再写通用的
2. **常见到罕见**: 先写常见网站的选择器，再写特殊的
3. **稳定到变化**: 先写稳定的选择器，再写可能变化的

## 🧪 测试功能

### 单个字段测试
1. 在字段编辑器旁边点击 **"测试"** 按钮
2. 系统会测试该字段的所有选择器
3. 显示测试结果和示例数据

### 批量测试
1. 点击 **"测试选择器"** 按钮
2. 系统会测试所有字段的选择器
3. 显示详细的测试报告

### 测试前提
- 需要在主界面输入要测试的URL
- 确保网络连接正常
- 目标网站可以正常访问

## 🔄 工作流程

### 典型编辑流程
```
1. 进入选择器编辑子标签页
   ↓
2. 启用选择器编辑开关
   ↓
3. 编辑需要的字段选择器
   ↓
4. 使用单个字段测试验证
   ↓
5. 使用批量测试验证所有字段
   ↓
6. 保存选择器配置
   ↓
7. 在字段选择子标签页配置字段
   ↓
8. 开始爬取任务
```

### 问题排查流程
```
1. 发现字段提取失败
   ↓
2. 进入选择器编辑子标签页
   ↓
3. 启用选择器编辑
   ↓
4. 检查对应字段的选择器
   ↓
5. 使用单个字段测试
   ↓
6. 根据测试结果调整选择器
   ↓
7. 重新测试直到成功
   ↓
8. 保存配置并重新爬取
```

## ⚙️ 高级功能

### 1. 选择器优先级
- 系统按顺序尝试选择器
- 第一个匹配的选择器被使用
- 建议将最可能匹配的选择器放在前面

### 2. 容错机制
- 如果所有选择器都失败，使用默认值
- 系统会记录选择器失败的情况
- 可以通过日志查看详细信息

### 3. 配置持久化
- 选择器配置自动保存到配置文件
- 重启应用后自动恢复配置
- 支持配置的导出和导入

## 🚨 注意事项

### 1. 选择器语法
- 使用标准CSS选择器语法
- 避免浏览器特定的选择器
- 测试选择器的兼容性

### 2. 性能考虑
- 避免过于复杂的选择器
- 优先使用类选择器和ID选择器
- 减少不必要的选择器数量

### 3. 维护管理
- 定期测试选择器的有效性
- 及时更新失效的选择器
- 为重要配置做好备份

### 4. 安全性
- 选择器配置会影响数据提取
- 谨慎修改关键字段的选择器
- 在正式使用前充分测试

## 🎉 优势特点

### 1. 集成化设计
- 无需打开单独对话框
- 与字段选择功能无缝集成
- 统一的用户界面体验

### 2. 实时编辑
- 即时编辑选择器
- 实时测试验证
- 快速保存应用

### 3. 分类管理
- 基础字段和扩展字段分类显示
- 清晰的字段信息展示
- 便于查找和编辑

### 4. 操作便捷
- 单个字段独立测试
- 批量操作支持
- 一键重置和保存

## 💡 最佳实践

### 1. 渐进式编辑
- 从最重要的字段开始编辑
- 逐步完善选择器配置
- 每次编辑后都要测试

### 2. 备份策略
- 编辑前导出当前配置
- 重要修改后及时保存
- 定期备份选择器配置

### 3. 测试验证
- 使用多个不同的测试URL
- 验证选择器在不同页面的效果
- 关注边界情况和异常处理

### 4. 文档记录
- 记录重要的选择器修改
- 说明修改的原因和效果
- 便于团队协作和维护

## 🎯 总结

GUI集成的选择器编辑功能提供了：

1. **便捷的编辑体验**: 直接在主界面编辑，无需额外对话框
2. **完整的功能支持**: 包含编辑、测试、保存等完整功能
3. **清晰的界面布局**: 分类显示，信息完整，操作便捷
4. **强大的测试能力**: 支持单个字段和批量测试
5. **灵活的配置管理**: 支持重置、保存、导入导出

这个集成设计大大提升了选择器编辑的便捷性和效率，让用户能够更轻松地定制字段提取规则，适应各种不同的网站结构。
