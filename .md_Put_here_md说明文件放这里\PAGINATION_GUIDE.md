# 翻页功能模块使用指南

## 概述

翻页功能模块 (`pagination_handler.py`) 是一个专门处理各种不同类型网站翻页逻辑的模块。它支持多种翻页模式，能够自动检测网站的翻页类型，并提供了内置的常见网站配置。

## 主要特性

- ✅ **多种翻页模式支持**: 后缀模式、查询参数模式、自定义模式
- ✅ **自动类型检测**: 智能识别网站使用的翻页方式
- ✅ **内置网站配置**: 预配置常见网站的翻页规则
- ✅ **自定义起始页码**: 支持从0、2等非标准页码开始
- ✅ **向后兼容**: 完全兼容原有的翻页接口
- ✅ **错误处理**: 完善的异常处理和参数验证

## 基本使用

### 1. 导入模块

```python
import pagination_handler
```

### 2. 生成翻页URL

```python
# 基本用法
page_url = pagination_handler.get_page_url(
    input_url="http://example.com/news",
    page_num=2,
    page_suffix="index_{n}.html"
)
# 结果: http://example.com/news/index_2.html
```

### 3. 在爬虫中使用

```python
import crawler

# 原有的接口仍然可用
page_url = crawler.get_page_url(
    input_url="http://example.com/news",
    page_num=2,
    page_suffix="index_{n}.html"
)
```

## 支持的翻页模式

### 1. 后缀模式 (Suffix Mode)

适用于URL路径中包含页码的网站。

```python
# 标准index模式
page_url = pagination_handler.get_page_url(
    input_url="http://www.gov.cn/news",
    page_num=2,
    page_suffix="index_{n}.html"
)
# 结果: http://www.gov.cn/news/index_2.html

# page模式
page_url = pagination_handler.get_page_url(
    input_url="http://company.com/articles",
    page_num=3,
    page_suffix="page_{n}.html"
)
# 结果: http://company.com/articles/page_3.html
```

### 2. 查询参数模式 (Query Mode)

适用于通过URL查询参数传递页码的网站。

```python
# 标准page参数
page_url = pagination_handler.get_page_url(
    input_url="http://news.sina.com.cn/latest?category=politics",
    page_num=2,
    page_suffix="?page={n}"
)
# 结果: http://news.sina.com.cn/latest?category=politics&page=2

# 自定义参数名
page_url = pagination_handler.get_page_url(
    input_url="http://bbs.tianya.cn/list",
    page_num=3,
    page_suffix="?pn={n}"
)
# 结果: http://bbs.tianya.cn/list?pn=3
```

### 3. 自定义模式 (Custom Mode)

支持任意自定义的翻页格式。

```python
# 复杂路径格式
page_url = pagination_handler.get_page_url(
    input_url="http://example.com/category/news",
    page_num=2,
    page_suffix="list_{n}/more.html"
)
# 结果: http://example.com/category/news/list_2/more.html
```

## 高级功能

### 1. 自定义起始页码

某些网站的第二页不是从2开始，而是从0或其他数字开始。

```python
# 起始页码为0
page_url = pagination_handler.get_page_url(
    input_url="http://example.com/articles",
    page_num=2,
    page_suffix="page_{n}.html",
    page_suffix_start=0
)
# 结果: http://example.com/articles/page_1.html

# 起始页码为2
page_url = pagination_handler.get_page_url(
    input_url="http://example.com/news",
    page_num=2,
    page_suffix="index_{n}.html",
    page_suffix_start=2
)
# 结果: http://example.com/news/index_3.html
```

### 2. 自动类型检测

系统可以自动检测网站使用的翻页模式。

```python
# 检测翻页类型
pagination_type = pagination_handler.pagination_handler.detect_pagination_type(
    url="http://example.com/news/index_2.html"
)
# 结果: "suffix"

pagination_type = pagination_handler.pagination_handler.detect_pagination_type(
    url="http://example.com/articles?page=3"
)
# 结果: "query"
```

### 3. 网站特定配置

系统内置了常见网站的翻页配置。

```python
# 获取网站配置
config = pagination_handler.get_site_pagination_config("http://www.gov.cn/news")
# 结果: {'type': 'suffix', 'suffix': 'index_{n}.html', 'start': 1}

# 使用网站配置
if config:
    page_url = pagination_handler.get_page_url(
        input_url="http://www.gov.cn/news",
        page_num=2,
        page_suffix=config['suffix'],
        page_suffix_start=config['start']
    )
```

### 4. 翻页模式分析

分析一组URL，自动检测翻页模式。

```python
urls = [
    'http://example.com/news',
    'http://example.com/news/index_2.html',
    'http://example.com/news/index_3.html'
]

result = pagination_handler.detect_pagination_patterns(urls)
print(f"检测到的模式: {result['patterns']}")
print(f"主要类型: {result['type']}")
print(f"置信度: {result['confidence']}")
print(f"建议后缀: {result['suggested_suffix']}")
```

## 内置网站配置

系统预配置了以下常见网站的翻页规则：

| 网站类型 | 域名模式 | 翻页类型 | 后缀格式 | 起始页码 |
|---------|---------|---------|---------|---------|
| 政府网站 | gov.cn | suffix | index_{n}.html | 1 |
| 人民网 | people.com.cn | suffix | index{n}.html | 2 |
| 新华网 | xinhuanet.com | query | ?page={n} | 1 |
| 新浪网 | sina.com.cn | query | ?page={n} | 1 |
| 搜狐网 | sohu.com | suffix | _{n}.shtml | 2 |
| 天涯论坛 | tianya.cn | query | ?pn={n} | 1 |

## 错误处理

模块提供了完善的错误处理机制：

```python
try:
    page_url = pagination_handler.get_page_url(
        input_url="",  # 空URL
        page_num=2,
        page_suffix="index_{n}.html"
    )
except ValueError as e:
    print(f"参数错误: {e}")

try:
    page_url = pagination_handler.get_page_url(
        input_url="http://example.com",
        page_num=-1,  # 无效页码
        page_suffix="index_{n}.html"
    )
except ValueError as e:
    print(f"页码错误: {e}")
```

## 在爬虫中的集成

翻页模块已经完全集成到主爬虫系统中：

```python
import crawler

# 使用新的翻页功能进行爬取
result = crawler.crawl_articles(
    input_url="http://example.com/news",
    base_url="http://example.com",
    max_pages=5,
    page_suffix="index_{n}.html",  # 支持所有翻页模式
    page_suffix_start=1,           # 支持自定义起始页码
    # ... 其他参数
)
```

## 扩展和自定义

### 添加新的网站配置

```python
# 在pagination_handler.py中的SITE_PAGINATION_CONFIGS字典中添加
SITE_PAGINATION_CONFIGS['new-site.com'] = {
    'type': 'suffix',
    'suffix': 'p{n}.html',
    'start': 1
}
```

### 添加新的翻页模式

```python
# 在PaginationHandler类中添加新的模式检测
self.pagination_patterns['custom_pattern'] = r'custom_(\d+)\.html?'
```

## 最佳实践

1. **优先使用内置配置**: 对于常见网站，系统会自动应用最佳配置
2. **测试翻页URL**: 在正式爬取前，先测试生成的翻页URL是否正确
3. **处理异常**: 始终包含适当的错误处理代码
4. **验证页码范围**: 确保页码在合理范围内
5. **使用自动检测**: 对于未知网站，可以先使用自动检测功能

## 示例代码

完整的使用示例请参考：
- `test_pagination_handler.py` - 功能测试
- `demo_pagination_features.py` - 功能演示
- `test_crawler_with_pagination.py` - 爬虫集成测试

## 总结

翻页功能模块提供了强大而灵活的翻页处理能力，支持各种不同类型网站的翻页逻辑。通过模块化设计，它既保持了向后兼容性，又提供了丰富的新功能，大大提升了爬虫系统处理不同网站的能力。
