#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查配置文件问题
"""

import os
import json
import platform

def check_config_file():
    """检查配置文件"""
    print("🔧 检查模组配置文件")
    print("="*50)
    
    config_file = "configs/modules/module_configs.json"
    
    # 检查文件是否存在
    print(f"配置文件路径: {config_file}")
    print(f"绝对路径: {os.path.abspath(config_file)}")
    print(f"当前工作目录: {os.getcwd()}")
    
    if os.path.exists(config_file):
        print("✅ 配置文件存在")
        
        # 检查文件权限
        try:
            # 检查读权限
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print("✅ 文件可读")
            print(f"  配置项数量: {len(data)}")
            
            # 检查写权限
            test_data = data.copy()
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=4)
            print("✅ 文件可写")
            
        except PermissionError as e:
            print(f"❌ 文件权限错误: {e}")
            return False
        except json.JSONDecodeError as e:
            print(f"❌ JSON格式错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 文件访问错误: {e}")
            return False
            
    else:
        print("❌ 配置文件不存在")
        
        # 检查目录是否存在
        config_dir = os.path.dirname(config_file)
        if os.path.exists(config_dir):
            print(f"✅ 配置目录存在: {config_dir}")
        else:
            print(f"❌ 配置目录不存在: {config_dir}")
            return False
    
    return True

def test_gui_config_path():
    """测试GUI配置路径"""
    print("\n🖥️ 测试GUI配置路径")
    print("="*50)
    
    try:
        # 模拟GUI中的路径获取
        config_file = "configs/modules/module_configs.json"
        
        print(f"GUI配置路径: {config_file}")
        print(f"文件存在: {os.path.exists(config_file)}")
        
        if os.path.exists(config_file):
            # 模拟打开文件操作
            system = platform.system()
            print(f"操作系统: {system}")
            
            if system == "Windows":
                print("将使用 os.startfile() 打开文件")
                # 不实际执行，只是测试
                print("✅ Windows文件打开方式正确")
            elif system == "Darwin":
                print("将使用 open 命令打开文件")
                print("✅ macOS文件打开方式正确")
            else:
                print("将使用 xdg-open 命令打开文件")
                print("✅ Linux文件打开方式正确")
            
            return True
        else:
            print("❌ GUI无法找到配置文件")
            return False
            
    except Exception as e:
        print(f"❌ GUI配置路径测试失败: {e}")
        return False

def test_module_manager():
    """测试模组管理器"""
    print("\n📦 测试模组管理器")
    print("="*50)
    
    try:
        from modules.manager import module_manager
        
        print(f"模组管理器配置文件: {module_manager.config_file}")
        print(f"已加载模组数量: {len(module_manager.modules)}")
        
        for module_name in module_manager.modules:
            print(f"  - {module_name}")
        
        # 测试保存功能
        if module_manager.save_modules():
            print("✅ 模组配置保存成功")
        else:
            print("❌ 模组配置保存失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模组管理器测试失败: {e}")
        return False

def create_config_if_missing():
    """如果配置文件缺失，创建默认配置"""
    print("\n🔧 检查并创建配置文件")
    print("="*50)
    
    config_file = "configs/modules/module_configs.json"
    
    if not os.path.exists(config_file):
        print("配置文件不存在，创建默认配置...")
        
        # 确保目录存在
        config_dir = os.path.dirname(config_file)
        os.makedirs(config_dir, exist_ok=True)
        
        # 创建默认配置
        default_config = {
            "微信公众号": {
                "name": "微信公众号",
                "description": "处理微信公众号文章的模组配置",
                "domain_patterns": ["mp.weixin.qq.com"],
                "url_patterns": [],
                "config": {
                    "title_selectors": ["#activity-name"],
                    "content_selectors": ["#js_content"],
                    "date_selectors": ["#publish_time"],
                    "source_selectors": ["#js_name"],
                    "mode": "safe",
                    "headless": True,
                    "file_format": "CSV"
                }
            },
            "珠海政协": {
                "name": "珠海政协",
                "description": "从配置组 '珠海政协' 创建的模组",
                "domain_patterns": ["www.zhzx.gov.cn"],
                "url_patterns": [".*www\\.zhzx\\.gov\\.cn/zwhgz/jjwkjw/.*"],
                "config": {
                    "title_selectors": [".title", "h1", ".article-title"],
                    "content_selectors": [".content", ".article-content", "#content"],
                    "date_selectors": [".date", ".time", ".publish-time"],
                    "source_selectors": [".source", ".author"],
                    "mode": "safe",
                    "headless": True,
                    "file_format": "CSV"
                }
            },
            "match_priority": ["珠海政协", "微信公众号"]
        }
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=4)
            print(f"✅ 默认配置文件已创建: {config_file}")
            return True
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False
    else:
        print("✅ 配置文件已存在")
        return True

def main():
    """主函数"""
    print("🧪 配置文件检查和修复")
    print("="*80)
    
    tests = [
        ("配置文件检查", check_config_file),
        ("创建配置文件", create_config_if_missing),
        ("GUI配置路径测试", test_gui_config_path),
        ("模组管理器测试", test_module_manager)
    ]
    
    results = {}
    passed = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "="*80)
    print("🎯 测试结果汇总")
    print("="*80)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("\n🎉 配置文件问题已解决！")
        print("✅ 配置文件存在且可访问")
        print("✅ GUI可以正确打开配置文件")
        print("✅ 模组管理器正常工作")
        print("\n🚀 现在GUI应该可以正常打开配置文件了")
    else:
        print(f"\n⚠️ 仍有 {len(tests) - passed} 个问题需要解决")
    
    return passed == len(tests)

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
