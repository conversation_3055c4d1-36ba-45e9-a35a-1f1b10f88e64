def normalize_source(src_str):
    """
    标准化来源字符串：
    1. 去除常见来源前缀
    2. 截断第一个空格后的内容（通常是日期）
    3. 处理特殊引号情况
    
    示例输入: "来源：“北蔡家园”微信公众号 2025/4/10 14:12:46"
    示例输出: "北蔡家园微信公众号"
    """
    src_str = src_str.strip()
    
    # 定义需要去除的来源前缀列表
    source_prefixes = [
        "来源：", "来源:", 
        "信息来源：", "信息来源:",
        "来源单位：", "来源单位:",
        "文章来源：", "文章来源:"
    ]
    
    # 逐个检查并去除前缀
    for prefix in source_prefixes:
        if src_str.startswith(prefix):
            src_str = src_str[len(prefix):].lstrip()
            break
    
    # 特殊处理：去除开头可能的中文引号
    if src_str.startswith("“") and "”" in src_str:
        # 提取引号内的内容（保留引号内的空格）
        start_index = 1
        end_index = src_str.find("”", 1)
        if end_index != -1:
            src_str = src_str[start_index:end_index] + src_str[end_index+1:]
    
    # 截断第一个空格后的所有内容
    if " " in src_str:
        src_str = src_str.split(" ", 1)[0]
    
    return src_str.strip()

# 测试用例
test_cases = [
    '来源：“北蔡家园”微信公众号 2025/4/10 14:12:46',
    '来源: 上海发布 2025-03-15',
    '来源单位：人民日报  2025年12月31日',
    '信息来源: "央视新闻" 2025/7/1',
    '无前缀直接内容 2025/01/01'
]

for test in test_cases:
    print(f"输入: '{test}'")
    print(f"输出: '{normalize_source(test)}'\n")