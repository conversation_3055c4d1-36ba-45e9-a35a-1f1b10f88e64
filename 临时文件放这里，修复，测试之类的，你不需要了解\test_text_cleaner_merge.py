#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试合并后的text_cleaner.py功能
验证所有函数是否正常工作
"""

import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_functions():
    """测试基本的文本清洗函数"""
    print("🧪 测试基本文本清洗函数")
    print("=" * 50)
    
    try:
        from utils.text_cleaner import (
            normalize_date, normalize_source, clean_title, 
            clean_html_content, filter_content, clean_text
        )
        
        # 测试日期标准化
        test_date = "2024年1月15日 10:30:45"
        normalized_date = normalize_date(test_date)
        print(f"✅ 日期标准化: '{test_date}' -> '{normalized_date}'")
        
        # 测试来源标准化
        test_source = '来源："北蔡家园"微信公众号 2025/4/10 14:12:46'
        normalized_source = normalize_source(test_source)
        print(f"✅ 来源标准化: '{test_source}' -> '{normalized_source}'")
        
        # 测试标题清洗
        test_title = "  测试标题\n\r  来源：测试网站 作者：测试作者  "
        cleaned_title = clean_title(test_title)
        print(f"✅ 标题清洗: '{test_title}' -> '{cleaned_title}'")
        
        # 测试HTML内容清洗
        test_html = "<p>测试内容</p><script>alert('test')</script><br/>换行"
        cleaned_html = clean_html_content(test_html)
        print(f"✅ HTML清洗: '{test_html}' -> '{cleaned_html}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本函数测试失败: {e}")
        return False

def test_advanced_functions():
    """测试高级文本清洗函数"""
    print("\n🧪 测试高级文本清洗函数")
    print("=" * 50)
    
    try:
        from utils.text_cleaner import (
            clean_non_text_fields, clean_string_field, enhanced_content_filter,
            clean_content_balanced, clean_content_specific
        )
        
        # 测试非文本字段清洗
        test_data = {
            "title": "<h1>测试标题</h1>",
            "content": "内容包含特殊字符★▪▫",
            "number": 123.000,
            "list": ["项目1", "<b>项目2</b>", 456]
        }
        cleaned_data = clean_non_text_fields(test_data)
        print(f"✅ 非文本字段清洗: {type(cleaned_data)} 数据已清洗")
        
        # 测试字符串字段清洗
        test_string = "<p>测试内容</p>包含★特殊符号   多余空格\n\n\n"
        cleaned_string = clean_string_field(test_string)
        print(f"✅ 字符串字段清洗: '{test_string[:30]}...' -> '{cleaned_string[:30]}...'")
        
        # 测试增强型内容过滤
        test_content = """
        <div>
        <h1>测试标题</h1>
        <p>这是正文内容。</p>
        <div class="meta">发布时间：2025-01-11</div>
        <p>这是另一段正文。</p>
        </div>
        """
        filtered_content = enhanced_content_filter(test_content)
        print(f"✅ 增强型内容过滤: 处理了 {len(test_content)} 字符，输出 {len(filtered_content)} 字符")
        
        # 测试平衡模式清洗
        balanced_content = clean_content_balanced(test_content)
        print(f"✅ 平衡模式清洗: 保留换行符数量 {balanced_content.count(chr(10))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 高级函数测试失败: {e}")
        return False

def test_conservative_functions():
    """测试保守清洗函数"""
    print("\n🧪 测试保守清洗函数")
    print("=" * 50)
    
    try:
        from utils.text_cleaner import (
            clean_string_field_conservative, clean_punctuation_conservative,
            is_useful_content, light_clean_sentence
        )
        
        # 测试保守字符串清洗
        test_text = "测试内容，包含。。。重复标点！！！"
        conservative_cleaned = clean_string_field_conservative(test_text)
        print(f"✅ 保守字符串清洗: '{test_text}' -> '{conservative_cleaned}'")
        
        # 测试保守标点清洗
        test_punctuation = "测试，，，内容。。。！！！"
        cleaned_punctuation = clean_punctuation_conservative(test_punctuation)
        print(f"✅ 保守标点清洗: '{test_punctuation}' -> '{cleaned_punctuation}'")
        
        # 测试有用内容判断
        useful_sentence = "这是一段有用的正文内容，包含实际信息。"
        useless_sentence = "发布时间：2025-01-11 10:30:45"
        
        is_useful1 = is_useful_content(useful_sentence)
        is_useful2 = is_useful_content(useless_sentence)
        print(f"✅ 有用内容判断: '{useful_sentence[:20]}...' -> {is_useful1}")
        print(f"✅ 有用内容判断: '{useless_sentence}' -> {is_useful2}")
        
        # 测试轻度句子清洗
        test_sentence = "测试句子 alert('test'); 包含JS代码"
        light_cleaned = light_clean_sentence(test_sentence)
        print(f"✅ 轻度句子清洗: '{test_sentence}' -> '{light_cleaned}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 保守函数测试失败: {e}")
        return False

def test_import_compatibility():
    """测试导入兼容性"""
    print("\n🧪 测试导入兼容性")
    print("=" * 50)
    
    try:
        # 测试从utils.text_cleaner导入
        from utils.text_cleaner import normalize_date
        print("✅ 从 utils.text_cleaner 导入成功")
        
        # 测试直接导入text_cleaner模块
        from utils import text_cleaner
        print("✅ 导入 text_cleaner 模块成功")
        
        # 测试函数调用
        result = text_cleaner.normalize_date("2024-01-15")
        print(f"✅ 模块函数调用成功: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入兼容性测试失败: {e}")
        return False

def test_crawler_integration():
    """测试与爬虫模块的集成"""
    print("\n🧪 测试爬虫模块集成")
    print("=" * 50)
    
    try:
        # 测试爬虫模块是否能正常导入text_cleaner
        from core.crawler import write_article_data_async
        print("✅ 爬虫模块导入成功")
        
        # 测试enhanced_content_filter是否能被导入
        from utils.text_cleaner import enhanced_content_filter
        test_result = enhanced_content_filter("<p>测试内容</p>")
        print(f"✅ enhanced_content_filter 函数可用: '{test_result}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 爬虫集成测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🔧 开始text_cleaner合并验证测试...")
    print("=" * 60)
    
    # 运行所有测试
    test_results = []
    
    test_results.append(test_basic_functions())
    test_results.append(test_advanced_functions())
    test_results.append(test_conservative_functions())
    test_results.append(test_import_compatibility())
    test_results.append(test_crawler_integration())
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    test_names = [
        "基本文本清洗函数",
        "高级文本清洗函数", 
        "保守清洗函数",
        "导入兼容性",
        "爬虫模块集成"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n总计: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！text_cleaner.py合并成功！")
        print("\n✨ 合并完成的功能:")
        print("  - 基本文本清洗 (日期、来源、标题、HTML)")
        print("  - 高级内容过滤 (增强型、平衡模式)")
        print("  - 保守清洗模式 (保留更多内容)")
        print("  - 智能内容判断 (有用/无用内容识别)")
        print("  - 完整的导入兼容性")
        print("  - 与爬虫系统的无缝集成")
        
        print("\n🚀 现在可以使用:")
        print("  from utils.text_cleaner import normalize_date, clean_title")
        print("  from utils.text_cleaner import enhanced_content_filter")
        print("  from utils import text_cleaner")
        
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
