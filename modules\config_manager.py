#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模组配置管理界面
提供命令行界面来管理爬虫模组配置
"""

import json
import os
import sys
import logging
from typing import Dict, List, Any, Optional

try:
    from modules.manager import ModuleManager
    MODULE_MANAGER_AVAILABLE = True
except ImportError:
    MODULE_MANAGER_AVAILABLE = False
    print("警告: 无法导入模组管理器")

try:
    from core.failed_url_processor import process_failed_csv
    FAILED_PROCESSOR_AVAILABLE = True
except ImportError:
    FAILED_PROCESSOR_AVAILABLE = False
    print("警告: 无法导入失败URL处理器")

logger = logging.getLogger(__name__)

class ModuleConfigManager:
    """模组配置管理器界面"""
    
    def __init__(self):
        """初始化管理器"""
        if MODULE_MANAGER_AVAILABLE:
            self.module_manager = ModuleManager()
        else:
            self.module_manager = None
            print("模组管理器不可用，部分功能将无法使用")
    
    def show_menu(self):
        """显示主菜单"""
        print("\n" + "="*50)
        print("模组配置管理系统")
        print("="*50)
        print("1. 查看所有模组")
        print("2. 查看模组详情")
        print("3. 添加新模组")
        print("4. 更新模组配置")
        print("5. 删除模组")
        print("6. 测试URL匹配")
        print("7. 处理失败URL文件")
        print("8. 保存配置")
        print("9. 重新加载配置")
        print("0. 退出")
        print("-"*50)
    
    def list_modules(self):
        """列出所有模组"""
        if not self.module_manager:
            print("模组管理器不可用")
            return
        
        modules = self.module_manager.list_modules()
        if not modules:
            print("没有找到任何模组配置")
            return

        print(f"\n找到 {len(modules)} 个模组配置:")
        print("-"*50)
        for i, module_name in enumerate(modules, 1):
            module_info = self.module_manager.get_module_info(module_name)
            description = module_info.get('description', '无描述') if module_info else '无描述'
            default_mark = " [默认]" if hasattr(self.module_manager, 'default_module') and module_name == self.module_manager.default_module else ""
            print(f"{i}. {module_name}{default_mark}")
            print(f"   描述: {description}")
        print("-"*50)
    
    def show_module_details(self):
        """显示模组详情"""
        if not self.module_manager:
            print("模组管理器不可用")
            return
        
        module_name = input("请输入模组名称: ").strip()
        if not module_name:
            print("模组名称不能为空")
            return
        
        module_info = self.module_manager.get_module_info(module_name)
        if not module_info:
            print(f"模组 '{module_name}' 不存在")
            return
        
        print(f"\n模组详情: {module_name}")
        print("="*50)
        print(f"名称: {module_info.get('name', module_name)}")
        print(f"描述: {module_info.get('description', '无描述')}")
        
        # 显示域名模式
        domain_patterns = module_info.get('domain_patterns', [])
        print(f"域名模式: {', '.join(domain_patterns) if domain_patterns else '无'}")
        
        # 显示URL模式
        url_patterns = module_info.get('url_patterns', [])
        print(f"URL模式: {', '.join(url_patterns) if url_patterns else '无'}")
        
        # 显示配置
        config = module_info.get('config', {})
        if config:
            print("\n配置详情:")
            print("-"*30)
            for key, value in config.items():
                if isinstance(value, list):
                    print(f"{key}: {', '.join(map(str, value))}")
                else:
                    print(f"{key}: {value}")
        
        print("="*50)
    
    def add_module(self):
        """添加新模组"""
        if not self.module_manager:
            print("模组管理器不可用")
            return
        
        print("\n添加新模组")
        print("-"*30)
        
        module_name = input("模组名称: ").strip()
        if not module_name:
            print("模组名称不能为空")
            return
        
        if module_name in self.module_manager.modules:
            print(f"模组 '{module_name}' 已存在")
            return
        
        description = input("模组描述: ").strip()
        
        # 域名模式
        print("请输入域名模式（用逗号分隔，例如: mp.weixin.qq.com, *.gov.cn）:")
        domain_input = input("域名模式: ").strip()
        domain_patterns = [p.strip() for p in domain_input.split(',') if p.strip()]
        
        # URL模式
        print("请输入URL正则表达式模式（用逗号分隔）:")
        url_input = input("URL模式: ").strip()
        url_patterns = [p.strip() for p in url_input.split(',') if p.strip()]
        
        # 基本配置
        print("\n配置选择器（用逗号分隔多个选择器）:")
        title_selectors = input("标题选择器: ").strip().split(',')
        content_selectors = input("内容选择器: ").strip().split(',')
        date_selectors = input("日期选择器: ").strip().split(',')
        source_selectors = input("来源选择器: ").strip().split(',')
        
        # 清理空值
        title_selectors = [s.strip() for s in title_selectors if s.strip()]
        content_selectors = [s.strip() for s in content_selectors if s.strip()]
        date_selectors = [s.strip() for s in date_selectors if s.strip()]
        source_selectors = [s.strip() for s in source_selectors if s.strip()]
        
        # 构建模组配置
        module_config = {
            "name": module_name,
            "description": description or "用户添加的模组",
            "domain_patterns": domain_patterns,
            "url_patterns": url_patterns,
            "config": {
                "title_selectors": title_selectors,
                "content_selectors": content_selectors,
                "content_type": "CSS",
                "date_selectors": date_selectors,
                "source_selectors": source_selectors,
                "mode": "balance",
                "collect_links": True,
                "retry": 2,
                "interval": 0.5,
                "headless": True,
                "page_load_strategy": "eager"
            }
        }
        
        # 添加模组
        if self.module_manager.add_module(module_name, module_config):
            print(f"成功添加模组: {module_name}")
        else:
            print(f"添加模组失败: {module_name}")
    
    def test_url_matching(self):
        """测试URL匹配"""
        if not self.module_manager:
            print("模组管理器不可用")
            return
        
        url = input("请输入要测试的URL: ").strip()
        if not url:
            print("URL不能为空")
            return
        
        module_name = self.module_manager.match_url(url)
        if module_name:
            print(f"URL '{url}' 匹配到模组: {module_name}")

            # 显示配置
            config = self.module_manager.get_module_info(module_name)
            if config:
                print("\n匹配的配置:")
                print("-"*30)
                for key, value in config.items():
                    if isinstance(value, list):
                        print(f"{key}: {', '.join(map(str, value))}")
                    else:
                        print(f"{key}: {value}")
        else:
            print(f"URL '{url}' 没有匹配到任何模组")
    
    def process_failed_urls(self):
        """处理失败URL文件"""
        if not FAILED_PROCESSOR_AVAILABLE:
            print("失败URL处理器不可用")
            return
        
        print("\n处理失败URL文件")
        print("-"*30)
        
        failed_csv_path = input("失败CSV文件路径: ").strip()
        if not failed_csv_path:
            print("文件路径不能为空")
            return
        
        if not os.path.exists(failed_csv_path):
            print(f"文件不存在: {failed_csv_path}")
            return
        
        save_dir = input("保存目录 (默认: articles): ").strip() or "articles"
        export_filename = input("导出文件名 (可选): ").strip() or None
        file_format = input("文件格式 (CSV/Excel, 默认: CSV): ").strip().upper() or "CSV"
        classid = input("分类ID (可选): ").strip() or ""
        
        try:
            retry = int(input("重试次数 (默认: 3): ").strip() or "3")
            interval = float(input("重试间隔秒数 (默认: 1.0): ").strip() or "1.0")
            max_workers = int(input("最大工作线程数 (默认: 5): ").strip() or "5")
        except ValueError:
            print("输入的数值格式不正确，使用默认值")
            retry = 3
            interval = 1.0
            max_workers = 5
        
        print(f"\n开始处理失败文件: {failed_csv_path}")
        print("处理中，请稍候...")
        
        try:
            result = process_failed_csv(
                failed_csv_path=failed_csv_path,
                save_dir=save_dir,
                export_filename=export_filename,
                file_format=file_format,
                classid=classid,
                retry=retry,
                interval=interval,
                max_workers=max_workers
            )
            
            print(f"\n处理完成!")
            print(f"总计: {result['total']}")
            print(f"成功: {result['success']}")
            print(f"失败: {result['failed']}")
            print(f"成功率: {result['success']/result['total']*100:.1f}%" if result['total'] > 0 else "0%")
            
        except Exception as e:
            print(f"处理失败: {e}")
    
    def save_config(self):
        """保存配置"""
        if not self.module_manager:
            print("模组管理器不可用")
            return
        
        if self.module_manager.save_modules():
            print("配置已保存")
        else:
            print("保存配置失败")
    
    def reload_config(self):
        """重新加载配置"""
        if not self.module_manager:
            print("模组管理器不可用")
            return
        
        self.module_manager.load_modules()
        print("配置已重新加载")
    
    def run(self):
        """运行管理界面"""
        while True:
            self.show_menu()
            choice = input("请选择操作 (0-9): ").strip()
            
            if choice == '0':
                print("退出程序")
                break
            elif choice == '1':
                self.list_modules()
            elif choice == '2':
                self.show_module_details()
            elif choice == '3':
                self.add_module()
            elif choice == '4':
                print("更新模组功能待实现")
            elif choice == '5':
                print("删除模组功能待实现")
            elif choice == '6':
                self.test_url_matching()
            elif choice == '7':
                self.process_failed_urls()
            elif choice == '8':
                self.save_config()
            elif choice == '9':
                self.reload_config()
            else:
                print("无效的选择，请重新输入")
            
            input("\n按回车键继续...")


def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO)
    
    print("模组配置管理系统")
    print("="*50)
    
    if not MODULE_MANAGER_AVAILABLE:
        print("错误: 模组管理器不可用，请检查modules.manager.py文件")
        return
    
    manager = ModuleConfigManager()
    manager.run()


if __name__ == "__main__":
    main()
