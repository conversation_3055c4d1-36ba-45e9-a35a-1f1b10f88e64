# 🔧 自定义字段功能说明

## 📋 功能概述

实现了用户可以自己填写字段名和值的自定义字段功能，类似于classid字段的使用方式。用户可以通过GUI界面添加任意的字段名和对应的固定值，这些字段会在爬取时自动添加到每条数据中。

## 🎯 功能特性

### 1. 自定义字段对话框
- **字段名输入**: 用户可以自定义字段名称（如：author, category, tags等）
- **字段值输入**: 用户可以设置该字段的固定值（如：管理员, 新闻, 重要等）
- **预设字段**: 提供常用字段的快速添加功能
- **批量管理**: 支持添加多个自定义字段

### 2. 预设字段快速添加
提供5个常用字段的快速添加按钮：
- **作者** (author): 默认值 "管理员"
- **分类** (category): 默认值 "新闻"
- **标签** (tags): 默认值 "重要"
- **来源** (source_type): 默认值 "官方"
- **状态** (status): 默认值 "已发布"

### 3. 完整的数据流集成
- GUI配置 → 爬虫配置 → 字段提取器 → 最终数据
- 自定义字段会自动添加到每条爬取的数据中
- 与现有字段系统无缝集成

## 🛠️ 技术实现

### 1. GUI界面实现

#### 自定义字段对话框 (`CustomFieldDialog`)
```python
class CustomFieldDialog(QDialog):
    """自定义字段对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("自定义字段")
        self.setModal(True)
        self.resize(500, 400)
        
        # 存储字段行
        self.field_rows = []
        
        self.setup_ui()
```

#### 字段行管理
```python
def add_field_row(self, field_name="", field_value=""):
    """添加字段行"""
    # 字段名输入
    name_input = QLineEdit()
    name_input.setPlaceholderText("如: author, category, tags")
    
    # 字段值输入
    value_input = QLineEdit()
    value_input.setPlaceholderText("如: 管理员, 新闻, 重要")
    
    # 删除按钮
    delete_button = QPushButton("删除")
```

#### 预设字段快速添加
```python
presets = [
    ("作者", "author", "管理员"),
    ("分类", "category", "新闻"),
    ("标签", "tags", "重要"),
    ("来源", "source_type", "官方"),
    ("状态", "status", "已发布")
]

for display_name, field_name, default_value in presets:
    btn = QPushButton(f"{display_name}")
    btn.clicked.connect(lambda checked, fn=field_name, dv=default_value: self.add_preset_field(fn, dv))
```

### 2. 配置集成

#### GUI配置获取
```python
def get_field_config_from_gui(self):
    """从GUI获取字段配置"""
    # 获取自定义字段（用户自己填写的字段名和值）
    custom_fields = {}
    if hasattr(self, 'custom_fields'):
        custom_fields = self.custom_fields.copy()

    return {
        'use_field_config': True,
        'field_preset': field_preset,
        'custom_field_list': custom_field_list,
        'custom_fields': custom_fields  # 新增自定义字段
    }
```

#### 爬虫配置准备
```python
def prepare_crawler_config(self, gui_config):
    """将GUI配置转换为爬虫配置"""
    crawler_config = {
        # ... 其他配置 ...
        # 字段配置
        'field_preset': gui_config.get('field_preset', ''),
        'custom_field_list': gui_config.get('custom_field_list', []),
        'user_custom_fields': gui_config.get('custom_fields', {}),  # 用户自定义字段
        'use_field_config': gui_config.get('use_field_config', False),
    }
```

### 3. 爬虫集成

#### 函数参数添加
```python
async def save_article_async(
    # ... 其他参数 ...
    field_preset=None,      # 字段预设名称
    custom_field_list=None, # 自定义字段列表
    user_custom_fields=None, # 用户自定义字段字典（字段名:值）
    use_field_config=False  # 是否使用字段配置
):
```

#### 静态值集成
```python
# 准备静态值
static_values = {
    'articlelink': link,
    'content': content_text,
    'classid': classid,
    'city': city,
    'title': article_title,
    'dateget': article_date,
    'source': article_source
}

# 添加用户自定义字段
if user_custom_fields:
    for field_name, field_value in user_custom_fields.items():
        static_values[field_name] = field_value
```

## 🎯 使用方式

### 1. 打开自定义字段对话框
1. 进入"字段配置"标签页
2. 点击"自定义字段"按钮
3. 打开自定义字段对话框

### 2. 添加自定义字段

#### 方式1: 手动添加
1. 在"字段名"输入框中输入字段名（如：author）
2. 在"字段值"输入框中输入字段值（如：管理员）
3. 点击"+ 添加字段"按钮添加更多字段

#### 方式2: 使用预设
1. 在"常用字段快速添加"区域点击预设按钮
2. 系统会自动添加对应的字段名和默认值
3. 可以修改默认值以适应具体需求

### 3. 管理字段
- **删除字段**: 点击字段行右侧的"删除"按钮
- **清空所有**: 点击"清空所有"按钮删除所有字段
- **修改值**: 直接在输入框中修改字段值

### 4. 应用字段
1. 配置完成后点击"确定"按钮
2. 字段会自动保存到配置中
3. 开始爬取时这些字段会自动添加到每条数据中

## 📊 数据效果

### 爬取前的数据结构
```csv
title,content,dateget,source,articlelink
文章标题1,文章内容1,2025-01-01,来源1,http://example.com/1
文章标题2,文章内容2,2025-01-02,来源2,http://example.com/2
```

### 添加自定义字段后的数据结构
```csv
title,content,dateget,source,articlelink,author,category,status
文章标题1,文章内容1,2025-01-01,来源1,http://example.com/1,管理员,新闻,已发布
文章标题2,文章内容2,2025-01-02,来源2,http://example.com/2,管理员,新闻,已发布
```

## 🔧 配置示例

### 自定义字段配置示例
```json
{
  "custom_fields": {
    "author": "系统管理员",
    "department": "信息中心",
    "category": "政务公告",
    "priority": "重要",
    "status": "已审核",
    "source_type": "官方发布",
    "tags": "政策,公告,重要",
    "reviewer": "审核员张三"
  }
}
```

### 实际应用场景
1. **政府网站爬取**:
   ```
   department: "市政府办公室"
   category: "政务公告"
   level: "重要"
   ```

2. **新闻网站爬取**:
   ```
   source_type: "官方媒体"
   category: "时政新闻"
   editor: "编辑部"
   ```

3. **企业网站爬取**:
   ```
   company: "某某集团"
   type: "企业动态"
   industry: "制造业"
   ```

## 🎯 应用场景

### 1. 数据标记和分类
- **来源标记**: 标记数据来源（官方、媒体、企业等）
- **分类标记**: 标记内容分类（新闻、公告、政策等）
- **重要性标记**: 标记内容重要程度（重要、一般、紧急等）

### 2. 数据管理和追踪
- **责任人标记**: 标记数据采集人员或负责人
- **部门标记**: 标记数据所属部门或机构
- **状态标记**: 标记数据处理状态（待审核、已发布等）

### 3. 数据分析和统计
- **时间标记**: 标记数据采集时间或处理时间
- **版本标记**: 标记数据版本或批次信息
- **质量标记**: 标记数据质量等级

## 💡 最佳实践

### 1. 字段命名规范
- 使用英文字段名，便于数据处理
- 使用下划线分隔单词（如：source_type）
- 避免使用特殊字符和空格

### 2. 字段值设计
- 使用简洁明确的值
- 保持值的一致性（如统一使用"已发布"而不是"发布"）
- 考虑后续数据分析的需求

### 3. 预设字段使用
- 优先使用预设字段，减少输入错误
- 根据实际需求修改预设值
- 建立团队内部的字段标准

### 4. 字段管理
- 定期检查和清理不需要的字段
- 建立字段使用文档
- 在团队中统一字段标准

## 🚨 注意事项

### 1. 字段名冲突
- 避免与系统字段重名（如title, content等）
- 如果重名，自定义字段会覆盖系统字段
- 建议使用前缀区分（如custom_author）

### 2. 数据类型
- 自定义字段的值都是字符串类型
- 如需数值计算，需要在后续处理中转换
- 布尔值建议使用"是/否"或"true/false"

### 3. 性能考虑
- 过多的自定义字段可能影响爬取性能
- 建议控制在10个字段以内
- 字段值不宜过长

### 4. 数据一致性
- 确保同一批次数据使用相同的自定义字段
- 避免在爬取过程中修改字段配置
- 建议在开始爬取前确认所有字段配置

## 🎉 功能优势

### 1. 灵活性
- 用户可以根据具体需求自定义字段
- 支持任意字段名和值的组合
- 无需修改代码即可扩展数据结构

### 2. 易用性
- 图形化界面，操作简单直观
- 提供预设字段，快速配置
- 支持批量添加和管理

### 3. 集成性
- 与现有字段系统无缝集成
- 自动添加到爬取数据中
- 支持各种导出格式

### 4. 扩展性
- 可以轻松添加新的预设字段
- 支持复杂的字段配置需求
- 为未来功能扩展预留接口

## 🎯 总结

自定义字段功能成功实现了用户自定义字段名和值的需求：

✅ **完整的GUI界面**: 提供直观的字段配置对话框
✅ **预设字段支持**: 快速添加常用字段
✅ **灵活的字段管理**: 支持添加、删除、修改字段
✅ **完整的数据流集成**: 从GUI到爬虫的完整数据传递
✅ **自动数据添加**: 自定义字段自动添加到每条爬取数据中

现在用户可以像使用classid字段一样，自由添加任意的自定义字段，大大提高了数据采集的灵活性和实用性。
