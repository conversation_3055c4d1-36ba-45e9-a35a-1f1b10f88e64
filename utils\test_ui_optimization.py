#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI布局优化测试脚本
用于验证界面优化效果
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QGroupBox, QComboBox, QSpinBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.utils import get_application_stylesheet

class UITestWindow(QMainWindow):
    """UI测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("UI布局优化测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置字体和样式
        font = QFont("Microsoft YaHei", 10)
        self.setFont(font)
        self.setStyleSheet(get_application_stylesheet())
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(12)
        main_layout.setContentsMargins(12, 12, 12, 12)
        central_widget.setLayout(main_layout)
        
        # 测试配置管理组
        self.create_config_test_group(main_layout)
        
        # 测试URL设置组
        self.create_url_test_group(main_layout)
        
        # 测试选择器组
        self.create_selector_test_group(main_layout)
        
        # 测试按钮组
        self.create_button_test_group(main_layout)
    
    def create_config_test_group(self, parent_layout):
        """创建配置测试组"""
        group = QGroupBox("配置管理测试")
        layout = QVBoxLayout()
        layout.setSpacing(6)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # 配置选择区域
        config_layout = QHBoxLayout()
        config_layout.setSpacing(8)
        
        combo1 = QComboBox()
        combo1.addItems(["分类1", "分类2", "分类3"])
        combo1.setMinimumWidth(90)
        config_layout.addWidget(combo1)
        
        combo2 = QComboBox()
        combo2.addItems(["子分类1", "子分类2"])
        combo2.setMinimumWidth(90)
        config_layout.addWidget(combo2)
        
        combo3 = QComboBox()
        combo3.addItems(["配置1", "配置2"])
        combo3.setMinimumWidth(110)
        config_layout.addWidget(combo3)
        
        config_layout.addStretch()
        layout.addLayout(config_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(6)
        
        ai_btn = QPushButton("🤖 AI")
        ai_btn.setObjectName("aiButton")
        ai_btn.setFixedSize(70, 32)
        button_layout.addWidget(ai_btn)
        
        save_btn = QPushButton("💾")
        save_btn.setFixedSize(50, 32)
        button_layout.addWidget(save_btn)
        
        new_btn = QPushButton("➕")
        new_btn.setFixedSize(50, 32)
        button_layout.addWidget(new_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        group.setLayout(layout)
        parent_layout.addWidget(group)
    
    def create_url_test_group(self, parent_layout):
        """创建URL测试组"""
        group = QGroupBox("URL设置测试")
        layout = QVBoxLayout()
        layout.setSpacing(6)
        
        # URL输入
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("目标URL:"))
        url_edit = QLineEdit()
        url_edit.setPlaceholderText("https://example.com/news")
        url_layout.addWidget(url_edit)
        layout.addLayout(url_layout)
        
        # 控制参数
        control_layout = QHBoxLayout()
        control_layout.setSpacing(8)
        
        control_layout.addWidget(QLabel("起始页:"))
        start_edit = QLineEdit()
        start_edit.setText("1")
        start_edit.setFixedWidth(50)
        control_layout.addWidget(start_edit)
        
        control_layout.addWidget(QLabel("至"))
        
        max_edit = QLineEdit()
        max_edit.setPlaceholderText("5")
        max_edit.setFixedWidth(50)
        control_layout.addWidget(max_edit)
        
        control_layout.addWidget(QLabel("页"))
        
        control_layout.addWidget(QLabel("URL模式:"))
        mode_combo = QComboBox()
        mode_combo.addItems(["绝对", "相对"])
        mode_combo.setFixedWidth(70)
        control_layout.addWidget(mode_combo)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        group.setLayout(layout)
        parent_layout.addWidget(group)
    
    def create_selector_test_group(self, parent_layout):
        """创建选择器测试组"""
        group = QGroupBox("选择器测试")
        layout = QVBoxLayout()
        layout.setSpacing(6)
        
        # 选择器输入
        selectors = [
            ("列表容器:", ".main"),
            ("文章链接:", ".clearfix li a"),
            ("标题:", "h1, .title"),
            ("内容:", ".article_cont")
        ]
        
        for label_text, placeholder in selectors:
            row_layout = QHBoxLayout()
            label = QLabel(label_text)
            label.setMinimumWidth(80)
            row_layout.addWidget(label)
            
            edit = QLineEdit()
            edit.setPlaceholderText(placeholder)
            row_layout.addWidget(edit)
            
            layout.addLayout(row_layout)
        
        group.setLayout(layout)
        parent_layout.addWidget(group)
    
    def create_button_test_group(self, parent_layout):
        """创建按钮测试组"""
        group = QGroupBox("按钮样式测试")
        layout = QHBoxLayout()
        layout.setSpacing(8)
        
        # 不同类型的按钮
        start_btn = QPushButton("开始爬取")
        start_btn.setObjectName("startButton")
        layout.addWidget(start_btn)
        
        stop_btn = QPushButton("停止爬取")
        stop_btn.setObjectName("stopButton")
        layout.addWidget(stop_btn)
        
        test_btn = QPushButton("🧪 测试选择器")
        test_btn.setFixedSize(110, 32)
        layout.addWidget(test_btn)
        
        # 数字输入框测试
        layout.addWidget(QLabel("并发数:"))
        spin_box = QSpinBox()
        spin_box.setRange(1, 20)
        spin_box.setValue(5)
        layout.addWidget(spin_box)
        
        layout.addStretch()
        
        group.setLayout(layout)
        parent_layout.addWidget(group)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("UI优化测试")
    app.setApplicationVersion("1.0")
    
    window = UITestWindow()
    window.show()
    
    print("UI优化测试窗口已启动")
    print("主要改进:")
    print("- 统一使用微软雅黑字体")
    print("- 优化控件间距和尺寸")
    print("- 现代化的颜色方案")
    print("- 更紧凑的布局设计")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
