# modules package
from .manager import <PERSON><PERSON>leMana<PERSON>, module_manager, get_config_for_url, match_module_for_url
from .config_manager import ModuleConfigManager

# 为GUI兼容性添加便捷函数
def list_modules():
    """获取所有模组名称列表 - GUI兼容函数"""
    return module_manager.list_modules()

def get_module_info(module_name):
    """获取模组信息 - GUI兼容函数"""
    return module_manager.get_module_info(module_name)

def add_module(name, config):
    """添加模组 - GUI兼容函数"""
    return module_manager.add_module(name, config)

def update_module(name, config):
    """更新模组 - GUI兼容函数"""
    return module_manager.update_module(name, config)

def delete_module(name):
    """删除模组 - GUI兼容函数"""
    return module_manager.delete_module(name)

def save_modules():
    """保存模组配置 - GUI兼容函数"""
    return module_manager.save_modules()

# 导出主要接口
__all__ = [
    'ModuleManager',
    'module_manager',
    'ModuleConfigManager',
    'get_config_for_url',
    'match_module_for_url',
    'list_modules',
    'get_module_info',
    'add_module',
    'update_module',
    'delete_module',
    'save_modules'
]
