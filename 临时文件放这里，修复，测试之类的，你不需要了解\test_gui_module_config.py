#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI模组配置功能测试脚本
测试GUI中的模组配置功能是否正常工作
"""

import sys
import os

def test_gui_imports():
    """测试GUI导入"""
    print("="*60)
    print("测试GUI导入")
    print("="*60)
    
    try:
        from crawler_gui_new import CrawlerGUI, ModuleEditDialog, UrlTestDialog
        print("✅ GUI主要类导入成功")
        return True
    except ImportError as e:
        print(f"❌ GUI导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI导入异常: {e}")
        return False

def test_module_manager_integration():
    """测试模组管理器集成"""
    print("\n" + "="*60)
    print("测试模组管理器集成")
    print("="*60)
    
    try:
        from crawler_gui_new import MODULE_MANAGER_AVAILABLE
        if MODULE_MANAGER_AVAILABLE:
            print("✅ 模组管理器可用")
            
            # 测试模组管理器功能
            from module_manager import module_manager
            modules = module_manager.list_modules()
            print(f"✅ 找到 {len(modules)} 个模组: {modules}")
            
            # 测试URL匹配
            test_url = "https://mp.weixin.qq.com/s/test"
            module_name = module_manager.match_url(test_url)
            print(f"✅ URL匹配测试: {test_url} -> {module_name}")
            
            return True
        else:
            print("⚠️ 模组管理器不可用")
            return False
            
    except Exception as e:
        print(f"❌ 模组管理器集成测试失败: {e}")
        return False

def test_failed_url_processor():
    """测试失败URL处理器"""
    print("\n" + "="*60)
    print("测试失败URL处理器")
    print("="*60)
    
    try:
        from failed_url_processor import FailedUrlProcessor
        processor = FailedUrlProcessor()
        print("✅ 失败URL处理器创建成功")
        
        # 检查是否有失败文件
        articles_dir = "articles"
        if os.path.exists(articles_dir):
            failed_files = [f for f in os.listdir(articles_dir) if f.endswith("_failed.csv")]
            print(f"✅ 找到 {len(failed_files)} 个失败文件")
            
            if failed_files:
                test_file = os.path.join(articles_dir, failed_files[0])
                failed_urls = processor.load_failed_urls(test_file)
                print(f"✅ 测试文件 {test_file} 包含 {len(failed_urls)} 个失败URL")
        
        return True
        
    except ImportError as e:
        print(f"❌ 失败URL处理器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 失败URL处理器测试失败: {e}")
        return False

def test_gui_module_tab():
    """测试GUI模组标签页"""
    print("\n" + "="*60)
    print("测试GUI模组标签页")
    print("="*60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from crawler_gui_new import CrawlerGUI
        
        # 创建应用（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建GUI实例
        gui = CrawlerGUI()
        
        # 检查模组标签页是否存在
        tab_count = gui.tab_widget.count()
        tab_names = [gui.tab_widget.tabText(i) for i in range(tab_count)]
        
        print(f"✅ GUI标签页数量: {tab_count}")
        print(f"✅ 标签页名称: {tab_names}")
        
        if "模组配置" in tab_names:
            print("✅ 模组配置标签页存在")
            
            # 检查模组配置相关控件
            if hasattr(gui, 'module_enabled'):
                print("✅ 模组开关控件存在")
            if hasattr(gui, 'module_list'):
                print("✅ 模组列表控件存在")
            if hasattr(gui, 'failed_file_path'):
                print("✅ 失败文件路径控件存在")
            
            return True
        else:
            print("❌ 模组配置标签页不存在")
            return False
            
    except Exception as e:
        print(f"❌ GUI模组标签页测试失败: {e}")
        return False

def test_config_integration():
    """测试配置集成"""
    print("\n" + "="*60)
    print("测试配置集成")
    print("="*60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from crawler_gui_new import CrawlerGUI
        
        # 创建应用（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建GUI实例
        gui = CrawlerGUI()
        
        # 测试配置获取
        config = gui.get_config_from_gui()
        
        if 'module_config' in config:
            print("✅ 配置中包含模组配置")
            module_config = config['module_config']
            print(f"✅ 模组配置内容: {module_config}")
            return True
        else:
            print("❌ 配置中缺少模组配置")
            return False
            
    except Exception as e:
        print(f"❌ 配置集成测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("GUI模组配置功能测试")
    print("="*60)
    
    tests = [
        ("GUI导入测试", test_gui_imports),
        ("模组管理器集成测试", test_module_manager_integration),
        ("失败URL处理器测试", test_failed_url_processor),
        ("GUI模组标签页测试", test_gui_module_tab),
        ("配置集成测试", test_config_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI模组配置功能正常。")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return passed == total

def demo_gui_usage():
    """演示GUI使用方法"""
    print("\n" + "="*60)
    print("GUI模组配置使用方法")
    print("="*60)
    
    print("1. 启动GUI:")
    print("   python crawler_gui_new.py")
    print()
    
    print("2. 使用模组配置:")
    print("   - 点击'模组配置'标签页")
    print("   - 启用'启用模组配置系统'开关")
    print("   - 查看和管理可用模组")
    print("   - 添加新的模组配置")
    print()
    
    print("3. 处理失败URL:")
    print("   - 在'失败URL处理'组中选择失败文件")
    print("   - 设置重试参数")
    print("   - 点击'处理失败URL'按钮")
    print()
    
    print("4. 测试URL匹配:")
    print("   - 点击'测试URL匹配'按钮")
    print("   - 输入要测试的URL")
    print("   - 查看匹配结果和配置详情")
    print()
    
    print("5. 自动配置应用:")
    print("   - 启用模组配置后，爬虫会自动根据URL选择配置")
    print("   - 微信公众号URL会自动使用微信模组配置")
    print("   - 其他URL会根据匹配规则选择合适配置")

if __name__ == "__main__":
    # 运行所有测试
    success = run_all_tests()
    
    # 显示使用方法
    demo_gui_usage()
    
    # 退出码
    exit(0 if success else 1)
