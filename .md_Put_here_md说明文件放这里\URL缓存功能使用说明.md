# 🚀 URL缓存功能使用说明

## 📋 功能概述

新的URL缓存功能专门解决您提出的需求：**避免重复翻页收集URL的问题**。

### 🎯 解决的问题
- 同一个列表网址多次爬虫时，每次都要重复翻页收集URL
- 翻页收集URL是耗时的步骤，重复执行浪费时间
- 需要一个开关来选择是否跳过翻页收集步骤

### ✨ 功能特点
1. **自动保存翻页收集的URL** - 第一次翻页收集后自动保存到CSV文件
2. **智能缓存检测** - 第二次及以后可以检测是否存在缓存
3. **可选跳过翻页** - 提供开关选择是否跳过翻页直接使用缓存
4. **配置组管理** - 按配置组分类管理缓存文件
5. **文件自动命名** - 根据列表页URL自动生成唯一文件名

## 🏗️ 文件结构

```
url_cache/                          # URL缓存文件夹
├── default/                        # 默认配置组
│   └── *.csv                      # 缓存的URL文件
├── 政府新闻/                       # 自定义配置组
│   └── *.csv                      # 缓存的URL文件
├── 新浪新闻/                       # 自定义配置组
│   └── *.csv                      # 缓存的URL文件
└── [其他配置组]/                   # 其他配置组
    └── *.csv                      # 缓存的URL文件
```

## 🔧 核心功能

### 1. 自动保存收集的URL

**触发时机：** 传统翻页收集完成后自动执行

**文件命名规则：**
```
格式: {domain}_{path}_collected_urls.csv
示例: www_gov_cn_news_list_html_collected_urls.csv
```

**文件内容格式：**
```csv
url,collected_time
https://www.gov.cn/news/article1.html,2024-01-15 10:30:25
https://www.gov.cn/news/article2.html,2024-01-15 10:30:25
https://www.gov.cn/news/article3.html,2024-01-15 10:30:25
```

### 2. 智能缓存检测

**函数：** `check_url_cache_exists(input_url, config_group=None)`

**功能：**
- 检查指定列表页URL是否存在对应的缓存文件
- 返回文件存在状态、路径和详细信息
- 包含文件大小、修改时间等元信息

### 3. 从缓存加载URL

**函数：** `load_cached_urls_from_csv(input_url, config_group=None, log_callback=None)`

**功能：**
- 根据列表页URL自动找到对应的缓存文件
- 读取并返回URL列表
- 完整的错误处理和日志记录

### 4. 跳过翻页选项

**新增参数：** `skip_pagination_if_cached=False`

**使用位置：** `crawl_articles_async` 函数

**工作流程：**
1. 检查是否启用跳过选项
2. 检查是否存在对应的缓存文件
3. 如果存在缓存，直接加载URL跳过翻页
4. 如果不存在缓存，执行正常翻页收集

## 🚀 使用方式

### 1. 第一次爬取（建立缓存）

```python
from core.crawler import crawl_articles_async

# 第一次爬取，会自动保存收集的URL到缓存
result = await crawl_articles_async(
    input_url="https://www.example.com/news/list.html",
    config_group="政府新闻",  # 配置组名称
    skip_pagination_if_cached=False,  # 第一次不跳过
    # ... 其他参数
)
```

### 2. 第二次及以后爬取（使用缓存）

```python
# 第二次及以后的爬取，可以选择跳过翻页
result = await crawl_articles_async(
    input_url="https://www.example.com/news/list.html",
    config_group="政府新闻",  # 同样的配置组
    skip_pagination_if_cached=True,  # 启用跳过翻页
    # ... 其他参数
)
```

### 3. 手动检查缓存状态

```python
from core.crawler import check_url_cache_exists

# 检查是否存在缓存
exists, filepath, info = check_url_cache_exists(
    "https://www.example.com/news/list.html",
    "政府新闻"
)

if exists:
    print(f"找到缓存文件: {info['filename']}")
    print(f"修改时间: {info['modified_str']}")
    print(f"文件大小: {info['size']} bytes")
else:
    print("未找到缓存文件")
```

### 4. 手动加载缓存URL

```python
from core.crawler import load_cached_urls_from_csv

# 手动加载缓存的URL
urls = load_cached_urls_from_csv(
    "https://www.example.com/news/list.html",
    "政府新闻",
    log_callback=print
)

print(f"加载了 {len(urls)} 个URL")
```

## 💡 使用场景

### 1. 定期更新爬取
- **第一次**: 完整翻页收集所有URL并建立缓存
- **后续更新**: 跳过翻页直接使用缓存，快速爬取内容
- **适用**: 定期更新、内容变化不大的网站

### 2. 测试和调试
- **开发阶段**: 使用缓存避免重复翻页，加快测试速度
- **参数调优**: 在相同URL集合上测试不同的爬取参数
- **适用**: 开发测试环境

### 3. 分阶段处理
- **第一阶段**: 专门收集URL并缓存
- **第二阶段**: 专门处理内容爬取
- **适用**: 大规模爬取任务的分工处理

## ⚙️ 配置选项

### skip_pagination_if_cached 参数
- **默认值**: `False`
- **作用**: 控制是否在有缓存时跳过翻页收集
- **建议**: 第一次设为 `False`，后续设为 `True`

### config_group 参数
- **默认值**: `None` (使用 "default" 组)
- **作用**: 指定配置组名称，用于组织缓存文件
- **建议**: 按网站或项目名称分组

## 🔍 日志信息

启用URL缓存功能后，您会看到以下日志信息：

```
🔍 发现URL缓存文件: www_example_com_news_list_html_collected_urls.csv (修改时间: 2024-01-15 10:30:25)
⚡ 跳过翻页收集步骤，直接从缓存加载URL...
📄 从缓存加载了 150 个URL: www_example_com_news_list_html_collected_urls.csv
✅ 从缓存加载了 150 个URL，跳过翻页收集
```

或者在建立缓存时：

```
💾 已保存收集的URL到: url_cache/政府新闻/www_example_com_news_list_html_collected_urls.csv (150 个链接)
```

## 🎯 总结

这个URL缓存功能完美解决了您提出的需求：

✅ **避免重复翻页** - 第二次及以后可以跳过翻页收集步骤
✅ **自动保存URL** - 第一次翻页后自动保存到CSV文件
✅ **智能检测缓存** - 自动检测是否存在对应的缓存文件
✅ **灵活控制** - 提供开关选择是否使用缓存
✅ **配置组管理** - 按项目分类管理缓存文件
✅ **完整日志** - 详细的操作日志便于监控

现在您可以高效地处理重复的爬取任务，大大节省翻页收集URL的时间！
