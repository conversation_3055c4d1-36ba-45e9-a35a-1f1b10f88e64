#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字段配置开关功能
验证开关是否能正确控制字段配置功能的启用/禁用
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_switch_creation():
    """测试开关控件创建"""
    print("=" * 60)
    print("测试字段配置开关控件创建")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 检查开关控件
        has_switch = hasattr(window, 'enable_field_config_checkbox')
        has_status_label = hasattr(window, 'field_config_status_label')
        has_config_area = hasattr(window, 'field_config_area')
        
        print(f"📋 字段配置开关: {'✅' if has_switch else '❌'}")
        print(f"📋 状态指示器: {'✅' if has_status_label else '❌'}")
        print(f"📋 配置区域: {'✅' if has_config_area else '❌'}")
        
        if has_switch:
            # 检查初始状态
            initial_state = window.enable_field_config_checkbox.isChecked()
            print(f"🔧 初始状态: {'启用' if initial_state else '禁用'}")
            
            # 检查配置区域初始状态
            if has_config_area:
                area_enabled = window.field_config_area.isEnabled()
                print(f"🔧 配置区域初始状态: {'启用' if area_enabled else '禁用'}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_switch_functionality():
    """测试开关功能"""
    print("=" * 60)
    print("测试字段配置开关功能")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        if not hasattr(window, 'enable_field_config_checkbox'):
            print("❌ 开关控件不存在")
            return False
        
        # 测试启用功能
        print("\n🔧 测试启用字段配置...")
        window.enable_field_config_checkbox.setChecked(True)
        
        # 检查相关组件状态
        components = [
            ('field_config_area', '配置区域'),
            ('custom_fields_group', '自定义字段组'),
            ('field_preview_group', '字段预览组'),
            ('field_actions_group', '字段操作组')
        ]
        
        enabled_count = 0
        for attr_name, display_name in components:
            if hasattr(window, attr_name):
                component = getattr(window, attr_name)
                is_enabled = component.isEnabled()
                print(f"   {display_name}: {'✅ 启用' if is_enabled else '❌ 禁用'}")
                if is_enabled:
                    enabled_count += 1
            else:
                print(f"   {display_name}: ⚠️ 不存在")
        
        print(f"   启用组件数量: {enabled_count}/{len(components)}")
        
        # 测试禁用功能
        print("\n🔧 测试禁用字段配置...")
        window.enable_field_config_checkbox.setChecked(False)
        
        disabled_count = 0
        for attr_name, display_name in components:
            if hasattr(window, attr_name):
                component = getattr(window, attr_name)
                is_enabled = component.isEnabled()
                print(f"   {display_name}: {'⚠️ 仍启用' if is_enabled else '✅ 已禁用'}")
                if not is_enabled:
                    disabled_count += 1
            else:
                print(f"   {display_name}: ⚠️ 不存在")
        
        print(f"   禁用组件数量: {disabled_count}/{len(components)}")
        
        # 检查状态指示器
        if hasattr(window, 'field_config_status_label'):
            status_text = window.field_config_status_label.text()
            print(f"📊 状态指示器: {status_text}")
        
        return enabled_count > 0 and disabled_count > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_retrieval():
    """测试配置获取"""
    print("=" * 60)
    print("测试字段配置获取")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 测试禁用状态下的配置获取
        print("\n🔧 测试禁用状态下的配置获取...")
        if hasattr(window, 'enable_field_config_checkbox'):
            window.enable_field_config_checkbox.setChecked(False)
        
        config_disabled = window.get_field_config_from_gui()
        print(f"   禁用状态配置: {config_disabled}")
        
        expected_disabled = {
            'use_field_config': False,
            'field_preset': '',
            'custom_field_list': []
        }
        
        disabled_correct = (config_disabled.get('use_field_config') == False)
        print(f"   禁用状态正确: {'✅' if disabled_correct else '❌'}")
        
        # 测试启用状态下的配置获取
        print("\n🔧 测试启用状态下的配置获取...")
        if hasattr(window, 'enable_field_config_checkbox'):
            window.enable_field_config_checkbox.setChecked(True)
        
        config_enabled = window.get_field_config_from_gui()
        print(f"   启用状态配置: {config_enabled}")
        
        enabled_correct = (config_enabled.get('use_field_config') == True)
        print(f"   启用状态正确: {'✅' if enabled_correct else '❌'}")
        
        return disabled_correct and enabled_correct
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("=" * 60)
    print("测试GUI集成")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 检查事件处理方法
        has_switch_handler = hasattr(window, 'on_field_config_enabled_changed')
        has_config_getter = hasattr(window, 'get_field_config_from_gui')
        
        print(f"🔧 开关事件处理方法: {'✅' if has_switch_handler else '❌'}")
        print(f"🔧 配置获取方法: {'✅' if has_config_getter else '❌'}")
        
        # 测试完整的配置获取流程
        if has_config_getter:
            try:
                full_config = window.get_config_from_gui()
                field_config = full_config.get('field_config', {})
                print(f"📊 完整配置中的字段配置: {field_config}")
                
                has_field_config = 'field_config' in full_config
                print(f"✅ 字段配置集成到完整配置: {'✅' if has_field_config else '❌'}")
                
                return has_field_config
            except Exception as e:
                print(f"⚠️ 配置获取测试失败: {e}")
                return False
        
        return has_switch_handler and has_config_getter
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试字段配置开关功能")
    print()
    
    tests = [
        ("开关控件创建测试", test_switch_creation),
        ("开关功能测试", test_switch_functionality),
        ("配置获取测试", test_config_retrieval),
        ("GUI集成测试", test_gui_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！字段配置开关功能正常。")
        print("\n💡 使用说明:")
        print("   1. 在字段配置标签页中找到'启用字段配置'复选框")
        print("   2. 勾选后可以使用字段预设和自定义字段功能")
        print("   3. 取消勾选则使用默认字段配置")
    else:
        print("⚠️ 部分测试失败，需要检查实现。")

if __name__ == "__main__":
    main()
