#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试get_page_url函数的修复结果
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.crawler import get_page_url

def test_get_page_url():
    """测试get_page_url函数"""
    print("🧪 测试get_page_url函数修复结果")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        {
            "name": "默认起始页数（从1开始）",
            "input_url": "https://example.com/news",
            "page_num": 1,
            "page_suffix": "index_{n}.html",
            "base_url": "https://example.com/news",
            "start_page": 1,
            "expected": "https://example.com/news"
        },
        {
            "name": "默认起始页数（第2页）",
            "input_url": "https://example.com/news",
            "page_num": 2,
            "page_suffix": "index_{n}.html",
            "base_url": "https://example.com/news",
            "start_page": 1,
            "expected": "https://example.com/news/index_2.html"
        },
        {
            "name": "自定义起始页数（从0开始）",
            "input_url": "https://example.com/news",
            "page_num": 0,
            "page_suffix": "page_{n}.html",
            "base_url": "https://example.com/news",
            "start_page": 0,
            "expected": "https://example.com/news"
        },
        {
            "name": "自定义起始页数（从0开始，第1页）",
            "input_url": "https://example.com/news",
            "page_num": 1,
            "page_suffix": "page_{n}.html",
            "base_url": "https://example.com/news",
            "start_page": 0,
            "expected": "https://example.com/news/page_1.html"
        },
        {
            "name": "自定义起始页数（从2开始）",
            "input_url": "https://example.com/news",
            "page_num": 2,
            "page_suffix": "list_{n}.html",
            "base_url": "https://example.com/news",
            "start_page": 2,
            "expected": "https://example.com/news"
        },
        {
            "name": "自定义起始页数（从2开始，第3页）",
            "input_url": "https://example.com/news",
            "page_num": 3,
            "page_suffix": "list_{n}.html",
            "base_url": "https://example.com/news",
            "start_page": 2,
            "expected": "https://example.com/news/list_3.html"
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"   输入URL: {test_case['input_url']}")
        print(f"   页码: {test_case['page_num']}")
        print(f"   起始页数: {test_case['start_page']}")
        print(f"   翻页后缀: {test_case['page_suffix']}")
        
        try:
            result = get_page_url(
                input_url=test_case['input_url'],
                page_num=test_case['page_num'],
                page_suffix=test_case['page_suffix'],
                base_url=test_case['base_url'],
                start_page=test_case['start_page']
            )
            
            print(f"   实际结果: {result}")
            print(f"   期望结果: {test_case['expected']}")
            
            if result == test_case['expected']:
                print("   ✅ 测试通过")
                success_count += 1
            else:
                print("   ❌ 测试失败")
                
        except Exception as e:
            print(f"   ❌ 测试出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！get_page_url函数修复成功")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    test_get_page_url()
