# 🏗️ 文章智能采集器项目架构技术文档

## ⚠️ 重要注意事项

**🔴 字段配置覆盖机制**: 参考模组配置逻辑，字段配置可覆盖默认选择器
**🔴 智能字段模块**: 统一开关控制字段配置、选择器编辑和AI正文识别
**🔴 用户自定义字段**: 支持类似classid的自定义字段名和值功能
**🔴 AI模块集成**: GUI已导入AI模块，支持智能正文识别
**🔴 配置文件位置**: 字段配置文件位于 `configs/fields/field_configs.json`

## 📋 项目概述

**项目名称**: 文章智能采集器 v3.0
**项目类型**: 智能网页爬虫系统
**技术栈**: Python + PyQt5 + Playwright + AI分析
**架构模式**: 模块化分层架构

### 核心功能
- 🤖 AI智能选择器分析
- 🔧 智能字段模块（统一开关控制）
- 🎯 字段配置覆盖机制
- 🔄 动态翻页处理
- 📦 模组化配置管理
- 💾 URL缓存功能
- 📊 Excel写入器类
- 🪟 GUI窗口状态记忆

## 🗂️ 项目架构

### 目录结构
```
crawler_project/
├── main.py                     # 主入口文件
├── core/                       # 核心爬虫功能
│   ├── crawler.py             # 主爬虫引擎（集成字段配置覆盖）
│   ├── field_config_manager.py # 字段配置管理器（新增）
│   ├── field_extractor.py     # 字段提取器
│   ├── excel_writer.py        # Excel写入器类
│   ├── PaginationHandler.py   # 分页处理器
│   ├── failed_url_processor.py # 失败URL处理
│   └── text_cleaner.py        # 文本清洗（优化后）
├── gui/                        # GUI界面模块
│   ├── main_window.py         # 主窗口（智能字段模块集成）
│   ├── window_state_manager.py # 窗口状态管理器
│   ├── config_manager.py      # GUI配置管理
│   ├── crawler_thread.py      # 爬虫线程管理
│   └── utils.py               # GUI工具函数
├── AI/                         # AI分析模块
│   ├── analyzer.py            # AI选择器分析器（集成测试功能）
│   ├── helper.py              # AI助手和配置
│   └── interactive.py         # 交互式AI工具
├── modules/                    # 模组管理系统
│   ├── manager.py             # 模组管理器
│   └── config_manager.py      # 模组配置管理
├── configs/                    # 配置文件目录
│   ├── app/                   # 应用配置
│   │   └── config.json        # 主配置文件
│   ├── fields/                # 字段配置（新增）
│   │   └── field_configs.json # 字段配置文件
│   └── modules/               # 模组配置
│       └── *.json             # 各网站模组配置
```

## 🔧 智能字段模块

### 字段配置管理器 (core/field_config_manager.py)
**主要类**: `FieldConfigManager`
**核心功能**:
- 字段配置文件管理 (configs/fields/field_configs.json)
- 字段预设管理（基础、社交媒体、电商、新闻、博客、综合）
- 可用字段获取（基础字段 + 扩展字段）
- 字段选择器配置
- 单例模式确保全局唯一实例

**支持的字段类型**:
- **基础字段**: title, content, dateget, source, articlelink, classid, city, getdate
- **扩展字段**: likes, views, comments, shares, price, sales, rating, tags, category, word_count, read_time, update_time, author, editor

### GUI智能字段模块
**统一开关控制**: "启用智能字段模块"开关统一控制字段配置和选择器编辑
**字段选择子标签页**:
- 字段预设选择（6种预设）
- 自定义字段选择（22个可选字段）
- 字段预览和操作

**选择器编辑子标签页**:
- 22个字段的选择器编辑器
- 实时选择器测试和验证

**AI正文识别功能**:
- 集成AI模块进行智能正文识别
- 自动分析文章页面结构
- 智能推荐最佳选择器

**自定义字段功能**:
- 用户可自定义字段名和值（类似classid）
- 预设字段快速添加
- 自定义字段自动添加到爬取数据中

### 字段配置覆盖机制
**设计理念**: 参考模组配置的成功逻辑
**覆盖优先级**:
1. **用户自定义字段**: 最高优先级，直接添加到数据中
2. **字段配置选择器**: 覆盖默认选择器（如果启用字段配置）
3. **模组配置选择器**: 网站特定配置覆盖
4. **默认选择器**: 最低优先级，兜底方案

**技术实现**:
- 在 `save_article_async` 函数中集成字段配置参数
- 使用直接变量覆盖方式（简单有效）
- 多点集成确保配置正确传递
- 与现有模组配置系统无缝兼容

## 📊 配置参数

### 字段配置参数
| 参数名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| use_field_config | bool | 启用字段配置 | False |
| field_preset | str | 字段预设名称 | "" |
| user_custom_fields | dict | 用户自定义字段字典 | {} |

**字段预设**: basic, social_media, ecommerce, news, blog, comprehensive
**扩展字段**: likes, views, comments, shares, price, sales, rating, tags, category, author, editor等

## 📝 配置文件格式

### 字段配置格式 (configs/fields/field_configs.json)
包含字段预设、可用字段和默认选择器配置。

### 模组配置格式 (module_configs.json)
```json
{
  "微信公众号": {
    "name": "微信公众号",
    "description": "微信公众号文章爬取配置",
    "domain_patterns": ["mp.weixin.qq.com"],
    "config": {
      "title_selectors": ["#activity-name", ".rich_media_title"],
      "content_selectors": ["#js_content"],
      "date_selectors": ["#publish_time"],
      "source_selectors": [".rich_media_meta_nickname"]
    }
  }
}
```

## 🔧 代码架构优化

### 智能字段模块架构
- **统一开关控制**：一个开关控制字段配置、选择器编辑和AI正文识别
- **配置覆盖机制**：参考模组配置逻辑，字段配置覆盖默认选择器
- **AI集成**：集成AI模块进行智能正文识别
- **用户自定义字段**：支持自定义字段名和值，类似classid使用方式

### 核心优化
- **函数统一化**：`save_article_async` 作为唯一文章保存函数
- **异步架构**：全面异步并发处理
- **代码简化**：删除冗余代码，提升维护性

## 📈 性能优化总结

### Excel写入性能
- **传统写入**: 20.29秒 (1000行)
- **批量写入**: 0.10秒 (1000行) - **177倍提升**
- **智能批量**: 2.17秒 (1000行) - **9倍提升**
- **混合策略**: 0.58秒 (1000行) - **30倍提升**

### 主要改进
- **智能字段模块**: 统一开关控制，字段配置覆盖机制
- **用户体验**: 窗口状态记忆，Excel模式选择，自定义字段支持
- **代码质量**: 模块化设计，架构统一

---

**文档版本**: v3.0
**最后更新**: 2025年7月
**重要更新**: 智能字段模块、字段配置覆盖机制、AI正文识别集成、用户自定义字段

---

*本文档为项目架构的技术总览，详细的API文档和使用说明请参考各模块的具体文档。*
