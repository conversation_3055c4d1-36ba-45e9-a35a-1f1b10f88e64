#!/usr/bin/env python3
"""
测试健壮的翻页功能
对比不同翻页模式的效果：智能模式、简单模式、标准模式
"""

import asyncio
import sys
import os
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.PaginationHandler import PaginationHandler
from core.crawler import launch_browser
from playwright.async_api import async_playwright

async def test_pagination_modes(url, max_pages=3):
    """测试不同翻页模式的效果"""
    
    print(f"🚀 开始测试健壮翻页功能")
    print(f"📍 测试URL: {url}")
    print(f"📄 最大页数: {max_pages}")
    
    results = {}
    
    # 测试配置
    extract_config = {
        'list_container_selector': '.main',
        'article_item_selector': 'li a, .list-item a, .news-item a, .article-item a',
        'title_selector': 'a',
        'save_dir': "翻页测试结果",
        'page_title': "翻页模式对比测试",
        'classid': 'pagination_test',
        'base_url': url.split('/')[0] + '//' + url.split('/')[2],
        'url_mode': 'relative'
    }
    
    # 测试模式列表
    test_modes = [
        {
            'name': '智能模式',
            'use_simple_pagination': False,
            'auto_detect_pagination': True,
            'description': '自动检测，失败时回退到简单模式'
        },
        {
            'name': '简单模式',
            'use_simple_pagination': True,
            'auto_detect_pagination': False,
            'description': '稳定的翻页逻辑，参考成功的测试脚本'
        },
        {
            'name': '标准模式',
            'use_simple_pagination': False,
            'auto_detect_pagination': True,
            'description': '完整的检测和等待机制'
        }
    ]
    
    async with async_playwright() as p:
        for i, mode in enumerate(test_modes):
            print(f"\n{'='*60}")
            print(f"🧪 测试 {i+1}/{len(test_modes)}: {mode['name']}")
            print(f"📝 描述: {mode['description']}")
            print(f"{'='*60}")
            
            try:
                # 启动浏览器
                browser, context, page = await launch_browser(p, headless=True)
                
                # 创建PaginationHandler实例
                handler = PaginationHandler(page)
                
                # 访问页面
                print(f"🌐 访问页面: {url}")
                await page.goto(url, wait_until='networkidle', timeout=30000)
                await page.wait_for_timeout(2000)
                
                # 执行翻页测试
                print(f"🔄 开始 {mode['name']} 翻页测试...")
                start_time = asyncio.get_event_loop().time()
                
                pages_processed = await handler.click_pagination(
                    next_button_selector='a:has-text("下一页")',
                    max_pages=max_pages,
                    wait_after_click=2000,
                    extract_articles_config=extract_config,
                    use_simple_pagination=mode['use_simple_pagination'],
                    auto_detect_pagination=mode['auto_detect_pagination']
                )
                
                end_time = asyncio.get_event_loop().time()
                duration = end_time - start_time
                
                # 获取收集的文章
                articles = handler.get_all_articles()
                
                # 记录结果
                results[mode['name']] = {
                    'pages_processed': pages_processed,
                    'articles_collected': len(articles),
                    'duration': round(duration, 2),
                    'success': pages_processed > 1,
                    'articles_per_page': round(len(articles) / max(pages_processed, 1), 1) if articles else 0
                }
                
                print(f"✅ {mode['name']} 完成:")
                print(f"   📄 处理页数: {pages_processed}")
                print(f"   📊 收集文章: {len(articles)} 篇")
                print(f"   ⏱️ 耗时: {duration:.2f} 秒")
                print(f"   📈 平均每页文章数: {results[mode['name']]['articles_per_page']}")
                
                if len(articles) > 0:
                    print(f"   📋 文章样例:")
                    for j, article in enumerate(articles[:3]):
                        title = article[0] if len(article) > 0 else "无标题"
                        print(f"      [{j+1}] {title[:50]}...")
                
                # 关闭浏览器
                await context.close()
                await browser.close()
                
            except Exception as e:
                print(f"❌ {mode['name']} 测试失败: {e}")
                results[mode['name']] = {
                    'pages_processed': 0,
                    'articles_collected': 0,
                    'duration': 0,
                    'success': False,
                    'error': str(e)
                }
                
                try:
                    await context.close()
                    await browser.close()
                except:
                    pass
    
    # 输出对比结果
    print(f"\n{'='*80}")
    print("🏆 翻页模式对比结果")
    print(f"{'='*80}")
    
    # 找出最佳模式
    best_mode = None
    best_score = 0
    
    for mode_name, result in results.items():
        if result['success']:
            # 计算综合得分：页数 * 2 + 文章数 * 0.1 - 耗时 * 0.1
            score = result['pages_processed'] * 2 + result['articles_collected'] * 0.1 - result['duration'] * 0.1
            if score > best_score:
                best_score = score
                best_mode = mode_name
        
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{mode_name:12} | {status:8} | {result['pages_processed']:3}页 | {result['articles_collected']:4}篇 | {result['duration']:6.2f}秒")
        
        if not result['success'] and 'error' in result:
            print(f"             | 错误: {result['error'][:50]}...")
    
    if best_mode:
        print(f"\n🎉 推荐使用: {best_mode}")
        print(f"   理由: 处理了 {results[best_mode]['pages_processed']} 页，收集了 {results[best_mode]['articles_collected']} 篇文章")
    else:
        print(f"\n⚠️ 所有模式均失败，建议检查网站结构或选择器配置")
    
    return results

async def test_specific_url():
    """测试特定URL"""
    # 可以在这里添加多个测试URL
    test_urls = [
        "https://www.tjszx.gov.cn/tagz/taxd/index.shtml",
        # 可以添加更多测试URL
    ]
    
    all_results = {}
    
    for url in test_urls:
        print(f"\n🌐 测试网站: {url}")
        try:
            results = await test_pagination_modes(url, max_pages=3)
            all_results[url] = results
        except Exception as e:
            print(f"❌ 测试 {url} 时出错: {e}")
            all_results[url] = {'error': str(e)}
    
    # 保存结果到文件
    with open('pagination_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 测试结果已保存到: pagination_test_results.json")
    return all_results

if __name__ == "__main__":
    print("健壮翻页功能测试工具")
    print("=" * 50)
    
    # 获取用户输入
    url = input("请输入要测试的URL (回车使用默认): ").strip()
    if not url:
        url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    max_pages_input = input("请输入最大测试页数 (回车使用默认3): ").strip()
    max_pages = int(max_pages_input) if max_pages_input.isdigit() else 3
    
    print(f"\n开始测试...")
    print(f"URL: {url}")
    print(f"最大页数: {max_pages}")
    
    # 运行测试
    results = asyncio.run(test_pagination_modes(url, max_pages))
    
    print(f"\n🎯 测试完成！")
    print(f"详细结果请查看上方输出或 pagination_test_results.json 文件")
