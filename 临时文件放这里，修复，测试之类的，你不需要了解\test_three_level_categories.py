#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试三级分类功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.manager import Confi<PERSON><PERSON><PERSON><PERSON>


def test_three_level_categories():
    """测试三级分类功能"""
    print("🧪 开始测试三级分类功能...")
    
    # 创建配置管理器
    config_manager = ConfigManager()
    
    # 测试获取父级分类
    print("\n📁 父级分类:")
    parent_categories = config_manager.get_parent_categories()
    for i, parent in enumerate(parent_categories, 1):
        print(f"  {i}. {parent}")
    
    # 测试获取次级分类
    print("\n📂 次级分类:")
    for parent in parent_categories:
        sub_categories = config_manager.get_sub_categories(parent)
        print(f"  {parent}:")
        for j, sub in enumerate(sub_categories, 1):
            print(f"    {j}. {sub}")
    
    # 测试获取子级分类
    print("\n📄 子级分类:")
    for parent in parent_categories:
        sub_categories = config_manager.get_sub_categories(parent)
        for sub in sub_categories:
            child_categories = config_manager.get_child_categories(parent, sub)
            print(f"  {parent}/{sub}:")
            for k, child in enumerate(child_categories, 1):
                print(f"    {k}. {child}")
    
    # 测试获取配置组
    print("\n⚙️ 各分类下的配置组:")
    for parent in parent_categories:
        sub_categories = config_manager.get_sub_categories(parent)
        for sub in sub_categories:
            child_categories = config_manager.get_child_categories(parent, sub)
            for child in child_categories:
                category_path = f"{parent}/{sub}/{child}"
                configs = config_manager.get_configs_by_category_path(category_path)
                if configs:
                    print(f"  {category_path}:")
                    for l, config in enumerate(configs, 1):
                        print(f"    {l}. {config}")
    
    # 测试添加新的三级分类
    print("\n➕ 测试添加新的三级分类...")
    success = config_manager.add_three_level_category("测试机构", "测试部门", "测试分类")
    if success:
        print("  ✅ 成功添加新的三级分类: 测试机构/测试部门/测试分类")
    else:
        print("  ❌ 添加新的三级分类失败")
    
    # 测试创建配置组到新分类
    print("\n🆕 测试创建配置组到新分类...")
    test_config = {
        "input_url": "https://test.example.com",
        "title_selectors": [".title"],
        "content_selectors": [".content"]
    }
    success = config_manager.add_group("测试配置", test_config, "测试机构/测试部门/测试分类")
    if success:
        print("  ✅ 成功创建配置组到新分类")
        
        # 验证配置组是否正确添加
        configs = config_manager.get_configs_by_category_path("测试机构/测试部门/测试分类")
        if "测试配置" in configs:
            print("  ✅ 配置组已正确添加到分类中")
        else:
            print("  ❌ 配置组未正确添加到分类中")
    else:
        print("  ❌ 创建配置组到新分类失败")
    
    # 测试移动配置组
    print("\n🔄 测试移动配置组...")
    success = config_manager.move_config_to_category_path("测试配置", "政府机构/人大系统/地方人大")
    if success:
        print("  ✅ 成功移动配置组")
        
        # 验证移动结果
        old_configs = config_manager.get_configs_by_category_path("测试机构/测试部门/测试分类")
        new_configs = config_manager.get_configs_by_category_path("政府机构/人大系统/地方人大")
        
        if "测试配置" not in old_configs and "测试配置" in new_configs:
            print("  ✅ 配置组移动验证成功")
        else:
            print("  ❌ 配置组移动验证失败")
    else:
        print("  ❌ 移动配置组失败")
    
    # 清理测试数据
    print("\n🧹 清理测试数据...")
    config_manager.delete_group("测试配置")
    print("  ✅ 测试数据清理完成")
    
    print("\n🎉 三级分类功能测试完成!")


if __name__ == "__main__":
    test_three_level_categories()
