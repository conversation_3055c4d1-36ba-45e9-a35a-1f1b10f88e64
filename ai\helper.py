#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版GUI AI配置助手模块
集成新的AI选择器分析器和模组管理器
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from PyQt5.QtCore import QThread, pyqtSignal, QObject
from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QTextEdit, QGroupBox, QGridLayout, QComboBox, QCheckBox

# 导入AI分析器
try:
    from ai.analyzer import AIAnalyzerWithTesting
    AI_ANALYZER_AVAILABLE = True
except ImportError:
    try:
        # 备用导入路径
        from analyzer import AIAnalyzerWithTesting
        AI_ANALYZER_AVAILABLE = True
    except ImportError:
        AI_ANALYZER_AVAILABLE = False
        print("警告: AI分析器不可用")

# 导入模组管理器
try:
    from modules.manager import module_manager
    MODULE_MANAGER_AVAILABLE = True
except ImportError:
    try:
        # 备用导入路径
        import modules.manager as module_manager_module
        module_manager = module_manager_module.module_manager
        MODULE_MANAGER_AVAILABLE = True
    except ImportError:
        MODULE_MANAGER_AVAILABLE = False
        print("警告: 模组管理器不可用")

# 配置日志
logger = logging.getLogger('GUIAIHelper')


class LLMConfigDialog(QDialog):
    """LLM API配置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("LLM API配置")
        self.setModal(True)
        self.resize(500, 400)
        
        self.init_ui()
        self.load_config()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        
        # API配置组
        api_group = QGroupBox("API配置")
        api_layout = QGridLayout()
        
        # API Key
        api_layout.addWidget(QLabel("API Key:"), 0, 0)
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setEchoMode(QLineEdit.Password)
        api_layout.addWidget(self.api_key_edit, 0, 1)
        
        # Base URL
        api_layout.addWidget(QLabel("Base URL:"), 1, 0)
        self.base_url_edit = QLineEdit()
        self.base_url_edit.setPlaceholderText("https://api.deepseek.com/v1")
        api_layout.addWidget(self.base_url_edit, 1, 1)
        
        # 模型名称
        api_layout.addWidget(QLabel("模型名称:"), 2, 0)
        self.model_combo = QComboBox()
        self.model_combo.addItems([
            "deepseek-chat",
            "gpt-3.5-turbo",
            "gpt-4",
            "claude-3-sonnet",
            "其他"
        ])
        api_layout.addWidget(self.model_combo, 2, 1)
        
        # 自定义模型名称
        api_layout.addWidget(QLabel("自定义模型:"), 3, 0)
        self.custom_model_edit = QLineEdit()
        self.custom_model_edit.setPlaceholderText("仅当选择'其他'时填写")
        api_layout.addWidget(self.custom_model_edit, 3, 1)
        
        api_group.setLayout(api_layout)
        layout.addWidget(api_group)
        
        # 高级配置组
        advanced_group = QGroupBox("高级配置")
        advanced_layout = QGridLayout()
        
        # Temperature
        advanced_layout.addWidget(QLabel("Temperature:"), 0, 0)
        self.temperature_edit = QLineEdit("0.3")
        advanced_layout.addWidget(self.temperature_edit, 0, 1)
        
        # Max Tokens
        advanced_layout.addWidget(QLabel("Max Tokens:"), 1, 0)
        self.max_tokens_edit = QLineEdit("1500")
        advanced_layout.addWidget(self.max_tokens_edit, 1, 1)
        
        # 启用AI分析
        self.enable_ai_checkbox = QCheckBox("启用AI智能分析")
        self.enable_ai_checkbox.setChecked(True)
        advanced_layout.addWidget(self.enable_ai_checkbox, 2, 0, 1, 2)
        
        advanced_group.setLayout(advanced_layout)
        layout.addWidget(advanced_group)
        
        # 测试区域
        test_group = QGroupBox("API测试")
        test_layout = QVBoxLayout()
        
        self.test_button = QPushButton("测试API连接")
        self.test_button.clicked.connect(self.test_api)
        test_layout.addWidget(self.test_button)
        
        self.test_result = QTextEdit()
        self.test_result.setMaximumHeight(100)
        self.test_result.setPlaceholderText("API测试结果将显示在这里...")
        test_layout.addWidget(self.test_result)
        
        test_group.setLayout(test_layout)
        layout.addWidget(test_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self.save_config)
        button_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def load_config(self):
        """加载配置"""
        try:
            import json
            import os
            
            config_file = "configs/ai/llm_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                self.api_key_edit.setText(config.get('api_key', ''))
                self.base_url_edit.setText(config.get('base_url', 'https://api.deepseek.com/v1'))
                
                model = config.get('model', 'deepseek-chat')
                index = self.model_combo.findText(model)
                if index >= 0:
                    self.model_combo.setCurrentIndex(index)
                else:
                    self.model_combo.setCurrentText('其他')
                    self.custom_model_edit.setText(model)
                
                self.temperature_edit.setText(str(config.get('temperature', 0.3)))
                self.max_tokens_edit.setText(str(config.get('max_tokens', 1500)))
                self.enable_ai_checkbox.setChecked(config.get('enable_ai', True))
        except Exception as e:
            logger.warning(f"加载LLM配置失败: {e}")
    
    def save_config(self):
        """保存配置"""
        try:
            import json
            
            # 获取模型名称
            if self.model_combo.currentText() == '其他':
                model = self.custom_model_edit.text().strip()
            else:
                model = self.model_combo.currentText()
            
            config = {
                'api_key': self.api_key_edit.text().strip(),
                'base_url': self.base_url_edit.text().strip() or 'https://api.deepseek.com/v1',
                'model': model,
                'temperature': float(self.temperature_edit.text() or 0.3),
                'max_tokens': int(self.max_tokens_edit.text() or 1500),
                'enable_ai': self.enable_ai_checkbox.isChecked()
            }
            
            with open("configs/ai/llm_config.json", 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            # 更新AI分析器配置
            self.update_ai_analyzer_config(config)
            
            self.accept()
        except Exception as e:
            self.test_result.setText(f"保存配置失败: {e}")
    
    def update_ai_analyzer_config(self, config):
        """更新AI分析器配置"""
        try:
            # 动态更新AI分析器的配置
            if AI_ANALYZER_AVAILABLE:
                from ai import analyzer
                # 更新AI配置到配置文件，让analyzer模块重新加载
                import json
                import os

                config_file = "configs/ai/llm_config.json"
                os.makedirs(os.path.dirname(config_file), exist_ok=True)

                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

                # 重新加载analyzer模块的配置
                if hasattr(analyzer, 'AI_CONFIG'):
                    analyzer.AI_CONFIG = config
                if hasattr(analyzer, 'client'):
                    from openai import OpenAI
                    analyzer.client = OpenAI(
                        api_key=config['api_key'],
                        base_url=config['base_url']
                    )

                logger.info("AI分析器配置已更新")
        except Exception as e:
            logger.warning(f"更新AI分析器配置失败: {e}")
    
    def test_api(self):
        """测试API连接"""
        self.test_button.setEnabled(False)
        self.test_result.setText("正在测试API连接...")
        
        try:
            from openai import OpenAI
            
            api_key = self.api_key_edit.text().strip()
            base_url = self.base_url_edit.text().strip() or 'https://api.deepseek.com/v1'
            model = self.model_combo.currentText()
            if model == '其他':
                model = self.custom_model_edit.text().strip()
            
            if not api_key:
                self.test_result.setText("❌ 请先填写API Key")
                return
            
            client = OpenAI(api_key=api_key, base_url=base_url)
            
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": "Hello, this is a test message."}],
                max_tokens=50
            )
            
            self.test_result.setText("✅ API连接测试成功！\n响应内容: " + response.choices[0].message.content)
            
        except Exception as e:
            self.test_result.setText(f"❌ API连接测试失败: {e}")
        finally:
            self.test_button.setEnabled(True)


class AIAnalysisThread(QThread):
    """AI分析线程"""
    
    analysis_finished = pyqtSignal(dict)
    analysis_progress = pyqtSignal(str)
    analysis_error = pyqtSignal(str)
    
    def __init__(self, url: str, analysis_type: str = "full", field_preset: str = None, custom_fields: List[str] = None):
        super().__init__()
        self.url = url
        self.analysis_type = analysis_type
        self.field_preset = field_preset
        self.custom_fields = custom_fields
        self.analyzer = None
    
    def run(self):
        """运行AI分析"""
        try:
            if not AI_ANALYZER_AVAILABLE:
                self.analysis_error.emit("AI分析器不可用")
                return
            
            self.analyzer = AIAnalyzerWithTesting()
            
            if self.analysis_type == "full":
                if self.field_preset or self.custom_fields:
                    self.analysis_progress.emit("开始字段配置增强分析...")
                    result = asyncio.run(self.analyzer.analyze_with_field_config(
                        self.url, self.field_preset, self.custom_fields
                    ))
                else:
                    self.analysis_progress.emit("开始完整分析...")
                    result = asyncio.run(self.analyzer.full_analysis_with_testing(self.url))
            elif self.analysis_type == "list":
                self.analysis_progress.emit("分析列表页...")
                result = asyncio.run(self.analyzer.analyze_list_page_selectors(self.url))
            elif self.analysis_type == "article":
                self.analysis_progress.emit("分析文章页...")
                result = asyncio.run(self.analyzer.analyze_article_page_selectors(self.url))
            else:
                self.analysis_error.emit("未知的分析类型")
                return
            
            self.analysis_finished.emit(result)
            
        except Exception as e:
            self.analysis_error.emit(f"AI分析失败: {str(e)}")


class EnhancedAIConfigManager:
    """增强版AI配置管理器"""
    
    def __init__(self):
        self.current_analysis_thread = None
        self._is_running = False
    
    def check_module_exists(self, url: str) -> Optional[str]:
        """检查是否已存在相同域名的模组配置"""
        if not MODULE_MANAGER_AVAILABLE:
            return None
        
        try:
            from urllib.parse import urlparse
            domain = urlparse(url).netloc
            
            # 获取所有模组
            if MODULE_MANAGER_AVAILABLE:
                modules = module_manager.list_modules()

                for module_name in modules:
                    module_info = module_manager.get_module_info(module_name)
                if module_info:
                    domain_patterns = module_info.get('domain_patterns', [])
                    for pattern in domain_patterns:
                        if domain in pattern or pattern in domain:
                            return module_name
            
            return None
        except Exception as e:
            logger.warning(f"检查模组配置失败: {e}")
            return None

    def is_running(self) -> bool:
        """检查AI分析是否正在运行"""
        return self._is_running or (self.current_analysis_thread and self.current_analysis_thread.isRunning())

    def start_ai_analysis(self, url: str, result_callback=None, error_callback=None, progress_callback=None) -> bool:
        """启动AI分析（兼容旧接口）"""
        return self.start_ai_analysis_new(url, "full", progress_callback, result_callback, error_callback)

    def start_ai_analysis_new(self, url: str, analysis_type: str = "full",
                         progress_callback=None, finished_callback=None, error_callback=None,
                         field_preset: str = None, custom_fields: List[str] = None) -> bool:
        """启动AI分析"""

        # 首先检查是否已有模组配置
        existing_module = self.check_module_exists(url)
        if existing_module:
            if error_callback:
                error_callback(f"域名已存在模组配置: {existing_module}，无需重复分析")
            return False

        # 检查LLM配置
        if not self.check_llm_config():
            if error_callback:
                error_callback("请先配置LLM API")
            return False

        # 停止之前的分析
        if self.current_analysis_thread and self.current_analysis_thread.isRunning():
            self.current_analysis_thread.terminate()
            self.current_analysis_thread.wait()

        # 设置运行状态
        self._is_running = True

        # 启动新的分析
        self.current_analysis_thread = AIAnalysisThread(url, analysis_type, field_preset, custom_fields)

        # 连接信号
        if progress_callback:
            self.current_analysis_thread.analysis_progress.connect(progress_callback)
        if finished_callback:
            def on_finished(result):
                self._is_running = False
                finished_callback(result)
            self.current_analysis_thread.analysis_finished.connect(on_finished)
        if error_callback:
            def on_error(error):
                self._is_running = False
                error_callback(error)
            self.current_analysis_thread.analysis_error.connect(on_error)

        self.current_analysis_thread.start()
        return True
    
    def check_llm_config(self) -> bool:
        """检查LLM配置是否完整"""
        try:
            import json
            import os
            
            config_file = "configs/ai/llm_config.json"
            if not os.path.exists(config_file):
                return False
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            return bool(config.get('api_key') and config.get('enable_ai', True))
        except:
            return False
    
    def apply_analysis_result_to_gui(self, result: Dict, gui_widgets: Dict):
        """将AI分析结果应用到GUI"""
        try:
            if not result.get('success'):
                return False
            
            final_config = result.get('final_config', {})
            
            # 应用列表页配置
            if 'list_container_selector' in final_config and 'list_container_selector' in gui_widgets:
                gui_widgets['list_container_selector'].setText(final_config['list_container_selector'] or '')
            
            if 'article_item_selector' in final_config and 'article_item_selector' in gui_widgets:
                gui_widgets['article_item_selector'].setText(final_config['article_item_selector'] or '')
            
            # 应用文章页配置
            selectors_map = {
                'title_selectors': 'title_selector',
                'content_selectors': 'content_selector',
                'date_selectors': 'date_selector',
                'source_selectors': 'source_selector'
            }
            
            for config_key, widget_key in selectors_map.items():
                if config_key in final_config and widget_key in gui_widgets:
                    selectors = final_config[config_key]
                    if selectors and len(selectors) > 0:
                        gui_widgets[widget_key].setText(selectors[0])
            
            return True
            
        except Exception as e:
            logger.error(f"应用AI分析结果失败: {e}")
            return False
    
    def save_analysis_result(self, result: Dict, config_name: str = None) -> bool:
        """保存AI分析结果到配置管理器和txt文件"""
        try:
            if not result.get('success'):
                return False

            from config.manager import ConfigManager
            from urllib.parse import urlparse
            import os
            import time

            config_manager = ConfigManager()
            final_config = result['final_config']

            # 获取域名
            domain = urlparse(result['list_url']).netloc

            # 生成第4级分类名称
            if not config_name:
                timestamp = result['timestamp'].replace(':', '').replace(' ', '_')
                config_name = f"AI分析_{domain}_{timestamp}"

            # 构建完整4级路径作为配置组名称
            category_path = f"政府机构/人大系统/AI分析/{config_name}"

            # 保存选择器到txt文件
            self._save_selectors_to_txt(final_config, category_path, domain)
            
            # 构建GUI配置格式
            gui_config = {
                'input_url': result['list_url'],
                'list_container_selector': final_config.get('list_container_selector', ''),
                'article_item_selector': final_config.get('article_item_selector', ''),
                'title_selector': final_config.get('title_selectors', [''])[0] if final_config.get('title_selectors') else '',
                'content_selector': final_config.get('content_selectors', [''])[0] if final_config.get('content_selectors') else '',
                'date_selector': final_config.get('date_selectors', [''])[0] if final_config.get('date_selectors') else '',
                'source_selector': final_config.get('source_selectors', [''])[0] if final_config.get('source_selectors') else '',
                'mode': 'safe',
                'headless': True,
                'file_format': 'CSV',
                'ai_confidence': final_config.get('confidence_score', 0.0)
            }
            
            # 保存到配置管理器（使用完整4级路径）
            success = config_manager.add_group(config_name, gui_config, category_path)

            if success:
                logger.info(f"AI分析结果已保存为配置组: {category_path}")

            return success

        except Exception as e:
            logger.error(f"保存AI分析结果失败: {e}")
            return False

    def _save_selectors_to_txt(self, final_config: Dict, category_path: str, domain: str):
        """保存选择器到txt文件"""
        try:
            import os
            import time

            # 创建选择器保存目录
            selectors_dir = "configs/ai/selectors"
            os.makedirs(selectors_dir, exist_ok=True)

            # 生成文件名：4级配置组+域名
            # 将路径中的"/"替换为"_"，避免文件名问题
            safe_category = category_path.replace("/", "_")
            safe_domain = domain.replace(".", "_")
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"{safe_category}_{safe_domain}_{timestamp}.txt"
            filepath = os.path.join(selectors_dir, filename)

            # 构建选择器内容
            content_lines = []
            content_lines.append(f"# AI分析选择器结果")
            content_lines.append(f"# 配置组路径: {category_path}")
            content_lines.append(f"# 域名: {domain}")
            content_lines.append(f"# 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            content_lines.append("")

            # 列表页选择器
            content_lines.append("## 列表页选择器")
            content_lines.append(f"list_container_selector = {final_config.get('list_container_selector', '')}")
            content_lines.append(f"article_item_selector = {final_config.get('article_item_selector', '')}")
            content_lines.append("")

            # 文章页选择器
            content_lines.append("## 文章页选择器")

            # 标题选择器（支持多个）
            title_selectors = final_config.get('title_selectors', [])
            if title_selectors:
                content_lines.append("# 标题选择器（多个可选）")
                for i, selector in enumerate(title_selectors, 1):
                    content_lines.append(f"title_selector_{i} = {selector}")
            else:
                content_lines.append("title_selector = ")
            content_lines.append("")

            # 内容选择器（支持多个）
            content_selectors = final_config.get('content_selectors', [])
            if content_selectors:
                content_lines.append("# 内容选择器（多个可选）")
                for i, selector in enumerate(content_selectors, 1):
                    content_lines.append(f"content_selector_{i} = {selector}")
            else:
                content_lines.append("content_selector = ")
            content_lines.append("")

            # 日期选择器（支持多个）
            date_selectors = final_config.get('date_selectors', [])
            if date_selectors:
                content_lines.append("# 日期选择器（多个可选）")
                for i, selector in enumerate(date_selectors, 1):
                    content_lines.append(f"date_selector_{i} = {selector}")
            else:
                content_lines.append("date_selector = ")
            content_lines.append("")

            # 来源选择器（支持多个）
            source_selectors = final_config.get('source_selectors', [])
            if source_selectors:
                content_lines.append("# 来源选择器（多个可选）")
                for i, selector in enumerate(source_selectors, 1):
                    content_lines.append(f"source_selector_{i} = {selector}")
            else:
                content_lines.append("source_selector = ")
            content_lines.append("")

            # 置信度信息
            confidence = final_config.get('confidence_score', 0.0)
            content_lines.append(f"# 置信度: {confidence:.2f}")
            content_lines.append("")

            # 使用说明
            content_lines.append("## 使用说明")
            content_lines.append("# 1. 每个字段可能有多个选择器，按优先级排序")
            content_lines.append("# 2. 爬虫会依次尝试这些选择器，直到成功提取内容")
            content_lines.append("# 3. 可以手动添加更多选择器以提高成功率")
            content_lines.append("# 4. 选择器格式为CSS选择器语法")

            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content_lines))

            logger.info(f"选择器已保存到文件: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"保存选择器到txt文件失败: {e}")
            return None

    def append_selectors_to_txt(self, final_config: Dict, category_path: str, domain: str, existing_file: str = None):
        """追加选择器到现有txt文件（支持同一域名的多个选择器）"""
        try:
            import os
            import time

            # 如果没有指定现有文件，查找同域名的文件
            if not existing_file:
                selectors_dir = "configs/ai/selectors"
                if os.path.exists(selectors_dir):
                    safe_domain = domain.replace(".", "_")
                    for filename in os.listdir(selectors_dir):
                        if safe_domain in filename and filename.endswith('.txt'):
                            existing_file = os.path.join(selectors_dir, filename)
                            break

            if existing_file and os.path.exists(existing_file):
                # 追加到现有文件
                with open(existing_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n\n# ===== 新增选择器 {time.strftime('%Y-%m-%d %H:%M:%S')} =====\n")
                    f.write(f"# 配置组路径: {category_path}\n")
                    f.write(f"# 置信度: {final_config.get('confidence_score', 0.0):.2f}\n\n")

                    # 添加新的选择器
                    if final_config.get('list_container_selector'):
                        f.write(f"list_container_selector_new = {final_config['list_container_selector']}\n")
                    if final_config.get('article_item_selector'):
                        f.write(f"article_item_selector_new = {final_config['article_item_selector']}\n")

                    # 文章页选择器
                    for field in ['title', 'content', 'date', 'source']:
                        selectors = final_config.get(f'{field}_selectors', [])
                        if selectors:
                            f.write(f"\n# {field}选择器\n")
                            for i, selector in enumerate(selectors, 1):
                                f.write(f"{field}_selector_new_{i} = {selector}\n")

                logger.info(f"选择器已追加到文件: {existing_file}")
                return existing_file
            else:
                # 创建新文件
                return self._save_selectors_to_txt(final_config, category_path, domain)

        except Exception as e:
            logger.error(f"追加选择器到txt文件失败: {e}")
            return None


# 向后兼容的函数
def fill_gui_from_ai_result(ai_result: dict, gui_widgets: dict) -> bool:
    """向后兼容的函数，填充GUI表单"""
    manager = EnhancedAIConfigManager()
    return manager.apply_analysis_result_to_gui(ai_result, gui_widgets)


class AIConfigManager:
    """向后兼容的AI配置管理器类"""
    
    def __init__(self):
        self.enhanced_manager = EnhancedAIConfigManager()
    
    def __getattr__(self, name):
        return getattr(self.enhanced_manager, name)
