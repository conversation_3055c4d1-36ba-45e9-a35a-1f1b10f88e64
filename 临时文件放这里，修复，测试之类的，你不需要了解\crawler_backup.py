"""
新版爬虫模块 - 使用 Playwright 替代 Selenium
合并了 crawler.py 和 PaginationHandler.py 的功能
支持传统分页和动态分页
"""

import asyncio
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from typing import Optional, Dict, Any, Union, Callable, Awaitable
import logging
import sys
import time
import os
import csv
import random
import requests
import txt_clear
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import queue

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger('CrawlerPlaywright')
# Excel支持
try:
    import openpyxl
    from openpyxl import Workbook
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("警告: 未安装openpyxl库，Excel格式不可用。请运行: pip install openpyxl")

# 全局Excel文件锁字典和写入队列
_excel_locks = {}
_excel_queues = {}
_excel_lock = threading.Lock()  # 保护锁字典的锁

print("Loaded crawler.py from:", __file__)

# ==================== PaginationHandler 类 ====================

class PaginationHandler:
    """翻页处理器，支持多种动态翻页方式"""

    def __init__(self, page: Page):
        self.page = page
        self.all_articles = []  # 存储所有收集到的文章

    async def debug_pagination_elements(self, selectors: list = None) -> dict:
        """
        调试翻页元素，检查页面上可能的翻页按钮
        :param selectors: 要检查的选择器列表，如果为None则使用默认列表
        :return: 调试信息字典
        """
        if selectors is None:
            selectors = [
                "a.next:not(.lose)",
                "a.page-link:has-text('Next')",
                "a[onclick*='page']",
                ".js_pageI:not(.cur)",
                ".pager a",
                "a:contains('下一页')",
                "a:contains('Next')",
                ".next-page"
            ]

        debug_info = {
            'found_elements': [],
            'page_url': self.page.url,
            'page_title': await self.page.title()
        }

        for selector in selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    for i, element in enumerate(elements):
                        text = await element.text_content()
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()
                        debug_info['found_elements'].append({
                            'selector': selector,
                            'index': i,
                            'text': text.strip() if text else '',
                            'visible': is_visible,
                            'enabled': is_enabled
                        })
            except Exception as e:
                logger.warning(f"Error checking selector {selector}: {str(e)}")

        return debug_info

    async def click_pagination(
        self,
        next_button_selector: str,
        max_pages: int = 10,
        content_ready_selector: Optional[str] = None,
        timeout: int = 10000,
        wait_after_click: int = 1000,
        disabled_check: bool = True,
        extract_articles_config: dict = None
    ) -> int:
        """
        点击翻页处理并提取文章
        :param next_button_selector: 下一页按钮的CSS选择器
        :param max_pages: 最大翻页次数
        :param content_ready_selector: 内容加载完成的标识选择器
        :param timeout: 超时时间(毫秒)
        :param wait_after_click: 点击后等待的时间（毫秒）
        :param disabled_check: 是否检查按钮禁用状态
        :param extract_articles_config: 文章提取配置字典
        :return: 实际翻页次数
        """
        current_page = 1
        total_articles_extracted = 0

        # 首先提取第一页的文章
        if extract_articles_config:
            try:
                articles_count = await self.extract_articles_from_page(**extract_articles_config)
                total_articles_extracted += articles_count
                logger.info(f"第1页提取到 {articles_count} 篇文章")
            except Exception as e:
                logger.warning(f"第1页文章提取失败: {e}")

        # 开始翻页循环
        logger.info(f"开始翻页循环，当前页={current_page}, 最大页数={max_pages}")
        while current_page < max_pages:
            logger.info(f"尝试翻到第 {current_page + 1} 页...")

            # 等待内容加载
            if content_ready_selector:
                try:
                    await self.page.wait_for_selector(
                        content_ready_selector,
                        state="attached",
                        timeout=timeout
                    )
                    logger.info(f"Page {current_page} content loaded")
                except Exception as e:
                    logger.warning(f"Content loading warning: {str(e)}")

            # 尝试点击下一页
            try:
                logger.info(f"查找下一页按钮，选择器: {next_button_selector}")
                next_button = await self.page.wait_for_selector(
                    next_button_selector,
                    state="visible",
                    timeout=timeout
                )
                logger.info("✅ 找到下一页按钮")

                # 检查按钮是否被禁用
                if disabled_check:
                    is_disabled = await next_button.is_disabled()
                    logger.info(f"下一页按钮禁用状态: {is_disabled}")
                    if is_disabled:
                        logger.info("已到达最后一页（按钮被禁用）")
                        break
                else:
                    logger.info("跳过按钮禁用检查")

                # 获取按钮信息用于调试
                button_text = await next_button.text_content()
                button_href = await next_button.get_attribute('href')
                logger.info(f"下一页按钮信息: 文本='{button_text}', href='{button_href}'")

                logger.info("点击下一页按钮...")
                await next_button.click()
                current_page += 1
                logger.info(f"✅ 成功点击，当前页面: {current_page}")

                # 等待页面稳定
                logger.info(f"等待页面稳定 {wait_after_click}ms...")
                await self.page.wait_for_timeout(wait_after_click)

                # 提取当前页的文章
                if extract_articles_config:
                    try:
                        logger.info(f"开始提取第{current_page}页的文章...")
                        articles_count = await self.extract_articles_from_page(**extract_articles_config)
                        total_articles_extracted += articles_count
                        logger.info(f"第{current_page}页提取到 {articles_count} 篇文章")
                    except Exception as e:
                        logger.warning(f"第{current_page}页文章提取失败: {e}")

            except Exception as e:
                logger.error(f"翻页出错（第{current_page}页 -> 第{current_page + 1}页）: {str(e)}")
                # 如果是超时错误，提供更详细的信息
                if "Timeout" in str(e):
                    logger.error(f"❌ 找不到下一页按钮，选择器: {next_button_selector}")
                    logger.info("可能原因: 1) 已到达最后一页 2) 选择器不正确 3) 页面加载未完成")

                    # 尝试查找其他可能的下一页按钮
                    alternative_selectors = [
                        ".next",
                        "a.next",
                        "[title*='下一页']",
                        "a[href*='page']",
                        ".pagination a:last-child"
                    ]

                    logger.info("尝试查找其他可能的下一页按钮...")
                    for alt_selector in alternative_selectors:
                        try:
                            alt_elements = await self.page.query_selector_all(alt_selector)
                            if alt_elements:
                                logger.info(f"  找到替代选择器 '{alt_selector}': {len(alt_elements)} 个元素")
                        except:
                            pass
                else:
                    logger.error(f"其他翻页错误: {str(e)}")
                break

        logger.info(f"翻页完成！总共处理 {current_page} 页，提取 {total_articles_extracted} 篇文章")
        return current_page

    def add_articles(self, articles_list):
        """
        添加文章到all_articles列表

        Args:
            articles_list: 文章列表，格式为 [(title, href, save_dir, page_title, page_url, classid), ...]
        """
        if articles_list:
            self.all_articles.extend(articles_list)
            logger.info(f"添加了 {len(articles_list)} 篇文章，总计 {len(self.all_articles)} 篇")

    def get_all_articles(self):
        """
        获取所有收集到的文章

        Returns:
            list: 所有文章的列表
        """
        return self.all_articles.copy()

    def clear_articles(self):
        """清空文章列表"""
        self.all_articles.clear()
        logger.info("已清空文章列表")

    def get_articles_count(self):
        """获取文章总数"""
        return len(self.all_articles)

    async def extract_articles_from_page(self,
                                       list_container_selector=".main",
                                       article_item_selector=".clearfix.ty_list li a",
                                       title_selector=None,
                                       list_container_type="CSS",
                                       article_item_type="CSS",
                                       title_selector_type="CSS",
                                       save_dir="articles",
                                       page_title="动态翻页结果",
                                       classid="",
                                       base_url=None,
                                       url_mode="relative"):
        """
        从当前页面提取文章信息并添加到all_articles
        参考crawler.py的get_article_links和get_full_link函数逻辑

        Args:
            list_container_selector: 列表容器选择器
            article_item_selector: 文章项选择器
            title_selector: 标题选择器（可选）
            list_container_type: 列表容器选择器类型 (CSS/XPath)
            article_item_type: 文章项选择器类型 (CSS/XPath)
            title_selector_type: 标题选择器类型 (CSS/XPath)
            save_dir: 保存目录
            page_title: 页面标题
            classid: 分类ID
            base_url: 基础URL，用于构建完整链接
            url_mode: URL模式 ('absolute'/'relative')

        Returns:
            int: 本页提取到的文章数量
        """
        try:
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle', timeout=10000)

            current_url = self.page.url
            if not base_url:
                base_url = current_url

            # 查找列表容器 - 支持多个容器（参考crawler.py的get_article_links）
            if list_container_type.upper() == "XPATH":
                containers = await self.page.query_selector_all(f"xpath={list_container_selector}")
            else:
                containers = await self.page.query_selector_all(list_container_selector)

            if not containers:
                logger.warning(f"未找到列表容器: {list_container_selector}")
                return 0

            current_page_articles = []
            total_articles_found = 0

            # 遍历所有容器（参考crawler.py逻辑）
            for container in containers:
                try:
                    # 查找文章项
                    if article_item_type.upper() == "XPATH":
                        articles = await container.query_selector_all(f"xpath={article_item_selector}")
                    else:
                        articles = await container.query_selector_all(article_item_selector)

                    if not articles:
                        continue

                    total_articles_found += len(articles)

                    # 处理每个文章项（参考crawler.py的get_article_links逻辑）
                    for article in articles:
                        try:
                            # 增强的链接提取逻辑（参考crawler.py）
                            href = await article.get_attribute('href')

                            # 若没有href，尝试查找子元素的a标签
                            if not href:
                                try:
                                    a_tag = await article.query_selector('a')
                                    if a_tag:
                                        href = await a_tag.get_attribute('href')
                                except Exception:
                                    pass

                            # 如果还是没有，尝试从onclick提取（参考crawler.py逻辑）
                            if not href:
                                onclick = await article.get_attribute('onclick')
                                if onclick:
                                    # 使用正则表达式提取URL（与crawler.py相同的模式）
                                    patterns = [
                                        r"location(?:\.href)?\s*=\s*['\"]([^'\"]+)",  # location='url'
                                        r"window\.open\s*\(\s*['\"]([^'\"]+)",       # window.open('url')
                                        r"window\.location\.href\s*=\s*['\"]([^'\"]+)",  # window.location.href='url'
                                        r"redirectTo\s*\(\s*['\"]([^'\"]+)"           # 自定义函数如 redirectTo('url')
                                    ]

                                    for pattern in patterns:
                                        match = re.search(pattern, onclick)
                                        if match:
                                            href = match.group(1)
                                            break

                            # 增强的标题提取逻辑（参考crawler.py）
                            title = ""
                            if title_selector:
                                try:
                                    if title_selector_type.upper() == "XPATH":
                                        title_element = await article.query_selector(f"xpath={title_selector}")
                                    else:
                                        title_element = await article.query_selector(title_selector)
                                    if title_element:
                                        title = await title_element.text_content()
                                except Exception:
                                    pass

                            # 如果没有专门的标题选择器或提取失败，使用文章元素的文本
                            if not title:
                                title = await article.text_content()

                            if title:
                                title = title.strip()

                            # 处理完整URL（参考crawler.py的get_full_link逻辑）
                            if href:
                                full_url = self._get_full_link(href, current_url, base_url, url_mode)

                                # 构建文章信息元组，格式与crawler.py兼容
                                article_info = (title, full_url, save_dir, page_title, current_url, classid)
                                current_page_articles.append(article_info)

                        except Exception as e:
                            logger.warning(f"提取单个文章信息时出错: {e}")
                            continue

                except Exception as e:
                    logger.warning(f"处理容器时出错: {e}")
                    continue

            # 添加到总列表
            if current_page_articles:
                self.add_articles(current_page_articles)
                logger.info(f"从当前页面提取到 {len(current_page_articles)} 篇文章")

            return len(current_page_articles)

        except Exception as e:
            logger.error(f"从页面提取文章时出错: {e}")
            return 0

    def _get_full_link(self, href, input_url, base_url, url_mode):
        """
        获取完整的链接（参考crawler.py的get_full_link函数）

        Args:
            href: 原始链接
            input_url: 输入URL
            base_url: 基础URL
            url_mode: URL模式 ('absolute'/'relative')

        Returns:
            str: 完整的URL
        """
        if not href:
            return ''

        # 绝对URL
        if href.startswith(('http://', 'https://')):
            return href

        # 协议相对URL
        if href.startswith('//'):
            scheme = base_url.split(':')[0] if ':' in base_url else 'https'
            return f"{scheme}:{href}"

        # 锚点
        if href.startswith('#'):
            return urljoin(base_url, href)

        # 相对路径处理（与crawler.py逻辑一致）
        if url_mode == "absolute":
            return urljoin(input_url, href)
        elif url_mode == "relative":
            return urljoin(base_url, href)
        else:
            return urljoin(base_url, href)

# ==================== 浏览器启动函数 ====================

async def launch_browser(
    p,  # async_playwright 实例
    headless: bool = False,
    browser_type: str = "chromium",
    slow_mo: int = 100,
    proxy: Optional[Dict] = None,
    viewport: Dict[str, int] = {"width": 1280, "height": 720},
    user_agent: Optional[str] = None,
    **launch_kwargs
) -> tuple[Browser, BrowserContext, Page]:
    """
    启动Playwright浏览器
    :param p: async_playwright 实例
    :param headless: 是否无头模式
    :param browser_type: 浏览器类型 (chromium, firefox, webkit)
    :param slow_mo: 操作延迟(毫秒)
    :param proxy: 代理设置 {'server': 'http://host:port'}
    :param viewport: 视口大小 {'width': 1280, 'height': 720}
    :param user_agent: 自定义User-Agent
    :return: (browser, context, page)
    """
    # 选择浏览器类型
    browser_launcher = getattr(p, browser_type).launch

    # 准备启动参数
    launch_options = {
        "headless": headless,
        "slow_mo": slow_mo,
        **launch_kwargs
    }

    # 添加代理设置
    if proxy:
        launch_options["proxy"] = proxy

    browser = await browser_launcher(**launch_options)

    # 创建上下文
    context_options = {"viewport": viewport}
    if user_agent:
        context_options["user_agent"] = user_agent

    context = await browser.new_context(**context_options)
    page = await context.new_page()

    return browser, context, page

def get_excel_lock(file_path):
    """获取指定Excel文件的锁"""
    with _excel_lock:
        if file_path not in _excel_locks:
            _excel_locks[file_path] = threading.Lock()
        return _excel_locks[file_path]

def safe_excel_write(file_path, data_row, headers=None):
    """线程安全的Excel写入函数"""
    if not EXCEL_AVAILABLE:
        print("错误: Excel格式不可用，请安装openpyxl库")
        return False

    file_lock = get_excel_lock(file_path)

    with file_lock:
        try:
            # 检查文件是否存在
            if os.path.exists(file_path):
                try:
                    wb = openpyxl.load_workbook(file_path)
                    ws = wb.active
                except Exception as e:
                    print(f"警告: Excel文件可能损坏，将重新创建: {e}")
                    # 创建备份文件名（避免冲突）
                    import time
                    timestamp = int(time.time())
                    backup_path = f"{file_path}.backup_{timestamp}"
                    try:
                        if os.path.exists(file_path):
                            os.rename(file_path, backup_path)
                    except Exception as backup_e:
                        print(f"备份文件失败: {backup_e}")
                    # 创建新文件
                    wb = Workbook()
                    ws = wb.active
                    if headers:
                        ws.append(headers)
            else:
                wb = Workbook()
                ws = wb.active
                if headers:
                    ws.append(headers)

            # 添加数据行
            ws.append(data_row)

            # 保存文件
            wb.save(file_path)
            return True

        except Exception as e:
            print(f"Excel文件操作失败: {e}")
            return False

# 创建保存文章的目录
def create_save_dir(page_title, export_filename=None):
    base_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "articles")
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    if export_filename:
        # 只返回articles目录，不创建子文件夹
        return base_dir
    page_folder = "".join(c for c in page_title if c.isalnum() or c in (' ','-','_'))
    save_dir = os.path.join(base_dir, page_folder)
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    return save_dir

# 获取文章链接 (Playwright版本)
async def get_article_links_playwright(page: Page, input_url, list_container_selector, article_item_selector, list_container_type="CSS", article_item_type="CSS", title_selector=None, title_selector_type="CSS"):
    try:
        await page.goto(input_url, timeout=60000, wait_until="domcontentloaded")
    except Exception as e:
        raise RuntimeError(f"无法访问目标URL: {input_url}，错误: {e}")

    page_title = await page.title()
    logger.info(f"正在爬取页面：{page_title}")

    try:
        # 等待页面加载
        await page.wait_for_load_state('networkidle', timeout=10000)

        # 支持多个列表容器
        if list_container_type.upper() == "XPATH":
            main_containers = await page.query_selector_all(f"xpath={list_container_selector}")
        else:
            main_containers = await page.query_selector_all(list_container_selector)

        if not main_containers:
            raise RuntimeError(f"页面结构异常，未找到列表容器: {list_container_selector}")

    except Exception as e:
        raise RuntimeError(f"页面结构异常，未找到列表容器: {list_container_selector}，错误: {e}")

    article_elements = []
    article_links = []
    article_titles = []

    for container in main_containers:
        try:
            if article_item_type.upper() == "XPATH":
                articles = await container.query_selector_all(f"xpath={article_item_selector}")
            else:
                articles = await container.query_selector_all(article_item_selector)

            article_elements.extend(articles)

            for article in articles:
                # 优先取 article 本身的 href
                href = await article.get_attribute('href')

                # 若没有，再取其下第一个 a 标签的 href
                if not href:
                    try:
                        a_tag = await article.query_selector('a')
                        if a_tag:
                            href = await a_tag.get_attribute('href')
                    except Exception:
                        href = None

                # 如果还是没有，尝试从 onclick 提取
                if not href:
                    onclick = await article.get_attribute('onclick')
                    if onclick:
                        # 兼容 onclick="location='url'" 或 onclick="window.location='url'"
                        patterns = [
                            r"location(?:\.href)?\s*=\s*['\"]([^'\"]+)",  # location='url'
                            r"window\.open\s*\(\s*['\"]([^'\"]+)",       # window.open('url'
                            r"window\.location\.href\s*=\s*['\"]([^'\"]+)",  # window.location.href='url'
                            r"redirectTo\s*\(\s*['\"]([^'\"]+)"           # 自定义函数如 redirectTo('url'
                        ]

                        for pattern in patterns:
                            match = re.search(pattern, onclick)
                            if match:
                                href = match.group(1)
                                break

                # 获取文章标题
                title_text = ""
                if title_selector:
                    try:
                        if title_selector_type.upper() == "XPATH":
                            title_element = await article.query_selector(f"xpath={title_selector}")
                        else:
                            title_element = await article.query_selector(title_selector)
                        if title_element:
                            title_text = await title_element.text_content()
                    except Exception:
                        pass

                # 如果没有专门的标题选择器或提取失败，使用文章元素的文本
                if not title_text:
                    title_text = await article.text_content()

                if title_text:
                    title_text = title_text.strip()

                # 只要有链接就加入，保证一一对应
                article_links.append(href)
                article_titles.append(title_text)

        except Exception:
            continue

    logger.info(f"找到 {len(article_links)} 个有效链接")
    return article_elements, page_title, article_links, article_titles

# 获取完整的链接
def get_full_link(href, input_url, base_url, url_mode):
    """
    url_mode: 'absolute' 绝对路径，'relative' 相对路径（base_url+href）
    """
    if not href:
        return ''
    # 绝对URL
    if href.startswith(('http://', 'https://')):
        return href
    # 协议相对URL
    if href.startswith('//'):
        scheme = base_url.split(':')[0] if ':' in base_url else 'https'
        return f"{scheme}:{href}"
    # 锚点
    if href.startswith('#'):
        return urljoin(base_url, href)
    # 相对路径
    if url_mode == "absolute":
        return urljoin(input_url, href)
    elif url_mode == "relative":
        return urljoin(base_url, href)
    else:
        return urljoin(base_url, href)
    
# 保存文章内容到文件
def save_article(
    link, save_dir, page_title, content_selectors,
    date_selectors=None, source_selectors=None, title_selectors=None,
    # 保持向后兼容的单选择器参数
    date_selector=None, source_selector=None, title_selector=None,
    content_type="CSS", date_selector_type="CSS", source_selector_type="CSS", title_selector_type="CSS",
    collect_links=True, mode="balance", filters=None,
    export_filename=None,  # 新增参数
    classid="",            # 新增参数，允许自定义classid
    file_format="CSV",     # 新增参数，文件格式
    retry=2,                # 新增参数，重试次数
    interval=0              # 新增参数，下载间隔（秒）
):
    """
    多线程友好：每个线程独立 session，异常自动重试，支持限速。
    支持多选择器：title_selectors, date_selectors, source_selectors
    """
    session = requests.Session()

    # 处理向后兼容：将单选择器转换为多选择器列表
    if title_selectors is None:
        title_selectors = [title_selector] if title_selector else []
    elif isinstance(title_selectors, str):
        title_selectors = [title_selectors]

    if date_selectors is None:
        date_selectors = [date_selector] if date_selector else []
    elif isinstance(date_selectors, str):
        date_selectors = [date_selectors]

    if source_selectors is None:
        source_selectors = [source_selector] if source_selector else []
    elif isinstance(source_selectors, str):
        source_selectors = [source_selectors]

    # 判断文件名和格式
    if export_filename:
        if file_format.upper() == "EXCEL":
            filename = f"{export_filename}.xlsx"
        else:
            filename = f"{export_filename}.csv"
        file_path = os.path.join(save_dir, filename)
    else:
        if file_format.upper() == "EXCEL":
            file_path = os.path.join(save_dir, f"{page_title}.xlsx")
        else:
            file_path = os.path.join(save_dir, f"{page_title}.csv")
    city = ""
    if export_filename and "_" in export_filename:
        city = export_filename.split("_")[0]
    elif export_filename:
        city = os.path.splitext(export_filename)[0]
    article_date = ""
    article_source = ""
    img_links = []
    attach_links = []
    article_title = ""
    # --- 1. 快速模式（requests+bs4） ---
    def fetch_by_requests():
        last_exc = None
        for attempt in range(retry+1):
            try:
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                }
                resp = session.get(link, headers=headers, timeout=10)
                resp.encoding = resp.apparent_encoding
                soup = BeautifulSoup(resp.text, "html.parser")
                content = None
                for selector in content_selectors:
                    if content_type == "XPath":
                        continue
                    content = soup.select_one(selector)
                    if content:
                        break
                # 标题提取
                page_title_val = ""
                if title_selectors and title_selector_type == "CSS":
                    for title_sel in title_selectors:
                        if title_sel:  # 跳过空选择器
                            title_elem = soup.select_one(title_sel)
                            if title_elem:
                                page_title_val = title_elem.get_text(strip=True)
                                if page_title_val:  # 成功提取后停止
                                    break
                elif title_selector:
                    # 向后兼容的单选择器处理
                    title_elem = soup.select_one(title_selector)
                    if title_elem:
                        page_title_val = title_elem.get_text(strip=True)
                page_title_val = txt_clear.clean_title(page_title_val)
                if content:
                    html = str(content)
                    text = txt_clear.clean_html_content(html)
                    imgs, attaches = [], []
                    if collect_links:
                        imgs = [img['src'] for img in content.find_all("img") if img.get("src")]
                        for a in content.find_all("a", href=True):
                            href = a['href']
                            if any(href.lower().endswith(ext) for ext in [".pdf", ".doc", ".docx", ".zip", ".rar", ".xls", ".xlsx"]):
                                attaches.append(href)
                    date, source = "", ""

                    # 多选择器支持：日期提取
                    if date_selectors and date_selector_type == "CSS":
                        for date_sel in date_selectors:
                            if date_sel:  # 跳过空选择器
                                date_elem = soup.select_one(date_sel)
                                if date_elem:
                                    date_text = date_elem.get_text(strip=True)
                                    if "日期：" in date_text:
                                        date = date_text.split("日期：")[-1].strip().split()[0]
                                    else:
                                        date = date_text
                                    date = txt_clear.normalize_date(date)
                                    if date:  # 找到有效日期就停止
                                        break

                    # 多选择器支持：来源提取
                    if source_selectors and source_selector_type == "CSS":
                        for source_sel in source_selectors:
                            if source_sel:  # 跳过空选择器
                                source_elems = soup.select(source_sel)
                                if len(source_elems) > 1:
                                    source_text = source_elems[1].get_text(strip=True)
                                    if "来源：" in source_text:
                                        source = source_text.split("来源：")[-1].strip()
                                    else:
                                        source = source_text
                                elif source_elems:
                                    source = source_elems[0].get_text(strip=True)
                                if source and source != "本站":  # 找到有效来源就停止
                                    source = txt_clear.normalize_source(source)
                                    break
                        if not source:  # 如果所有选择器都没找到来源
                            source = "本站"
                    return text, date, source, imgs, attaches, page_title_val
                else:
                    return None, "", "", [], [], page_title_val
            except Exception as e:
                last_exc = e
                if attempt < retry:
                    time.sleep(1)
        return None, "", "", [], [], ""
    # --- 选择模式 ---
    content_text, article_date, article_source, img_links, attach_links, article_title = fetch_by_requests()
    content_text = txt_clear.filter_content(content_text, filters)
    all_links = []
    if collect_links:
        if img_links:
            all_links.append("图片链接: " + ", ".join(img_links))
        if attach_links:
            all_links.append("附件链接: " + ", ".join(attach_links))
        if all_links and content_text:
            content_text += "[" + " | ".join(all_links) + "]"
    if not article_source:
        article_source = "本站"
    now_str = time.strftime('%Y-%m-%d %H:%M:%S')
    data_row = [
        article_date, article_source, article_title, link, content_text, classid, city, now_str
    ]
    headers = ['dateget', 'source', 'title', 'articlelink', 'dateinfo', 'classid', 'city', 'getdate']
    try:
        if file_format.upper() == "EXCEL":
            # 使用线程安全的Excel写入函数
            success = safe_excel_write(file_path, data_row, headers)
            if not success:
                return False
        else:
            write_header = not os.path.exists(file_path)
            with open(file_path, 'a', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                if write_header:
                    writer.writerow(headers)
                writer.writerow(data_row)
        print(f"已保存: {article_title}")
        return True
    except Exception as e:
        print(f"保存文章时出错: {article_title}, 错误: {str(e)}")
        return False

# 简化的翻页URL生成函数（替代旧的翻页处理模块）
def get_page_url(input_url, page_num, page_suffix, page_suffix_next=None):
    """
    根据页码生成对应的页面URL（简化版本）

    Args:
        input_url: 输入URL
        page_num: 页码
        page_suffix: 翻页后缀
        page_suffix_next: 备用翻页后缀（兼容性参数，暂未使用）

    Returns:
        生成的页面URL
    """
    from urllib.parse import urljoin, urlparse
    import re

    if page_num == 1:
        return input_url

    # 简单的翻页后缀处理
    if "{n}" in page_suffix:
        suffix = page_suffix.replace("{n}", str(page_num))
    else:
        suffix = page_suffix

    # 如果输入URL以/结尾，直接拼接
    if input_url.endswith('/'):
        return input_url + suffix
    else:
        return input_url + '/' + suffix

# ==================== 文章批处理函数 ====================

def process_articles_batch(all_articles,
                          content_selectors,
                          # 多选择器支持
                          title_selectors=None,
                          date_selectors=None,
                          source_selectors=None,
                          # 向后兼容的单选择器参数
                          title_selector=None,
                          date_selector=None,
                          source_selector=None,
                          content_type="CSS",
                          date_selector_type="CSS",
                          source_selector_type="CSS",
                          title_selector_type="CSS",
                          collect_links=True,
                          mode="balance",
                          filters=None,
                          export_filename=None,
                          classid="",
                          file_format="CSV",
                          retry=2,
                          interval=0,
                          max_workers=5,
                          log_callback=None):
    """
    批量处理文章列表（下载和保存）
    支持多选择器：title_selectors, date_selectors, source_selectors

    Args:
        all_articles: 文章信息列表，格式为 [(title, href, save_dir, page_title, page_url, classid), ...]
        其他参数: 处理配置参数

    Returns:
        dict: 处理结果统计
    """
    from concurrent.futures import ThreadPoolExecutor, as_completed
    import threading

    # 处理向后兼容：将单选择器转换为多选择器列表
    if title_selectors is None:
        title_selectors = [title_selector] if title_selector else []
    elif isinstance(title_selectors, str):
        title_selectors = [title_selectors]

    if date_selectors is None:
        date_selectors = [date_selector] if date_selector else []
    elif isinstance(date_selectors, str):
        date_selectors = [date_selectors]

    if source_selectors is None:
        source_selectors = [source_selector] if source_selector else []
    elif isinstance(source_selectors, str):
        source_selectors = [source_selectors]

    if not all_articles:
        return {
            'total_articles': 0,
            'processed_articles': 0,
            'failed_articles': 0,
            'save_dir': None
        }

    # 确定保存目录
    if export_filename:
        save_dir = create_save_dir("articles", export_filename=export_filename)
    else:
        # 使用第一篇文章的页面标题作为目录名
        save_dir = create_save_dir(all_articles[0][3] if all_articles else "articles")

    success_count = 0
    fail_count = 0
    total_to_process = len(all_articles)

    def save_one(args):
        title, href, article_save_dir, page_title, page_url, classid_val = args
        # 如果有全局导出文件名，使用统一的保存目录
        final_save_dir = save_dir if export_filename else article_save_dir
        return save_article(
            href, final_save_dir, page_title, content_selectors,
            date_selectors=date_selectors, source_selectors=source_selectors, title_selectors=title_selectors,
            content_type=content_type, date_selector_type=date_selector_type, source_selector_type=source_selector_type,
            title_selector_type=title_selector_type, collect_links=collect_links, mode=mode, filters=filters,
            export_filename=export_filename, classid=classid_val, file_format=file_format,
            retry=retry, interval=interval
        )

    if log_callback:
        log_callback(f"开始批量处理 {total_to_process} 篇文章...")
    else:
        print(f"开始批量处理 {total_to_process} 篇文章...")

    # 多线程处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_article = {executor.submit(save_one, article): article for article in all_articles}

        for future in as_completed(future_to_article):
            try:
                result = future.result()
                if result:
                    success_count += 1
                else:
                    fail_count += 1
            except Exception as e:
                fail_count += 1
                if log_callback:
                    log_callback(f"处理文章时出错: {e}")
                else:
                    print(f"处理文章时出错: {e}")

            # 进度报告
            processed = success_count + fail_count
            if log_callback:
                log_callback(f"进度: {processed}/{total_to_process} (成功: {success_count}, 失败: {fail_count})")
            else:
                print(f"进度: {processed}/{total_to_process} (成功: {success_count}, 失败: {fail_count})")

    if log_callback:
        log_callback(f"批量处理完成！成功: {success_count}, 失败: {fail_count}")
    else:
        print(f"批量处理完成！成功: {success_count}, 失败: {fail_count}")

    return {
        'total_articles': len(all_articles),
        'processed_articles': success_count,
        'failed_articles': fail_count,
        'save_dir': save_dir if success_count > 0 else None
    }

# Selenium相关函数已删除，使用Playwright替代

# ==================== 传统分页处理函数 ====================

async def crawl_traditional_pagination_playwright(page: Page, input_url, base_url, max_pages=None,
                               list_container_selector=".main",
                               list_container_type="CSS",
                               article_item_selector=".clearfix.ty_list li a",
                               article_item_type="CSS",
                               title_selector=None,
                               title_selector_type="CSS",
                               page_suffix="index_{n}.html",
                               url_mode="absolute",
                               log_callback=None,
                               classid=""):
    """
    传统分页爬取功能 (Playwright版本)

    Args:
        page: Playwright Page实例
        input_url: 起始URL
        base_url: 基础URL
        max_pages: 最大页数
        其他参数: 选择器和配置参数

    Returns:
        tuple: (all_article_info, found_urls, total_articles)
    """
    processed_titles = set()
    all_article_info = []
    found_urls = set()
    page_num = 1
    total_articles = 0

    # 处理max_pages为0或None的情况
    if max_pages is not None:
        try:
            max_pages = int(max_pages)
            if max_pages <= 0:
                max_pages = None
        except ValueError:
            max_pages = None

    while True:
        if max_pages is not None and page_num > max_pages:
            msg = f"已达到最大页数限制: {max_pages}"
            if log_callback:
                log_callback(msg)
            else:
                logger.info(msg)
            break

        # 生成页面URL
        if page_num == 1:
            page_url = input_url
        else:
            page_url = get_page_url(
                input_url=base_url,
                page_num=page_num,
                page_suffix=page_suffix
            )

        if log_callback:
            log_callback(f"[DEBUG] page_num={page_num}, page_url={page_url}")
        else:
            logger.info(f"[DEBUG] page_num={page_num}, page_url={page_url}")

        try:
            articles, page_title, article_links, article_titles = await get_article_links_playwright(
                page, page_url, list_container_selector, article_item_selector,
                list_container_type, article_item_type, title_selector, title_selector_type)
            total_articles += len(articles)

        except Exception as e:
            msg = f"获取文章链接时出错: {str(e)}"
            # 遇到404直接跳过该页
            if "404" in str(e) or "Not Found" in str(e):
                msg2 = f"页面404，跳过本页: {page_url}"
                if log_callback:
                    log_callback(msg2)
                else:
                    logger.info(msg2)
                page_num += 1
                continue

            if log_callback:
                log_callback(msg)
            else:
                logger.error(msg)

            if "无法访问目标URL" in str(e):
                msg2 = f"请检查网络连接或目标网站是否可访问: {page_url}"
                if log_callback:
                    log_callback(msg2)
                else:
                    logger.error(msg2)
            elif "未找到列表容器" in str(e) or "未找到文章项" in str(e):
                msg2 = f"页面结构可能已变动，请检查选择器设置: {list_container_selector} / {article_item_selector}"
                if log_callback:
                    log_callback(msg2)
                else:
                    logger.error(msg2)
                break
            else:
                break

        if not articles:
            msg = "没有更多文章，采集结束。"
            if log_callback:
                log_callback(msg)
            else:
                logger.info(msg)
            break

        # 处理文章信息
        save_dir = create_save_dir(page_title)
        new_found = False

        for href, title in zip(article_links, article_titles):
            if not href:
                continue
            full_url = get_full_link(href, page_url, base_url, url_mode)
            found_urls.add(full_url)
            if title in processed_titles:
                continue
            all_article_info.append((title, href, save_dir, page_title, page_url, classid))
            new_found = True
            processed_titles.add(title)

        if not new_found:
            msg = "没有新文章。"
            if log_callback:
                log_callback(msg)
            else:
                logger.info(msg)
            break

        page_num += 1

    if log_callback:
        log_callback(f"共找到 {total_articles} 篇文章")
    else:
        logger.info(f"共找到 {total_articles} 篇文章")

    return all_article_info, found_urls, total_articles

# ==================== 主要爬取函数 ====================

def crawl_articles(all_articles=None, input_url=None, base_url=None, max_pages=None,
                  list_container_selector=".main",
                  list_container_type="CSS",
                  article_item_selector=".clearfix.ty_list li a",
                  article_item_type="CSS",
                  # 多选择器支持
                  title_selectors=None,
                  date_selectors=None,
                  source_selectors=None,
                  # 向后兼容的单选择器参数
                  title_selector=None,
                  date_selector="div.xl_ly.fl > span",
                  source_selector="div.xl_ly.fl > span",
                  title_selector_type="CSS",
                  date_selector_type="CSS",
                  source_selector_type="CSS",
                  content_selectors=[
                      ".article_cont",
                      "div[class*='content']",
                      "div[class*='article']",
                      "div.view.TRS_UEDITOR.trs_paper_default.trs_web",
                      ".TRS_Editor",
                      "div.zhengwen"
                  ],
                  content_type="CSS",
                  log_callback=None,
                  page_suffix="index_{n}.html",
                  page_suffix_start=1,
                  url_mode="absolute",
                  browser="firefox",
                  diver=None,
                  collect_links=True,   
                  mode="balance",      
                  filters=None,
                  export_filename=None, # 新增参数
                  classid="",           # 新增参数
                  file_format="CSV",    # 新增参数，文件格式
                  dynamic_pagination_type=None,  # 新增参数，动态翻页类型
                  max_workers=5,          # 新增参数，线程数
                  retry=2,                # 新增参数，重试次数
                  interval=0              # 新增参数，下载间隔（秒）
                  ):
    """
    主要爬取函数，支持传统分页和动态分页后处理

    Args:
        all_articles: 预处理的文章列表（来自PaginationHandler），如果提供则跳过爬取直接处理
        input_url: 起始URL（传统分页时使用）
        base_url: 基础URL（传统分页时使用）
        max_pages: 最大页数
        url_mode: 'absolute' 绝对路径，'relative' 相对路径（base_url+href）
        collect_links: 是否采集正文图片和附件链接
        mode: fast（只用requests+bs4），safe（只用selenium），balance（先requests失败再selenium，默认）
        filters: 过滤关键词或正则表达式列表，匹配到的行将被移除
        export_filename: 导出文件名（全局唯一，所有文章导出到同一文件）

    Returns:
        dict: 包含爬取结果的字典

    Note:
        - 如果提供all_articles，则直接进行后处理（下载和保存）
        - 如果未提供all_articles，则使用传统分页方式爬取
        - dynamic_pagination_type参数已弃用，动态翻页由GUI直接调用PaginationHandler处理
    """
    # 处理向后兼容：将单选择器转换为多选择器列表
    if title_selectors is None:
        title_selectors = [title_selector] if title_selector else []
    elif isinstance(title_selectors, str):
        title_selectors = [title_selectors]

    if date_selectors is None:
        date_selectors = [date_selector] if date_selector else []
    elif isinstance(date_selectors, str):
        date_selectors = [date_selectors]

    if source_selectors is None:
        source_selectors = [source_selector] if source_selector else []
    elif isinstance(source_selectors, str):
        source_selectors = [source_selectors]

    # 如果提供了all_articles，直接进行后处理
    if all_articles is not None:
        if log_callback:
            log_callback(f"使用预处理的文章列表，共 {len(all_articles)} 篇文章")
        else:
            print(f"使用预处理的文章列表，共 {len(all_articles)} 篇文章")

        return process_articles_batch(
            all_articles=all_articles,
            content_selectors=content_selectors,
            title_selectors=title_selectors,
            date_selectors=date_selectors,
            source_selectors=source_selectors,
            content_type=content_type,
            date_selector_type=date_selector_type,
            source_selector_type=source_selector_type,
            title_selector_type=title_selector_type,
            collect_links=collect_links,
            mode=mode,
            filters=filters,
            export_filename=export_filename,
            classid=classid,
            file_format=file_format,
            retry=retry,
            interval=interval,
            max_workers=max_workers,
            log_callback=log_callback
        )

    # 传统分页处理 - 使用 Playwright
    if log_callback:
        log_callback(f"开始爬取任务: {input_url}")
    else:
        logger.info(f"开始爬取任务: {input_url}")

    # 动态翻页处理已移至GUI直接调用PaginationHandler
    # 这里只处理传统分页
    if dynamic_pagination_type and dynamic_pagination_type != "traditional":
        if log_callback:
            log_callback("动态翻页应由GUI直接调用PaginationHandler处理，这里使用传统分页方式")
        else:
            logger.info("动态翻页应由GUI直接调用PaginationHandler处理，这里使用传统分页方式")

    # 注意：这个函数现在需要在异步环境中调用
    # 为了保持向后兼容，我们提供一个同步包装器
    logger.warning("crawl_articles 函数现在需要在异步环境中调用，请使用 crawl_articles_async")

    # 返回错误信息，提示用户使用异步版本
    return {
        "total": 0,
        "success": 0,
        "failed": 0,
        "save_dir": None,
        "error": "请使用 crawl_articles_async 函数进行异步调用"
    }

# ==================== 异步版本的主要爬取函数 ====================

async def crawl_articles_async(all_articles=None, input_url=None, base_url=None, max_pages=None,
                  list_container_selector=".main",
                  list_container_type="CSS",
                  article_item_selector=".clearfix.ty_list li a",
                  article_item_type="CSS",
                  # 多选择器支持
                  title_selectors=None,
                  date_selectors=None,
                  source_selectors=None,
                  # 向后兼容的单选择器参数
                  title_selector=None,
                  date_selector="div.xl_ly.fl > span",
                  source_selector="div.xl_ly.fl > span",
                  title_selector_type="CSS",
                  date_selector_type="CSS",
                  source_selector_type="CSS",
                  content_selectors=[
                      ".article_cont",
                      "div[class*='content']",
                      "div[class*='article']",
                      "div.view.TRS_UEDITOR.trs_paper_default.trs_web",
                      ".TRS_Editor",
                      "div.zhengwen"
                  ],
                  content_type="CSS",
                  log_callback=None,
                  page_suffix="index_{n}.html",
                  url_mode="absolute",
                  browser_type="chromium",
                  headless=False,
                  collect_links=True,
                  mode="balance",
                  filters=None,
                  export_filename=None, # 新增参数
                  classid="",           # 新增参数
                  file_format="CSV",    # 新增参数，文件格式
                  dynamic_pagination_type=None,  # 新增参数，动态翻页类型
                  max_workers=5,          # 新增参数，线程数
                  retry=2,                # 新增参数，重试次数
                  interval=0              # 新增参数，下载间隔（秒）
                  ):
    """
    主要爬取函数的异步版本，支持传统分页和动态分页后处理

    Args:
        all_articles: 预处理的文章列表（来自PaginationHandler），如果提供则跳过爬取直接处理
        input_url: 起始URL（传统分页时使用）
        base_url: 基础URL（传统分页时使用）
        max_pages: 最大页数
        browser_type: 浏览器类型 (chromium, firefox, webkit)
        headless: 是否无头模式
        其他参数: 与原函数相同

    Returns:
        dict: 包含爬取结果的字典
    """
    # 处理向后兼容：将单选择器转换为多选择器列表
    if title_selectors is None:
        title_selectors = [title_selector] if title_selector else []
    elif isinstance(title_selectors, str):
        title_selectors = [title_selectors]

    if date_selectors is None:
        date_selectors = [date_selector] if date_selector else []
    elif isinstance(date_selectors, str):
        date_selectors = [date_selectors]

    if source_selectors is None:
        source_selectors = [source_selector] if source_selector else []
    elif isinstance(source_selectors, str):
        source_selectors = [source_selectors]

    # 如果提供了all_articles，直接进行后处理
    if all_articles is not None:
        if log_callback:
            log_callback(f"使用预处理的文章列表，共 {len(all_articles)} 篇文章")
        else:
            logger.info(f"使用预处理的文章列表，共 {len(all_articles)} 篇文章")

        return process_articles_batch(
            all_articles=all_articles,
            content_selectors=content_selectors,
            title_selectors=title_selectors,
            date_selectors=date_selectors,
            source_selectors=source_selectors,
            content_type=content_type,
            date_selector_type=date_selector_type,
            source_selector_type=source_selector_type,
            title_selector_type=title_selector_type,
            collect_links=collect_links,
            mode=mode,
            filters=filters,
            export_filename=export_filename,
            classid=classid,
            file_format=file_format,
            retry=retry,
            interval=interval,
            max_workers=max_workers,
            log_callback=log_callback
        )

    # 传统分页处理 - 使用 Playwright
    if log_callback:
        log_callback(f"开始爬取任务: {input_url}")
    else:
        logger.info(f"开始爬取任务: {input_url}")

    async with async_playwright() as p:
        browser, context, page = await launch_browser(
            p,
            headless=headless,
            browser_type=browser_type
        )

        try:
            # 使用传统分页爬取
            all_article_info, found_urls, total_articles = await crawl_traditional_pagination_playwright(
                page=page,
                input_url=input_url,
                base_url=base_url,
                max_pages=max_pages,
                list_container_selector=list_container_selector,
                list_container_type=list_container_type,
                article_item_selector=article_item_selector,
                article_item_type=article_item_type,
                title_selector=title_selector,
                title_selector_type=title_selector_type,
                page_suffix=page_suffix,
                url_mode=url_mode,
                log_callback=log_callback,
                classid=classid
            )

            # 使用批处理函数处理文章
            result = process_articles_batch(
                all_articles=all_article_info,
                content_selectors=content_selectors,
                title_selectors=title_selectors,
                date_selectors=date_selectors,
                source_selectors=source_selectors,
                content_type=content_type,
                date_selector_type=date_selector_type,
                source_selector_type=source_selector_type,
                title_selector_type=title_selector_type,
                collect_links=collect_links,
                mode=mode,
                filters=filters,
                export_filename=export_filename,
                classid=classid,
                file_format=file_format,
                retry=retry,
                interval=interval,
                max_workers=max_workers,
                log_callback=log_callback
            )

            return {
                "total": result['total_articles'],
                "success": result['processed_articles'],
                "failed": result['failed_articles'],
                "save_dir": result['save_dir']
            }

        finally:
            await context.close()
            await browser.close()