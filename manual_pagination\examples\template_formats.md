# URL格式说明和示例

## 📋 Excel文件格式说明

### 必需列
- **URL**: 要爬取的页面完整URL（必填）
- **页面名称**: 页面的描述性名称（可选，建议填写）
- **状态**: 处理状态（系统自动更新）
- **文章数量**: 提取的文章数量（系统自动更新）
- **备注**: 其他说明信息（可选）

### 状态说明
- `待处理`: 尚未开始处理
- `处理中`: 正在处理该页面
- `完成`: 成功处理并提取到文章
- `无文章`: 处理成功但未找到文章
- `失败: 错误信息`: 处理失败及原因

## 🌐 常见URL格式示例

### 1. 政府网站翻页格式

#### 天津市政协
```
https://www.tjszx.gov.cn/tagz/taxd/index.shtml      # 第1页
https://www.tjszx.gov.cn/tagz/taxd/index_2.shtml    # 第2页
https://www.tjszx.gov.cn/tagz/taxd/index_3.shtml    # 第3页
```

#### 全国人大
```
http://www.npc.gov.cn/npc/c30834/list.shtml        # 第1页
http://www.npc.gov.cn/npc/c30834/list_2.shtml      # 第2页
http://www.npc.gov.cn/npc/c30834/list_3.shtml      # 第3页
```

### 2. 新闻网站翻页格式

#### 参数式翻页
```
https://news.example.com/list?page=1                # 第1页
https://news.example.com/list?page=2                # 第2页
https://news.example.com/list?page=3                # 第3页
```

#### 路径式翻页
```
https://news.example.com/list/1/                    # 第1页
https://news.example.com/list/2/                    # 第2页
https://news.example.com/list/3/                    # 第3页
```

### 3. 论坛网站翻页格式

#### Discuz论坛
```
https://bbs.example.com/forum-2-1.html              # 第1页
https://bbs.example.com/forum-2-2.html              # 第2页
https://bbs.example.com/forum-2-3.html              # 第3页
```

#### phpBB论坛
```
https://forum.example.com/viewforum.php?f=2&start=0    # 第1页
https://forum.example.com/viewforum.php?f=2&start=25   # 第2页
https://forum.example.com/viewforum.php?f=2&start=50   # 第3页
```

## 📝 Excel填写示例

| URL | 页面名称 | 状态 | 文章数量 | 备注 |
|-----|----------|------|----------|------|
| https://www.tjszx.gov.cn/tagz/taxd/index.shtml | 提案选登第1页 | 待处理 | 0 | 天津市政协提案 |
| https://www.tjszx.gov.cn/tagz/taxd/index_2.shtml | 提案选登第2页 | 待处理 | 0 | 天津市政协提案 |
| https://www.tjszx.gov.cn/tagz/taxd/index_3.shtml | 提案选登第3页 | 待处理 | 0 | 天津市政协提案 |

## 💡 填写建议

### 1. URL填写规范
- 使用完整的URL，包含协议（http://或https://）
- 确保URL可以正常访问
- 按照页面顺序填写，便于管理

### 2. 页面名称建议
- 使用描述性名称，如"提案选登第1页"
- 包含页码信息，便于识别
- 保持命名一致性

### 3. 备注信息
- 记录URL来源或特殊说明
- 标注特殊处理要求
- 记录已知问题或注意事项

## 🔧 URL获取方法

### 1. 手动浏览获取
1. 在浏览器中访问网站
2. 逐页点击翻页按钮
3. 复制每页的URL地址
4. 填入Excel文件

### 2. 开发者工具分析
1. 打开浏览器开发者工具
2. 点击翻页按钮观察网络请求
3. 分析URL规律
4. 批量生成URL列表

### 3. 源码分析
1. 查看页面源码
2. 找到翻页链接的HTML
3. 提取href属性值
4. 组合成完整URL

## ⚠️ 注意事项

### 1. URL有效性
- 确保所有URL都能正常访问
- 检查是否需要登录或特殊权限
- 验证URL格式是否正确

### 2. 访问频率
- 合理控制页面间的访问间隔
- 避免对目标网站造成过大负担
- 遵守网站的robots.txt规则

### 3. 数据备份
- 定期备份Excel文件
- 保存处理结果
- 记录重要的配置信息

## 🚀 快速开始

1. **复制模板**: 复制`example_urls.xlsx`作为起点
2. **填写URL**: 替换示例URL为实际要爬取的URL
3. **设置名称**: 为每个页面设置描述性名称
4. **保存文件**: 保存Excel文件
5. **开始爬取**: 在GUI中选择手动翻页模式并加载Excel文件
