#!/usr/bin/env python3
"""
测试修复后的天津税务局翻页功能
对比原始测试文件和修复后的程序翻页效果
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.PaginationHandler import PaginationHandler
from core.crawler import launch_browser
from playwright.async_api import async_playwright

async def test_fixed_tjszx_pagination():
    """测试修复后的天津税务局翻页功能"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("🚀 开始测试修复后的天津税务局翻页功能")
    print(f"📍 测试URL: {url}")
    
    async with async_playwright() as p:
        # 启动浏览器（可见模式便于调试）
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            # 创建PaginationHandler实例
            handler = PaginationHandler(page)
            
            # 访问页面
            print(f"🌐 访问页面: {url}")
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(2000)  # 等待页面完全加载
            
            # 准备文章提取配置
            extract_config = {
                'list_container_selector': '.main',
                'article_item_selector': 'li a',
                'title_selector': 'a',
                'save_dir': "测试结果",
                'page_title': "天津税务局测试",
                'classid': 'test',
                'base_url': 'https://www.tjszx.gov.cn',
                'url_mode': 'relative'
            }
            
            print("\n=== 测试1: 使用简单翻页模式 ===")
            # 测试简单翻页模式
            pages_processed = await handler.click_pagination(
                next_button_selector='a:has-text("下一页")',
                max_pages=3,  # 测试3页
                wait_after_click=2000,
                extract_articles_config=extract_config,
                use_simple_pagination=True,  # 启用简单翻页模式
                auto_detect_pagination=False  # 禁用自动检测
            )
            
            print(f"✅ 简单翻页模式完成，处理了 {pages_processed} 页")
            
            # 获取收集的文章
            articles_simple = handler.get_all_articles()
            print(f"📊 简单翻页模式收集了 {len(articles_simple)} 篇文章")
            
            if len(articles_simple) > 0:
                print("📋 简单翻页模式收集的前几篇文章:")
                for i, article in enumerate(articles_simple[:5]):
                    title = article[0] if len(article) > 0 else "无标题"
                    url_link = article[1] if len(article) > 1 else "无URL"
                    print(f"  [{i+1}] {title[:50]}...")
                    print(f"      URL: {url_link}")
            
            # 重新访问页面进行对比测试
            print("\n=== 测试2: 使用标准翻页模式（对比） ===")
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(2000)
            
            # 创建新的handler实例
            handler2 = PaginationHandler(page)
            
            # 测试标准翻页模式
            pages_processed2 = await handler2.click_pagination(
                next_button_selector='a:has-text("下一页")',
                max_pages=3,
                wait_after_click=2000,
                extract_articles_config=extract_config,
                use_simple_pagination=False,  # 使用标准翻页模式
                auto_detect_pagination=True
            )
            
            print(f"✅ 标准翻页模式完成，处理了 {pages_processed2} 页")
            
            # 获取收集的文章
            articles_standard = handler2.get_all_articles()
            print(f"📊 标准翻页模式收集了 {len(articles_standard)} 篇文章")
            
            # 对比结果
            print("\n=== 结果对比 ===")
            print(f"简单翻页模式: {pages_processed} 页, {len(articles_simple)} 篇文章")
            print(f"标准翻页模式: {pages_processed2} 页, {len(articles_standard)} 篇文章")
            
            if pages_processed > pages_processed2:
                print("🎉 简单翻页模式表现更好！")
            elif pages_processed == pages_processed2:
                print("✅ 两种模式表现相同")
            else:
                print("⚠️ 标准翻页模式表现更好")
            
            # 关闭浏览器
            await context.close()
            await browser.close()
            
            return {
                'simple_mode': {
                    'pages': pages_processed,
                    'articles': len(articles_simple)
                },
                'standard_mode': {
                    'pages': pages_processed2,
                    'articles': len(articles_standard)
                }
            }
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            await page.screenshot(path='tjszx_test_error.png')
            await context.close()
            await browser.close()
            raise e

async def test_simple_pagination_only():
    """仅测试简单翻页模式"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    print("🚀 仅测试简单翻页模式")
    print(f"📍 测试URL: {url}")
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            handler = PaginationHandler(page)
            
            print(f"🌐 访问页面: {url}")
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(2000)
            
            extract_config = {
                'list_container_selector': '.main',
                'article_item_selector': 'li a',
                'title_selector': 'a',
                'save_dir': "简单翻页测试",
                'page_title': "天津税务局简单翻页",
                'classid': 'simple_test',
                'base_url': 'https://www.tjszx.gov.cn',
                'url_mode': 'relative'
            }
            
            print("🔄 开始简单翻页测试...")
            pages_processed = await handler.click_pagination(
                next_button_selector='a:has-text("下一页")',
                max_pages=5,  # 测试5页
                wait_after_click=2000,
                extract_articles_config=extract_config,
                use_simple_pagination=True,
                auto_detect_pagination=False
            )
            
            articles = handler.get_all_articles()
            
            print(f"🎉 测试完成！")
            print(f"📄 处理页数: {pages_processed}")
            print(f"📊 收集文章: {len(articles)} 篇")
            
            if len(articles) > 0:
                print("\n📋 收集的文章样例:")
                for i, article in enumerate(articles[:10]):
                    title = article[0] if len(article) > 0 else "无标题"
                    print(f"  [{i+1}] {title[:60]}...")
            
            await context.close()
            await browser.close()
            
            return {
                'pages': pages_processed,
                'articles': len(articles),
                'success': pages_processed > 1
            }
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            await page.screenshot(path='simple_pagination_error.png')
            await context.close()
            await browser.close()
            return {
                'pages': 0,
                'articles': 0,
                'success': False,
                'error': str(e)
            }

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 对比测试（简单模式 vs 标准模式）")
    print("2. 仅测试简单翻页模式")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        result = asyncio.run(test_fixed_tjszx_pagination())
        print("\n=== 最终结果 ===")
        print(f"简单翻页: {result['simple_mode']['pages']}页, {result['simple_mode']['articles']}篇")
        print(f"标准翻页: {result['standard_mode']['pages']}页, {result['standard_mode']['articles']}篇")
    else:
        result = asyncio.run(test_simple_pagination_only())
        print("\n=== 最终结果 ===")
        if result['success']:
            print(f"✅ 简单翻页成功: {result['pages']}页, {result['articles']}篇文章")
        else:
            print(f"❌ 简单翻页失败: {result.get('error', '未知错误')}")
