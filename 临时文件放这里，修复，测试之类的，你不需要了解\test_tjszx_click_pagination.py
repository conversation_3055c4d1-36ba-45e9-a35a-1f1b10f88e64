#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试天津市政协网站的动态翻页点击功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from core.crawler import launch_browser
from core.PaginationHandler import PaginationHandler

async def test_click_pagination():
    """测试动态翻页点击功能"""
    print("🧪 测试天津市政协网站动态翻页点击功能")
    print("=" * 60)
    
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            print(f"📋 访问网站: {url}")
            await page.goto(url, timeout=30000)
            await page.wait_for_load_state('networkidle', timeout=15000)
            
            print("✅ 页面加载完成")
            
            # 创建PaginationHandler实例
            handler = PaginationHandler(page)
            
            # 配置文章提取参数
            extract_config = {
                'list_container_selector': 'body',  # 使用整个页面作为容器
                'article_item_selector': 'a[href*="/tagz/system/"]',  # 提案链接
                'title_selectors': None,
                'date_selectors': None,
                'source_selectors': None,
                'url_mode': 'absolute'
            }
            
            print("\n🔍 开始测试动态翻页...")
            
            # 测试不同的选择器
            test_selectors = [
                "a.next:not(.lose)",  # 默认选择器
                "a.next",
                ".next",
                "a[title*='下一页']",
                "a:has-text('下一页')",
                "a:has-text('>')",
                "a[href*='index_2']",
                "a[onclick*='page']",
                "a[onclick*='Page']"
            ]
            
            for selector in test_selectors:
                print(f"\n🎯 测试选择器: {selector}")
                
                try:
                    # 使用PaginationHandler的click_pagination方法
                    pages_processed = await handler.click_pagination(
                        next_button_selector=selector,
                        max_pages=2,  # 只测试2页
                        content_ready_selector=None,
                        timeout=5000,  # 5秒超时
                        wait_after_click=2000,
                        disabled_check=True,
                        extract_articles_config=extract_config,
                        stop_on_duplicate_last_item=True,
                        auto_detect_pagination=False  # 禁用自动检测，直接尝试点击
                    )
                    
                    print(f"  ✅ 成功处理了 {pages_processed} 页")
                    
                    # 获取收集的文章
                    articles = handler.get_all_articles()
                    print(f"  📊 总共收集了 {len(articles)} 篇文章")
                    
                    if len(articles) > 0:
                        print("  📋 前几篇文章:")
                        for i, article in enumerate(articles[:3]):
                            title = article[0] if len(article) > 0 else "无标题"
                            url = article[1] if len(article) > 1 else "无URL"
                            print(f"    [{i+1}] {title[:50]}... | {url}")
                    
                    # 如果成功处理了多页，说明找到了有效的选择器
                    if pages_processed > 1:
                        print(f"  🎉 找到有效的翻页选择器: {selector}")
                        break
                    else:
                        print(f"  ⚠️ 只处理了1页，可能没有翻页功能或选择器无效")
                        
                except Exception as e:
                    print(f"  ❌ 测试失败: {e}")
                    continue
            
            # 如果所有选择器都失败，尝试手动查找翻页元素
            print("\n🔍 手动查找可能的翻页元素...")
            
            # 获取页面中所有可能的翻页元素
            all_links = await page.query_selector_all("a")
            potential_next_buttons = []
            
            for link in all_links:
                try:
                    text = await link.text_content()
                    href = await link.get_attribute('href')
                    onclick = await link.get_attribute('onclick')
                    class_name = await link.get_attribute('class')
                    
                    # 检查是否可能是翻页按钮
                    if (text and any(keyword in text.lower() for keyword in ['下一页', 'next', '>', '更多', '2', '3'])) or \
                       (href and any(keyword in href.lower() for keyword in ['page', 'index_'])) or \
                       (onclick and any(keyword in onclick.lower() for keyword in ['page', 'next'])) or \
                       (class_name and any(keyword in class_name.lower() for keyword in ['next', 'page'])):
                        
                        potential_next_buttons.append({
                            'element': link,
                            'text': text,
                            'href': href,
                            'onclick': onclick,
                            'class': class_name
                        })
                        
                except Exception:
                    continue
            
            if potential_next_buttons:
                print(f"  ✅ 找到 {len(potential_next_buttons)} 个可能的翻页元素:")
                for i, btn in enumerate(potential_next_buttons[:5]):
                    print(f"    [{i+1}] 文本: '{btn['text']}' | href: '{btn['href']}' | onclick: '{btn['onclick']}' | class: '{btn['class']}'")
                    
                    # 尝试点击第一个看起来最有希望的按钮
                    if i == 0:
                        try:
                            print(f"    🎯 尝试点击第一个按钮...")
                            
                            # 检查按钮状态
                            is_visible = await btn['element'].is_visible()
                            is_enabled = await btn['element'].is_enabled()
                            print(f"    📋 按钮状态: 可见={is_visible}, 可用={is_enabled}")
                            
                            if is_visible and is_enabled:
                                # 获取点击前的URL
                                current_url = page.url
                                print(f"    📋 点击前URL: {current_url}")
                                
                                # 点击按钮
                                await btn['element'].click()
                                print("    ✅ 点击成功")
                                
                                # 等待页面变化
                                await page.wait_for_timeout(3000)
                                
                                # 检查URL是否改变
                                new_url = page.url
                                print(f"    📋 点击后URL: {new_url}")
                                
                                if new_url != current_url:
                                    print("    🎉 页面URL发生了变化，翻页成功！")
                                else:
                                    print("    ⚠️ 页面URL没有变化，可能是AJAX翻页或无效点击")
                                    
                                    # 检查页面内容是否变化
                                    await page.wait_for_timeout(2000)
                                    print("    🔍 检查页面内容是否有变化...")
                                    
                            else:
                                print("    ❌ 按钮不可见或不可用")
                                
                        except Exception as e:
                            print(f"    ❌ 点击测试失败: {e}")
            else:
                print("  ❌ 未找到任何可能的翻页元素")
            
            print("\n📊 测试完成")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            
        finally:
            await context.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_click_pagination())
