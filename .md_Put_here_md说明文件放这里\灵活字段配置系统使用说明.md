# 🔧 灵活字段配置系统使用说明

## 📋 概述

新的灵活字段配置系统允许您根据实际需求动态增减文章保存的字段，支持点赞数、阅读量、价格、成交量等各种自定义字段。

## 🎯 核心特性

### 1. 可配置字段
- **基础字段**: 标题、内容、链接、日期等传统字段
- **扩展字段**: 点赞数、阅读量、价格、成交量、评分等
- **自定义字段**: 完全自定义的字段配置

### 2. 字段预设
- **basic**: 基础字段集合
- **social_media**: 社交媒体相关字段（点赞、阅读量、评论等）
- **ecommerce**: 电商相关字段（价格、成交量、评分等）
- **news**: 新闻相关字段（分类、标签、阅读量等）
- **blog**: 博客相关字段（字数、阅读时长等）
- **comprehensive**: 综合字段集合

### 3. 智能提取器
- **文本提取器**: 提取纯文本内容
- **数字提取器**: 提取数字（点赞数、阅读量等）
- **货币提取器**: 提取价格信息
- **日期提取器**: 提取和标准化日期
- **列表提取器**: 提取标签、分类等列表数据

## 🚀 使用方式

### 1. 使用预设字段

```python
from core.field_config_manager import apply_field_preset

# 应用社交媒体预设（包含点赞数、阅读量等）
apply_field_preset("social_media")

# 应用电商预设（包含价格、成交量等）
apply_field_preset("ecommerce")

# 应用综合预设（包含所有常用字段）
apply_field_preset("comprehensive")
```

### 2. 自定义字段列表

```python
from core.field_config_manager import apply_custom_field_list

# 自定义字段组合
custom_fields = [
    "title",        # 标题
    "articlelink",  # 链接
    "content",      # 内容
    "likes",        # 点赞数
    "views",        # 阅读量
    "price",        # 价格
    "sales",        # 成交量
    "getdate"       # 采集时间
]

apply_custom_field_list(custom_fields)
```

### 3. 添加完全自定义字段

```python
from core.field_config_manager import get_field_config_manager

manager = get_field_config_manager()

# 定义自定义字段
custom_field = {
    "name": "库存数量",
    "type": "number",
    "selectors": [".stock-count", "[data-stock]", ".inventory"],
    "required": False,
    "default": 0,
    "extractor": "number_extractor",
    "description": "商品库存数量"
}

# 添加字段
manager.add_custom_field("stock", custom_field)

# 应用包含自定义字段的配置
apply_custom_field_list(["title", "price", "stock", "sales"])
```

## 📊 可用字段列表

### 基础字段
| 字段名 | 中文名 | 类型 | 说明 |
|--------|--------|------|------|
| title | 标题 | text | 文章标题 |
| articlelink | 文章链接 | url | 文章URL |
| content | 内容 | html | 文章正文 |
| dateget | 发布日期 | text | 文章发布日期 |
| source | 来源 | text | 文章来源或作者 |
| classid | 分类ID | text | 文章分类标识 |
| city | 城市 | text | 文章所属城市 |
| getdate | 采集时间 | datetime | 数据采集时间 |

### 扩展字段
| 字段名 | 中文名 | 类型 | 说明 |
|--------|--------|------|------|
| likes | 点赞数 | number | 文章点赞数量 |
| views | 阅读量 | number | 文章阅读量 |
| comments | 评论数 | number | 文章评论数量 |
| shares | 分享数 | number | 文章分享次数 |
| price | 价格 | currency | 商品或服务价格 |
| sales | 成交量 | number | 商品成交数量 |
| rating | 评分 | float | 文章或商品评分 |
| tags | 标签 | list | 文章标签列表 |
| category | 分类 | text | 文章分类 |
| word_count | 字数 | number | 文章字数统计 |
| reading_time | 阅读时长 | text | 预估阅读时长 |

## 🔧 在爬虫中使用

### 方法1: 通过参数配置

```python
from core.crawler import crawl_articles_async

# 使用预设
result = await crawl_articles_async(
    all_articles=articles,
    content_selectors=[".content"],
    field_preset="social_media",  # 使用社交媒体预设
    # ... 其他参数
)

# 使用自定义字段列表
result = await crawl_articles_async(
    all_articles=articles,
    content_selectors=[".content"],
    custom_field_list=["title", "likes", "views", "price"],
    # ... 其他参数
)
```

### 方法2: 预先配置

```python
from core.field_config_manager import apply_field_preset
from core.crawler import crawl_articles_async

# 预先配置字段
apply_field_preset("ecommerce")

# 正常调用爬虫
result = await crawl_articles_async(
    all_articles=articles,
    content_selectors=[".content"],
    # ... 其他参数
)
```

## 🎯 字段预设详情

### social_media 预设
适用于社交媒体平台的内容采集：
```json
["dateget", "source", "title", "articlelink", "content", "likes", "views", "comments", "shares", "getdate"]
```

### ecommerce 预设
适用于电商平台的商品信息采集：
```json
["title", "articlelink", "content", "price", "sales", "rating", "category", "getdate"]
```

### news 预设
适用于新闻网站的文章采集：
```json
["dateget", "source", "title", "articlelink", "content", "category", "tags", "views", "getdate"]
```

### comprehensive 预设
包含所有常用字段的综合预设：
```json
["dateget", "source", "title", "articlelink", "content", "classid", "city", "likes", "views", "comments", "shares", "price", "sales", "rating", "tags", "category", "getdate"]
```

## 🔍 选择器配置

每个字段都可以配置多个CSS选择器，系统会按顺序尝试：

```json
{
  "likes": {
    "selectors": [
      ".like-count",     // 优先尝试
      ".praise-num",     // 备选1
      "[data-likes]",    // 备选2
      ".zan-count"       // 备选3
    ]
  }
}
```

## 🧪 测试验证

运行测试脚本验证字段系统：

```bash
python "临时文件放这里，修复，测试之类的，/test_flexible_fields.py"
```

## 📝 配置文件

字段配置保存在 `configs/fields/field_configs.json`，您可以：

1. **查看所有可用字段**
2. **修改字段选择器**
3. **添加新的字段定义**
4. **创建自定义预设**

## 💡 最佳实践

### 1. 选择合适的预设
- 社交媒体内容 → `social_media`
- 电商商品信息 → `ecommerce`
- 新闻文章 → `news`
- 博客文章 → `blog`

### 2. 优化选择器
根据目标网站的HTML结构，调整字段的CSS选择器以提高提取准确性。

### 3. 性能考虑
- 只启用需要的字段，避免不必要的提取开销
- 对于大量数据，优先使用基础字段

### 4. 数据验证
定期检查提取的数据质量，根据需要调整选择器或字段配置。

## 🎉 总结

灵活字段配置系统让您能够：

1. **按需配置**: 根据实际需求选择字段
2. **快速切换**: 使用预设快速适应不同场景
3. **完全自定义**: 添加任何您需要的字段
4. **易于维护**: 集中管理所有字段配置

这个系统大大提升了爬虫的灵活性和适用性，让您能够轻松应对各种数据采集需求。
