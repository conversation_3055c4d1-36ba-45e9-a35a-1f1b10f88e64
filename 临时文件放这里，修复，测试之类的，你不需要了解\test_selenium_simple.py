#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Selenium测试
"""

import time
import sys
import os

def test_selenium_simple():
    """简化的Selenium测试"""
    print("🧪 简化的Selenium测试")
    print("=" * 60)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        print("✅ Selenium导入成功")
        
        # 配置Chrome选项
        chrome_options = Options()
        # 有头模式
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        print("🚀 启动Chrome浏览器...")
        
        # 使用webdriver_manager自动下载和管理ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("✅ Chrome浏览器启动成功")
        
        try:
            url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
            print(f"📋 访问网站: {url}")
            
            driver.get(url)
            time.sleep(5)
            
            print("✅ 页面加载完成")
            print(f"📋 页面标题: {driver.title}")
            
            # 查找翻页相关元素
            print("\n🔍 查找翻页相关元素...")
            
            # 查找所有包含"下一页"的元素
            try:
                elements = driver.find_elements(By.XPATH, "//*[contains(text(), '下一页')]")
                if elements:
                    print(f"✅ 找到 {len(elements)} 个包含'下一页'的元素")
                    for i, elem in enumerate(elements):
                        print(f"  [{i+1}] 标签: {elem.tag_name}, 文本: '{elem.text}', 可见: {elem.is_displayed()}")
                else:
                    print("❌ 未找到包含'下一页'的元素")
            except Exception as e:
                print(f"❌ 查找元素失败: {e}")
            
            # 查找所有input元素
            try:
                inputs = driver.find_elements(By.TAG_NAME, "input")
                print(f"\n🔍 找到 {len(inputs)} 个input元素")
                for i, inp in enumerate(inputs[:10]):  # 只显示前10个
                    input_type = inp.get_attribute('type')
                    input_value = inp.get_attribute('value')
                    print(f"  [{i+1}] type: {input_type}, value: '{input_value}', 可见: {inp.is_displayed()}")
            except Exception as e:
                print(f"❌ 查找input元素失败: {e}")
            
            # 保持浏览器打开
            print("\n⏳ 保持浏览器打开20秒，请手动检查...")
            time.sleep(20)
            
        finally:
            driver.quit()
            print("🔚 浏览器已关闭")
            
    except ImportError as e:
        print(f"❌ 导入Selenium失败: {e}")
        print("💡 请安装Selenium: pip install selenium webdriver-manager")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_selenium_simple()
