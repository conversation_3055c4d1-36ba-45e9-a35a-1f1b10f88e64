#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSP处理器集成到主爬虫系统
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core import crawler

async def test_jsp_integration():
    """测试JSP处理器集成"""
    print("🧪 测试JSP处理器集成到主爬虫系统...")
    
    # 合肥政协网站测试URL
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    print(f"📋 测试URL: {test_url}")
    
    def log_callback(message):
        print(f"[LOG] {message}")
    
    try:
        print("\n🔍 第一步：测试文章链接收集（使用JSP处理器）...")
        
        # 使用collect_articles_from_pagination函数测试
        collected_articles, found_urls, total_articles = await crawler.collect_articles_from_pagination(
            input_url=test_url,
            max_pages=2,  # 测试2页
            headless=True,
            disable_js_injection=False,
            log_callback=log_callback,
            use_module_config=True  # 启用模组配置
        )
        
        print(f"\n✅ 文章链接收集结果:")
        print(f"   - 收集到文章: {len(collected_articles) if collected_articles else 0}")
        print(f"   - 唯一URL: {len(found_urls) if found_urls else 0}")
        print(f"   - 总计文章: {total_articles}")
        
        if collected_articles:
            print(f"\n📋 文章样本:")
            for i, article in enumerate(collected_articles[:5]):
                title = article[0] if len(article) > 0 else "无标题"
                url = article[1] if len(article) > 1 else "无URL"
                print(f"   {i+1}. {title} -> {url}")
        
        if not collected_articles:
            print("⚠️ 未收集到文章，创建测试数据继续测试...")
            collected_articles = [
                ("测试文章1", "http://www.hfszx.org.cn/hfzx/web/article.jsp?strId=test1", "articles", "测试页面", test_url, "test"),
                ("测试文章2", "http://www.hfszx.org.cn/hfzx/web/article.jsp?strId=test2", "articles", "测试页面", test_url, "test"),
            ]
            print(f"📝 创建了 {len(collected_articles)} 个测试文章")
        
        print(f"\n📝 第二步：测试内容提取...")
        
        # 使用crawl_articles_async函数测试内容提取
        result = await crawler.crawl_articles_async(
            all_articles=collected_articles,
            headless=True,
            disable_js_injection=False,
            mode="safe",  # 使用安全模式
            file_format="CSV",
            export_filename="test_jsp_integration",
            max_workers=2,  # 减少并发数
            retry=1,
            interval=1,
            log_callback=log_callback,
            use_module_config=True
        )
        
        print(f"\n✅ 内容提取结果:")
        print(f"   - 总计: {result.get('total', 0)}")
        print(f"   - 成功: {result.get('success', 0)}")
        print(f"   - 失败: {result.get('failed', 0)}")
        print(f"   - 保存目录: {result.get('save_dir', '未知')}")
        
        print(f"\n🎉 JSP处理器集成测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

async def test_traditional_vs_jsp():
    """对比传统方式和JSP处理器的效果"""
    print("\n🔄 对比测试：传统方式 vs JSP处理器...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    def log_callback(message):
        print(f"[COMPARE] {message}")
    
    try:
        # 测试1：不使用模组配置（传统方式）
        print("\n📊 测试1：传统方式（不使用模组配置）")
        traditional_articles, _, traditional_total = await crawler.collect_articles_from_pagination(
            input_url=test_url,
            max_pages=1,
            headless=True,
            log_callback=log_callback,
            use_module_config=False  # 禁用模组配置
        )
        
        print(f"   传统方式结果: {len(traditional_articles) if traditional_articles else 0} 篇文章")
        
        # 测试2：使用模组配置（JSP处理器）
        print("\n📊 测试2：JSP处理器（使用模组配置）")
        jsp_articles, _, jsp_total = await crawler.collect_articles_from_pagination(
            input_url=test_url,
            max_pages=1,
            headless=True,
            log_callback=log_callback,
            use_module_config=True  # 启用模组配置
        )
        
        print(f"   JSP处理器结果: {len(jsp_articles) if jsp_articles else 0} 篇文章")
        
        # 对比结果
        print(f"\n📈 对比结果:")
        print(f"   - 传统方式: {len(traditional_articles) if traditional_articles else 0} 篇")
        print(f"   - JSP处理器: {len(jsp_articles) if jsp_articles else 0} 篇")
        
        if jsp_articles and len(jsp_articles) > (len(traditional_articles) if traditional_articles else 0):
            print(f"   ✅ JSP处理器效果更好！")
        elif traditional_articles and len(traditional_articles) > (len(jsp_articles) if jsp_articles else 0):
            print(f"   ⚠️ 传统方式效果更好")
        else:
            print(f"   🤔 两种方式效果相当")
        
    except Exception as e:
        print(f"❌ 对比测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始JSP处理器集成测试...")
    
    # 运行主要测试
    asyncio.run(test_jsp_integration())
    
    # 运行对比测试
    asyncio.run(test_traditional_vs_jsp())
    
    print("\n🎯 所有测试完成！")
