#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微信公众号文章的collect_links设置
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append('.')

from core.crawler import save_article_async
from modules.manager import match_module_for_url, get_config_for_url

async def test_wechat_collect_links():
    """测试微信公众号的collect_links设置"""

    # 测试URL
    test_url = "https://mp.weixin.qq.com/s/60jlvKivGwROdL3UQRmm0g"

    print("=" * 60)
    print("测试微信公众号文章的collect_links设置（修复后）")
    print("=" * 60)

    # 1. 检查模组配置
    print(f"1. 测试URL: {test_url}")
    module_name = match_module_for_url(test_url)
    print(f"   匹配的模组: {module_name}")

    config = get_config_for_url(test_url)
    if config:
        collect_links_setting = config.get('collect_links', '未设置')
        print(f"   模组中的collect_links设置: {collect_links_setting}")
    else:
        print("   未获取到模组配置")
        return

    # 2. 测试GUI配置管理器的修复
    print(f"\n2. 测试GUI配置管理器的模组配置覆盖功能...")

    try:
        from gui.config_manager import ConfigManager

        # 模拟GUI配置（collect_links=True）
        gui_config = {
            'input_url': test_url,
            'collect_links': True,  # GUI设置为True
            'use_module_config': True,  # 启用模组配置
            'mode': 'balance',
            'headless': True,
            'content_selectors': ['#js_content'],
            'title_selectors': ['#activity-name'],
            'date_selectors': ['#publish_time'],
            'source_selectors': ['.rich_media_meta_nickname']
        }

        config_manager = ConfigManager()
        crawler_config = config_manager.prepare_crawler_config(gui_config)

        final_collect_links = crawler_config.get('collect_links')
        print(f"   GUI原始设置: collect_links = True")
        print(f"   最终爬虫配置: collect_links = {final_collect_links}")

        if final_collect_links == False:
            print("   ✅ 模组配置成功覆盖GUI设置")
        else:
            print("   ❌ 模组配置未能覆盖GUI设置")

    except Exception as e:
        print(f"   ❌ 测试GUI配置管理器时出错: {e}")

    # 3. 测试实际爬取（使用修复后的配置）
    print(f"\n3. 测试实际爬取（应该不包含图片链接）...")

    try:
        result = await save_article_async(
            link=test_url,
            save_dir="test_output",
            page_title="测试微信文章",
            content_selectors=["#js_content"],
            title_selectors=["#activity-name", ".rich_media_title"],
            date_selectors=["#publish_time"],
            source_selectors=[".rich_media_meta_nickname"],
            content_type='CSS',
            collect_links=True,  # GUI设置为True，但应该被模组配置覆盖
            mode='safe',
            export_filename="test_wechat_fixed",
            classid="test",
            file_format="CSV",
            retry=2,
            interval=1,
            use_module_config=True  # 启用模组配置
        )
        
        if result:
            if isinstance(result, bool):
                if result:
                    print("   ✅ 爬取成功（返回布尔值）")
                    # 检查生成的文件
                    import glob
                    csv_files = glob.glob("test_wechat_fixed*.csv")
                    if csv_files:
                        print(f"   生成的文件: {csv_files[0]}")
                        # 读取文件内容检查
                        try:
                            with open(csv_files[0], 'r', encoding='utf-8') as f:
                                content = f.read()
                                if '[图片链接:' in content:
                                    print("   ❌ 发现问题：文件中仍包含图片链接信息")
                                    print("   修复可能未完全生效")

                                    # 显示图片链接部分
                                    import re
                                    img_pattern = r'\[图片链接:[^\]]+\]'
                                    img_matches = re.findall(img_pattern, content)
                                    if img_matches:
                                        print(f"   发现的图片链接信息: {img_matches[0][:100]}...")
                                else:
                                    print("   ✅ 修复成功：文件中没有图片链接信息")
                        except Exception as e:
                            print(f"   读取文件时出错: {e}")
                else:
                    print("   ❌ 爬取失败（返回False）")
            elif isinstance(result, dict):
                if result.get('success'):
                    print("   ✅ 爬取成功")
                    content = result.get('content', '')

                    # 检查内容中是否包含图片链接
                    if '[图片链接:' in content:
                        print("   ❌ 发现问题：内容中仍包含图片链接信息")
                        print("   修复可能未完全生效")

                        # 显示图片链接部分
                        import re
                        img_pattern = r'\[图片链接:[^\]]+\]'
                        img_matches = re.findall(img_pattern, content)
                        if img_matches:
                            print(f"   发现的图片链接信息: {img_matches[0][:100]}...")
                    else:
                        print("   ✅ 修复成功：内容中没有图片链接信息")
                else:
                    print("   ❌ 爬取失败")
                    print(f"   错误信息: {result.get('error', '未知错误')}")
        else:
            print("   ❌ 爬取失败（无返回值）")

    except Exception as e:
        print(f"   ❌ 爬取过程中出现异常: {e}")

    print("\n4. 测试完成")
    print("\n总结：")
    print("- 微信公众号模组配置中 collect_links = false")
    print("- GUI配置管理器已修复，会优先使用模组配置")
    print("- 如果测试通过，说明图片链接问题已解决")

if __name__ == "__main__":
    # 创建测试输出目录
    os.makedirs("test_output", exist_ok=True)
    
    # 运行测试
    asyncio.run(test_wechat_collect_links())
