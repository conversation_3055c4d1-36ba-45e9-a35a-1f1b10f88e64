#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义字段选择器对话框
允许用户为特定字段自定义CSS选择器
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTextEdit, QComboBox,
                            QGroupBox, QGridLayout, QScrollArea, QWidget,
                            QMessageBox, QTabWidget, QCheckBox, QSpinBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from core.field_config_manager import get_field_config_manager
    FIELD_CONFIG_AVAILABLE = True
except ImportError:
    FIELD_CONFIG_AVAILABLE = False

class CustomSelectorDialog(QDialog):
    """自定义字段选择器对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("自定义字段选择器")
        self.setModal(True)
        self.resize(800, 600)
        
        # 字段选择器编辑器字典
        self.selector_editors = {}
        
        self.setup_ui()
        self.load_current_selectors()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 说明文字
        info_label = QLabel("为每个字段自定义CSS选择器，多个选择器用逗号分隔。系统会按顺序尝试这些选择器。")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 12px; padding: 10px;")
        layout.addWidget(info_label)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 基础字段标签页
        self.create_basic_fields_tab()
        
        # 扩展字段标签页
        self.create_extended_fields_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        # 重置按钮
        reset_btn = QPushButton("重置为默认")
        reset_btn.setToolTip("重置所有选择器为默认值")
        reset_btn.clicked.connect(self.reset_to_default)
        button_layout.addWidget(reset_btn)
        
        # 测试按钮
        test_btn = QPushButton("测试选择器")
        test_btn.setToolTip("测试当前选择器配置")
        test_btn.clicked.connect(self.test_selectors)
        button_layout.addWidget(test_btn)
        
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept_changes)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def create_basic_fields_tab(self):
        """创建基础字段标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        
        if FIELD_CONFIG_AVAILABLE:
            try:
                manager = get_field_config_manager()
                basic_fields = manager.config_data.get("default_fields", {})
                
                for field_name, field_config in basic_fields.items():
                    group = self.create_field_editor_group(field_name, field_config)
                    scroll_layout.addWidget(group)
                    
            except Exception as e:
                error_label = QLabel(f"加载基础字段失败: {e}")
                error_label.setStyleSheet("color: red;")
                scroll_layout.addWidget(error_label)
        else:
            error_label = QLabel("字段配置功能不可用")
            error_label.setStyleSheet("color: orange;")
            scroll_layout.addWidget(error_label)
        
        scroll_layout.addStretch()
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        layout.addWidget(scroll_area)
        tab.setLayout(layout)
        
        self.tab_widget.addTab(tab, "基础字段")
    
    def create_extended_fields_tab(self):
        """创建扩展字段标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        
        if FIELD_CONFIG_AVAILABLE:
            try:
                manager = get_field_config_manager()
                extended_fields = manager.config_data.get("extended_fields", {})
                
                for field_name, field_config in extended_fields.items():
                    group = self.create_field_editor_group(field_name, field_config)
                    scroll_layout.addWidget(group)
                    
            except Exception as e:
                error_label = QLabel(f"加载扩展字段失败: {e}")
                error_label.setStyleSheet("color: red;")
                scroll_layout.addWidget(error_label)
        else:
            error_label = QLabel("字段配置功能不可用")
            error_label.setStyleSheet("color: orange;")
            scroll_layout.addWidget(error_label)
        
        scroll_layout.addStretch()
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        layout.addWidget(scroll_area)
        tab.setLayout(layout)
        
        self.tab_widget.addTab(tab, "扩展字段")
    
    def create_field_editor_group(self, field_name, field_config):
        """创建字段编辑器组"""
        group = QGroupBox(f"{field_config.get('name', field_name)} ({field_name})")
        layout = QVBoxLayout()
        
        # 字段描述
        description = field_config.get('description', '')
        if description:
            desc_label = QLabel(f"说明: {description}")
            desc_label.setStyleSheet("color: #666; font-size: 11px;")
            layout.addWidget(desc_label)
        
        # 字段类型和提取器信息
        field_type = field_config.get('type', 'text')
        extractor = field_config.get('extractor', 'text_extractor')
        info_label = QLabel(f"类型: {field_type} | 提取器: {extractor}")
        info_label.setStyleSheet("color: #888; font-size: 10px;")
        layout.addWidget(info_label)
        
        # 检查是否为静态字段（不需要选择器）
        extractor = field_config.get('extractor', 'text_extractor')
        is_static_field = extractor in ['static_extractor', 'timestamp_extractor', 'url_extractor']

        if is_static_field:
            # 静态字段说明
            static_info = QLabel()
            if extractor == 'static_extractor':
                static_info.setText("📌 此字段为静态字段，值通过参数传入，无需配置选择器")
            elif extractor == 'timestamp_extractor':
                static_info.setText("⏰ 此字段为时间戳字段，自动生成当前时间，无需配置选择器")
            elif extractor == 'url_extractor':
                static_info.setText("🔗 此字段为URL字段，自动使用文章链接，无需配置选择器")

            static_info.setStyleSheet("color: #2196F3; font-weight: bold; padding: 10px; background-color: #E3F2FD; border-radius: 5px; margin: 5px 0;")
            static_info.setWordWrap(True)
            layout.addWidget(static_info)

            # 不创建选择器编辑器，但保存一个空的引用
            self.selector_editors[field_name] = None
        else:
            # 选择器编辑器
            selector_label = QLabel("CSS选择器 (多个用逗号分隔):")
            layout.addWidget(selector_label)

            selector_edit = QTextEdit()
            selector_edit.setMaximumHeight(80)
            selector_edit.setPlaceholderText("例如: .like-count, .praise-num, [data-likes]")

            # 设置当前选择器
            current_selectors = field_config.get('selectors', [])
            if current_selectors:
                selector_edit.setText(", ".join(current_selectors))

            layout.addWidget(selector_edit)

            # 保存编辑器引用
            self.selector_editors[field_name] = selector_edit
        
        group.setLayout(layout)
        return group
    
    def load_current_selectors(self):
        """加载当前选择器配置"""
        # 选择器已在创建编辑器组时加载
        pass
    
    def reset_to_default(self):
        """重置为默认选择器"""
        if not FIELD_CONFIG_AVAILABLE:
            return
            
        try:
            # 重新加载默认配置
            manager = get_field_config_manager()
            manager.load_config()
            
            # 更新编辑器
            all_fields = manager.get_available_fields()
            for field_name, editor in self.selector_editors.items():
                if editor is not None and field_name in all_fields:  # 跳过静态字段
                    selectors = all_fields[field_name].get('selectors', [])
                    editor.setText(", ".join(selectors))
            
            QMessageBox.information(self, "成功", "已重置为默认选择器配置")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"重置失败: {e}")
    
    def test_selectors(self):
        """测试选择器"""
        # 获取父窗口并显示URL选择对话框
        parent_window = self.parent()
        if hasattr(parent_window, 'show_url_selection_dialog'):
            test_url = parent_window.show_url_selection_dialog()
            if not test_url:
                return
        else:
            QMessageBox.warning(self, "警告", "无法获取URL选择功能")
            return
        
        # 收集当前配置的选择器
        field_selectors = {}
        for field_name, editor in self.selector_editors.items():
            if editor is not None:  # 跳过静态字段
                selector_text = editor.toPlainText().strip()
                if selector_text:
                    selectors = [s.strip() for s in selector_text.split(',') if s.strip()]
                    field_selectors[field_name] = selectors
        
        if not field_selectors:
            QMessageBox.warning(self, "警告", "没有配置任何选择器")
            return
        
        # 打开测试对话框
        try:
            from gui.selector_test_dialog import SelectorTestDialog
            dialog = SelectorTestDialog(self, test_url, field_selectors)
            dialog.exec_()
        except ImportError:
            QMessageBox.warning(self, "警告", "测试功能不可用")
    
    def accept_changes(self):
        """接受更改"""
        if not FIELD_CONFIG_AVAILABLE:
            QMessageBox.warning(self, "警告", "字段配置功能不可用")
            return
        
        try:
            # 收集所有选择器配置
            manager = get_field_config_manager()
            
            # 更新字段配置
            for field_name, editor in self.selector_editors.items():
                if editor is not None:  # 跳过静态字段
                    selector_text = editor.toPlainText().strip()
                    selectors = []
                    if selector_text:
                        selectors = [s.strip() for s in selector_text.split(',') if s.strip()]

                    # 更新配置
                    all_fields = manager.get_available_fields()
                    if field_name in all_fields:
                        # 更新选择器
                        if field_name in manager.config_data.get("default_fields", {}):
                            manager.config_data["default_fields"][field_name]["selectors"] = selectors
                        elif field_name in manager.config_data.get("extended_fields", {}):
                            manager.config_data["extended_fields"][field_name]["selectors"] = selectors
            
            # 保存配置
            manager.save_config()
            
            QMessageBox.information(self, "成功", "字段选择器配置已保存")
            self.accept()
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存配置失败: {e}")


if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    dialog = CustomSelectorDialog()
    dialog.show()
    sys.exit(app.exec_())
