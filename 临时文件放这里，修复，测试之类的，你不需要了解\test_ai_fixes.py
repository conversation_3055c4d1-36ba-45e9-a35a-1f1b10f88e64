#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI模块修复
验证AI模块的导入和基本功能是否正常
"""

import sys
import os
import asyncio
import traceback

def test_ai_imports():
    """测试AI模块导入"""
    print("🔍 测试AI模块导入...")
    print("="*50)
    
    results = {}
    
    # 测试AI分析器导入
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        results['ai_analyzer'] = "✅ 成功"
        print("✅ ai.analyzer.AIAnalyzerWithTesting 导入成功")
    except Exception as e:
        results['ai_analyzer'] = f"❌ 失败: {e}"
        print(f"❌ ai.analyzer.AIAnalyzerWithTesting 导入失败: {e}")
    
    # 测试AI助手导入
    try:
        from ai.helper import EnhancedAIConfigManager, LLMConfigDialog
        results['ai_helper'] = "✅ 成功"
        print("✅ ai.helper 导入成功")
    except Exception as e:
        results['ai_helper'] = f"❌ 失败: {e}"
        print(f"❌ ai.helper 导入失败: {e}")
    
    return results

def test_ai_config_loading():
    """测试AI配置加载"""
    print("\n🔧 测试AI配置加载...")
    print("="*50)
    
    try:
        from ai.analyzer import load_ai_config
        config = load_ai_config()
        
        print(f"✅ 配置加载成功")
        print(f"   API Key: {'已配置' if config.get('api_key') else '未配置'}")
        print(f"   Base URL: {config.get('base_url', '未配置')}")
        print(f"   Model: {config.get('model', '未配置')}")
        print(f"   AI启用: {config.get('enable_ai', False)}")
        
        return True
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_ai_analyzer_creation():
    """测试AI分析器创建"""
    print("\n🤖 测试AI分析器创建...")
    print("="*50)
    
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        analyzer = AIAnalyzerWithTesting()
        
        print("✅ AI分析器创建成功")
        
        # 检查关键属性
        if hasattr(analyzer, 'test_manager'):
            print(f"✅ test_manager 属性存在: {analyzer.test_manager is not None}")
        
        if hasattr(analyzer, 'retry_strategies'):
            print(f"✅ retry_strategies 存在，包含 {len(analyzer.retry_strategies)} 个策略")
        
        return True
    except Exception as e:
        print(f"❌ AI分析器创建失败: {e}")
        traceback.print_exc()
        return False

def test_ai_manager_creation():
    """测试AI管理器创建"""
    print("\n📋 测试AI管理器创建...")
    print("="*50)
    
    try:
        from ai.helper import EnhancedAIConfigManager
        manager = EnhancedAIConfigManager()
        
        print("✅ AI管理器创建成功")
        
        # 测试关键方法
        try:
            llm_status = manager.check_llm_config()
            print(f"✅ LLM配置检查: {llm_status}")
        except Exception as e:
            print(f"⚠️ LLM配置检查失败: {e}")
        
        try:
            is_running = manager.is_running()
            print(f"✅ 运行状态检查: {is_running}")
        except Exception as e:
            print(f"⚠️ 运行状态检查失败: {e}")
        
        return True
    except Exception as e:
        print(f"❌ AI管理器创建失败: {e}")
        traceback.print_exc()
        return False

async def test_basic_ai_functionality():
    """测试基础AI功能"""
    print("\n🧪 测试基础AI功能...")
    print("="*50)
    
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        analyzer = AIAnalyzerWithTesting()
        
        # 测试HTML清理功能
        test_html = "<html><head><script>test</script></head><body><p>Test content</p></body></html>"
        cleaned = analyzer._clean_html(test_html)
        
        if "script" not in cleaned and "Test content" in cleaned:
            print("✅ HTML清理功能正常")
        else:
            print("❌ HTML清理功能异常")
            return False
        
        # 测试AI结果解析
        test_ai_result = '{"title_selectors": ["h1", ".title"], "content_selectors": [".content"]}'
        parsed = analyzer._parse_ai_result(test_ai_result)
        
        if isinstance(parsed, dict) and 'title_selectors' in parsed:
            print("✅ AI结果解析功能正常")
        else:
            print("❌ AI结果解析功能异常")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 基础AI功能测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 AI模块修复验证测试")
    print("="*60)
    
    # 运行所有测试
    test_results = []
    
    # 导入测试
    import_results = test_ai_imports()
    test_results.append(('导入测试', all(r.startswith('✅') for r in import_results.values())))
    
    # 配置测试
    config_result = test_ai_config_loading()
    test_results.append(('配置测试', config_result))
    
    # 分析器创建测试
    analyzer_result = test_ai_analyzer_creation()
    test_results.append(('分析器创建测试', analyzer_result))
    
    # 管理器创建测试
    manager_result = test_ai_manager_creation()
    test_results.append(('管理器创建测试', manager_result))
    
    # 基础功能测试
    try:
        basic_result = asyncio.run(test_basic_ai_functionality())
        test_results.append(('基础功能测试', basic_result))
    except Exception as e:
        print(f"❌ 基础功能测试异常: {e}")
        test_results.append(('基础功能测试', False))
    
    # 生成总结报告
    print("\n📊 测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / total * 100) if total > 0 else 0
    print(f"\n总体结果: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 AI模块修复基本成功！")
    elif success_rate >= 60:
        print("⚠️ AI模块部分修复，仍有问题需要解决")
    else:
        print("❌ AI模块修复失败，需要进一步调试")

if __name__ == "__main__":
    main()
