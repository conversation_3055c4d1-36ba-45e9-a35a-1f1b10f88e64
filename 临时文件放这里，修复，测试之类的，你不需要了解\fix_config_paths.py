#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复配置文件路径引用
确保所有文件都使用正确的配置文件路径
"""

import os
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_config_paths():
    """修复所有文件中的配置路径"""
    
    # 路径映射
    path_mappings = {
        # 旧路径 -> 新路径
        '"config.json"': '"configs/app/config.json"',
        "'config.json'": "'configs/app/config.json'",
        '"myconfig.json"': '"configs/app/myconfig.json"',
        "'myconfig.json'": "'configs/app/myconfig.json'",
        '"module_configs.json"': '"configs/modules/module_configs.json"',
        "'module_configs.json'": "'configs/modules/module_configs.json'",
        '"llm_config.json"': '"configs/ai/llm_config.json"',
        "'llm_config.json'": "'configs/ai/llm_config.json'",
        '"crawler_config.json"': '"configs/crawler/crawler_config.json"',
        "'crawler_config.json'": "'configs/crawler/crawler_config.json'",
    }
    
    # 需要检查的文件
    files_to_check = [
        'config/manager.py',
        'modules/manager.py',
        'modules/config_manager.py',
        'ai/helper.py',
        'ai/analyzer.py',
        'gui/main_window.py',
        'gui/config_manager.py',
        'testing/selectors_test.py',
        'core/crawler.py',
        'core/failed_url_processor.py'
    ]
    
    fixed_count = 0
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 应用路径映射
                for old_path, new_path in path_mappings.items():
                    if old_path in content:
                        content = content.replace(old_path, new_path)
                        logger.info(f"在 {file_path} 中替换: {old_path} -> {new_path}")
                
                # 如果内容有变化，写回文件
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    logger.info(f"更新文件: {file_path}")
                    fixed_count += 1
                    
            except Exception as e:
                logger.error(f"处理文件失败 {file_path}: {e}")
    
    logger.info(f"总共修复了 {fixed_count} 个文件的配置路径")

def ensure_config_files_exist():
    """确保配置文件存在"""
    
    config_files = {
        'configs/app/config.json': {
            "last_used": "default",
            "groups": {
                "default": {
                    "input_url": "",
                    "list_container_selector": "",
                    "article_item_selector": "",
                    "title_selector": "",
                    "content_selector": "",
                    "date_selector": "",
                    "source_selector": "",
                    "mode": "safe",
                    "headless": True,
                    "file_format": "CSV"
                }
            }
        },
        
        'configs/app/myconfig.json': {},
        
        'configs/modules/module_configs.json': {
            "微信公众号": {
                "description": "处理微信公众号文章的模组配置",
                "domain_patterns": ["mp.weixin.qq.com"],
                "url_patterns": [],
                "config": {
                    "title_selectors": ["#activity-name"],
                    "content_selectors": ["#js_content"],
                    "date_selectors": ["#publish_time"],
                    "source_selectors": ["#js_name"]
                }
            },
            "珠海政协": {
                "description": "从配置组 '珠海政协' 创建的模组",
                "domain_patterns": ["www.zhzx.gov.cn"],
                "url_patterns": [".*www\\.zhzx\\.gov\\.cn/zwhgz/jjwkjw/.*"],
                "config": {
                    "title_selectors": [".title", "h1", ".article-title"],
                    "content_selectors": [".content", ".article-content", "#content"],
                    "date_selectors": [".date", ".time", ".publish-time"],
                    "source_selectors": [".source", ".author"]
                }
            }
        },
        
        'configs/ai/llm_config.json': {
            "api_key": "",
            "base_url": "https://api.deepseek.com/v1",
            "model": "deepseek-chat",
            "temperature": 0.3,
            "max_tokens": 1500,
            "enable_ai": True
        },
        
        'configs/crawler/crawler_config.json': {
            "default_timeout": 30000,
            "default_wait_time": 2000,
            "max_retries": 3,
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
    }
    
    for file_path, default_content in config_files.items():
        if not os.path.exists(file_path):
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            try:
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(default_content, f, ensure_ascii=False, indent=2)
                logger.info(f"创建配置文件: {file_path}")
            except Exception as e:
                logger.error(f"创建配置文件失败 {file_path}: {e}")

def test_module_manager():
    """测试模组管理器是否正常工作"""
    try:
        from modules.manager import ModuleManager
        
        module_manager = ModuleManager()
        modules = module_manager.list_modules()
        
        print(f"✅ 模组管理器测试成功")
        print(f"  - 可用模组: {len(modules)} 个")
        for module_name in modules:
            print(f"  - {module_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模组管理器测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器是否正常工作"""
    try:
        from config.manager import ConfigManager
        
        config_manager = ConfigManager()
        groups = config_manager.get_groups()
        
        print(f"✅ 配置管理器测试成功")
        print(f"  - 配置组: {len(groups)} 个")
        for group_name in groups:
            print(f"  - {group_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复配置文件路径引用")
    print("="*50)
    
    # 1. 修复配置路径
    fix_config_paths()
    
    # 2. 确保配置文件存在
    ensure_config_files_exist()
    
    # 3. 测试模组管理器
    print("\n📦 测试模组管理器")
    print("-"*30)
    module_test = test_module_manager()
    
    # 4. 测试配置管理器
    print("\n⚙️ 测试配置管理器")
    print("-"*30)
    config_test = test_config_manager()
    
    # 5. 汇总结果
    print("\n" + "="*50)
    print("🎯 修复结果汇总")
    print("="*50)
    
    if module_test and config_test:
        print("✅ 所有配置路径修复成功！")
        print("✅ 模组管理器正常工作")
        print("✅ 配置管理器正常工作")
        print("\n🚀 现在可以正常使用应用了")
    else:
        print("❌ 仍有问题需要解决")
        if not module_test:
            print("  - 模组管理器有问题")
        if not config_test:
            print("  - 配置管理器有问题")

if __name__ == "__main__":
    main()
