from openai import OpenAI
import time
import asyncio
import crawler
import os  # 新增导入
from playwright.async_api import async_playwright

# 配置DeepSeek API (替换为你的实际API密钥)
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"
MODEL_NAME = "deepseek-chat"

# 初始化OpenAI客户端
client = OpenAI(api_key=DEEPSEEK_API_KEY, base_url=DEEPSEEK_BASE_URL)


async def analyze_page_with_playwright(url):
    """使用Playwright下载网页HTML页面"""
    print(f"正在使用Playwright分析页面: {url}")

    async with async_playwright() as p:
        # 使用crawler.py中的launch_browser函数
        browser, context, page = await crawler.launch_browser(
            p,
            headless=True,
            browser_type="chromium"
        )

        try:
            # 访问页面
            await page.goto(url, timeout=60000, wait_until="networkidle")
            await page.wait_for_load_state('networkidle', timeout=30000)

            # 等待页面加载
            await asyncio.sleep(3)

            # 获取页面基本信息
            page_title = await page.title()
            page_html = await page.content()

            print(f"已获取页面: {page_title}")

            return {
                'url': url,
                'title': page_title,
                'html': page_html
            }

        finally:
            await context.close()
            await browser.close()

def analyze_page_with_selenium(url):
    """兼容性包装函数，调用Playwright版本"""
    return asyncio.run(analyze_page_with_playwright(url))

def analyze_container_with_deepseek_list(page_data):
    """使用DeepSeek API分析容器结构，输出变量名=值格式"""
    print("AI正在努力找目标中。。。。")
    prompt = f"""
你是一个专业的网页解析助手。请分析以下页面结构，找出适合用于网页采集的主要容器，并直接输出其他采集程序所需的变量，格式为“变量名称=值”，每行一个变量。

list_container_selector 为主要列表所在容器
article_item_selector 为文章在主要列表中的容器

页面标题: {page_data['title']}
页面URL: {page_data['url']}
页面html: {page_data['html']}

请直接输出如下变量（如能判断）：
list_container_selector=
article_item_selector=

每个变量一行，值CSS选择器，若无法判断可留空。
请根据页面内容，给出一个最合适的AI文章采集配置，
并给出简要解释。例子：

======== 简要描述 =========
网站结构类型，网站结构类型，列表有没Js加载与特点，
翻页规则与总页数,换页的后缀名（如有）
定位容器置概况信度，等等


"""
    response = client.chat.completions.create(
        model=MODEL_NAME,
        messages=[{"role": "user", "content": prompt}],
        temperature=0.3,
        max_tokens=500
    )
    return response.choices[0].message.content

def analyze_container_with_deepseek_words(page_data):
    print("AI正在努力找寻中。。。。")
    """使用DeepSeek API分析容器结构，输出变量名=值格式"""
    
    # 清理HTML内容（新增处理逻辑）
    cleaned_html = clean_html(page_data['html'])
    
    prompt = f"""
你是一个专业的网页解析助手。请分析以下页面结构，找出适合用于网页采集的正文容器，并直接输出其他采集程序所需的变量
选择一个可能性最高的CSS选择器，格式为“变量名称=值”，每行一个变量。

list_container_selector 为主要列表所在容器
date_selector 为文章日期所在容器
source_selector 为文章来源所在容器
content_selectors 为文章内容所在容器
tilte_selector 为文章标题所在容器

页面标题: {page_data['title']}
页面URL: {page_data['url']}
页面html: {cleaned_html} 

请直接输出如下定位容器变量（如能判断）：
======== 以下为需要填写的变量 ========
date_selector=
source_selector=
content_selectors=
tilte_selector=
"""

    response = client.chat.completions.create(
        model=MODEL_NAME,
        messages=[{"role": "user", "content": prompt}],
        temperature=0.3,
        max_tokens=500
    )
    print("AI完成任务")
    return response.choices[0].message.content

# 新增HTML清理函数
def clean_html(html):
    """清理HTML内容，减少token使用量"""
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(html, 'html.parser')
    
    # 移除不需要的元素
    for tag in ['script', 'style', 'meta', 'link', 'noscript']:
        for element in soup.find_all(tag):
            element.decompose()
  

async def extract_first_article_url_playwright(
        list_url: str,
        article_item_selector: str,
        list_container_selector: str = "body",
        list_container_type: str = "CSS",
        article_item_type: str = "CSS",
        url_mode: str = "absolute",
        base_url: str = None
    ) -> str:
        """使用Playwright提取第一个文章URL"""
        async with async_playwright() as p:
            browser, context, page = await crawler.launch_browser(
                p,
                headless=True,
                browser_type="chromium"
            )

            try:
                # 使用crawler.py中的get_article_links_playwright函数
                _, _, article_links, _ = await crawler.get_article_links_playwright(
                    page, list_url,
                    list_container_selector or "body",
                    article_item_selector,
                    list_container_type,
                    article_item_type
                )

                for href in article_links[:3]:  # 最多尝试前3个链接
                    full_url = crawler.get_full_link(
                        href,
                        list_url,
                        base_url or list_url,
                        url_mode
                    )
                    if full_url:
                        return full_url

            except Exception as e:
                print(f"提取文章URL失败: {e}")

            finally:
                await context.close()
                await browser.close()

        return None

def extract_first_article_url(
        list_url: str,
        article_item_selector: str,
        list_container_selector: str = "body",
        list_container_type: str = "CSS",
        article_item_type: str = "CSS",
        url_mode: str = "absolute",
        base_url: str = None
    ) -> str:
        """兼容性包装函数，调用Playwright版本"""
        return asyncio.run(extract_first_article_url_playwright(
            list_url, article_item_selector, list_container_selector,
            list_container_type, article_item_type, url_mode, base_url
        ))

def test_deepseek_api():
    """简单的DeepSeek API测试函数"""
    print("正在测试DeepSeek API连接...")
    try:
        test_prompt = "请回复'API连接成功'以确认服务正常"
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": test_prompt}],
            temperature=0.1,
            max_tokens=20
        )
        result = response.choices[0].message.content
        print(f"API测试成功，响应: {result}")
        return True
    except Exception as e:
        print(f"API测试失败，错误: {str(e)}")
        return False

def save_ai_analysis_to_txt(page_title, content):
    """保存AI分析内容到以标题+时间命名的txt文件"""
    folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), "AI分析结果")
    if not os.path.exists(folder):
        os.makedirs(folder)
    now_str = time.strftime('%Y%m%d_%H%M%S')
    safe_title = "".join(c for c in page_title if c.isalnum() or c in (' ','-','_'))
    filename = f"{now_str}{safe_title}.txt"
    filepath = os.path.join(folder, filename)
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"AI分析内容已保存到: {filepath}")

def main(url):
    """主函数：执行分析流程"""
    # 新增API测试步骤
    if not test_deepseek_api():
        print("API连接失败，程序终止")
        return
    
    # 步骤1: 使用Playwright获取页面结构
    page_data = analyze_page_with_selenium(url)  # 保持函数名兼容性
    
    # 步骤2: 使用DeepSeek分析容器
    analysis_result = analyze_container_with_deepseek_list(page_data)
    
    print("\n===== 列表容器分析结果 =====")
    print(analysis_result)
    # 保存AI分析内容
    save_ai_analysis_to_txt(page_data['title'], analysis_result)
    # 解析AI返回的结果
    selectors = {}
    for line in analysis_result.splitlines():
        if '=' in line:
            key, value = line.split('=', 1)
            selectors[key.strip()] = value.strip()
    
    # 获取选择器值，如果没有则使用空字符串
    list_container_selector = selectors.get('list_container_selector', 'body')
    article_item_selector = selectors.get('article_item_selector', 'a[href*="article"]')
    
    # 调用提取函数
    if article_item_selector:
        article_url = extract_first_article_url(
            list_url=url,
            article_item_selector=article_item_selector,
            list_container_selector=list_container_selector
        )
        full_url = crawler.get_full_link(
            article_url,
            input_url=url,
            base_url=url, 
            url_mode="absolute"
        )
        
        print("\n===== 获取文章链接结果 =====")
        print(full_url)
    else:
        print("\n警告：未获取到有效的文章项选择器")
    page_data = analyze_page_with_selenium(article_url)  # 使用Playwright版本
    analysis_result2 = analyze_container_with_deepseek_words(page_data)
    print("\n===== 正文容器分析结果 =====")
    print(analysis_result2)
    save_ai_analysis_to_txt(page_data['title'], analysis_result2)

def main2(url):
    """主函数：执行分析流程"""
    # 新增API测试步骤
    if not test_deepseek_api():
        print("API连接失败，程序终止")
        return
    
    # 步骤1: 使用Selenium获取页面结构
    page_data = analyze_page_with_selenium(url)
    
    # 步骤2: 使用DeepSeek分析容器
    analysis_result2 = analyze_container_with_deepseek_words(page_data)
    print("\n===== 列表容器分析结果 =====")
    print(analysis_result2)
    save_ai_analysis_to_txt(page_data['title'], analysis_result2)



if __name__ == "__main__":
    # 支持命令行参数传递 input_url 和 article_url
    import sys
    input_url = None
    article_url = "https://www.shrd.gov.cn/n8347/n8483/u1ai271945.html"
    if len(sys.argv) > 1:
        for arg in sys.argv[1:]:
            if arg.startswith("input_url="):
                input_url = arg.split("=", 1)[1]
            elif arg.startswith("article_url="):
                article_url = arg.split("=", 1)[1]
    if input_url and not article_url:
        main(input_url)
    elif article_url and not input_url:
        main2(article_url)
    else:
        print("请只设置 input_url 或 article_url 其中之一。")