# 健壮等待策略迁移指南

## 概述
本指南帮助您将项目中的等待策略更新为新的健壮等待机制。

## 新的健壮等待策略特性
- 🔄 渐进式等待: networkidle -> domcontentloaded -> load -> commit
- 🎯 网站特定配置: 针对不同网站优化参数
- 📊 成功率统计: 记录各策略的成功率
- 🛡️ 错误恢复: 自动尝试多种策略

## 使用方法

### 1. 导入健壮等待策略
```python
from utils.robust_wait_strategy import robust_goto
```

### 2. 替换原有的 page.goto 调用
```python
# 原有方式
await page.goto(url, timeout=90000, wait_until='networkidle')
await page.wait_for_load_state('networkidle', timeout=45000)

# 新方式
success = await robust_goto(page, url)
if not success:
    # 处理失败情况
    return False
```

### 3. 自定义配置
```python
# 使用自定义配置
success = await robust_goto(
    page, 
    url,
    timeout=60000,
    preferred_strategy='domcontentloaded',
    extra_wait=3.0
)
```

## 网站特定配置

健壮等待策略会根据URL自动选择最适合的配置：

- **微信公众号** (mp.weixin.qq.com): 使用 domcontentloaded + 5秒等待
- **政府网站** (*.gov.cn): 使用 networkidle + 2秒等待
- **默认配置**: 使用 networkidle + 2秒等待

## 错误处理

```python
success = await robust_goto(page, url)
if not success:
    logger.error(f'所有等待策略都失败: {url}')
    # 记录失败URL或采取其他措施
    await save_failed_url(url, '页面访问失败')
    return False
```

## 成功率监控

```python
from utils.robust_wait_strategy import get_success_stats

# 获取成功率统计
stats = get_success_stats()
for domain, strategies in stats.items():
    print(f'{domain}: {strategies}')
```

## 注意事项

1. **向后兼容**: 如果健壮等待策略不可用，会自动回退到原有方式
2. **性能影响**: 新策略可能会增加页面加载时间，但提高成功率
3. **日志记录**: 建议启用详细日志以监控策略效果
4. **测试验证**: 更新后请运行测试确保功能正常

## 测试

运行测试脚本验证新策略:
```bash
python test_robust_wait_strategy.py
```