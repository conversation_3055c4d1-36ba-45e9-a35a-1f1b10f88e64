
📚 包结构使用指南

## 新的导入方式

### 核心功能
```python
from core import crawler
from core.crawler import get_article_links_playwright
from core.failed_url_processor import FailedUrlProcessor
```

### GUI界面
```python
from gui import main_window
from gui.main_window import CrawlerGUI
from gui.config_manager import GUIConfigManager
```

### AI分析
```python
from ai import analyzer, helper
from ai.analyzer import AIAnalyzerWithTesting
from ai.helper import EnhancedAIConfigManager
```

### 模组管理
```python
from modules import manager
from modules.manager import ModuleManager
```

### 测试功能
```python
from testing import selectors_test
from testing.selectors_test import SelectorsTestManager
```

### 配置管理
```python
from config import manager
from config.manager import ConfigManager
```

### 工具函数
```python
from utils import text_cleaner, playwright_config
from utils.text_cleaner import normalize_date
```

## 启动应用

### 方式1: 使用主入口
```bash
python main.py
```

### 方式2: 直接启动GUI
```python
from gui.main_window import main
main()
```

## 包结构优势

1. **模块化**: 功能清晰分离，易于维护
2. **可扩展**: 新功能可以独立开发
3. **可测试**: 每个包都可以独立测试
4. **可重用**: 包可以在其他项目中重用
