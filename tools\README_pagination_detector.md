# 网站翻页功能检测工具

## 📋 工具简介

网站翻页功能检测工具是一个专门用于分析网站翻页机制的自动化工具。它能够智能检测网站是否具有翻页功能，识别翻页类型，并提供相应的爬取建议。

## 🎯 主要功能

### 1. 翻页类型检测
- **按钮翻页**: 检测input、button等按钮类型的翻页
- **链接翻页**: 检测a标签链接类型的翻页
- **表单翻页**: 检测通过表单提交的翻页
- **JavaScript翻页**: 检测通过JS实现的翻页
- **无限滚动**: 检测滚动加载更多内容的翻页

### 2. 智能分析
- **页码信息提取**: 自动识别"第1/12页"等页码信息
- **选择器推荐**: 根据检测结果推荐最佳选择器
- **使用建议**: 提供具体的爬取策略建议

### 3. 多种使用方式
- **命令行版本**: 适合批量检测和脚本集成
- **GUI版本**: 提供友好的图形界面
- **API接口**: 可集成到其他项目中

## 🚀 使用方法

### 命令行版本

```bash
# 基本使用
python tools/pagination_detector.py https://example.com

# 检测天津市政协网站
python tools/pagination_detector.py https://www.tjszx.gov.cn/tagz/taxd/index.shtml
```

### GUI版本

```bash
# 启动图形界面
python tools/pagination_detector_gui.py
```

在GUI中：
1. 输入要检测的网站URL
2. 选择是否使用无头模式
3. 点击"开始检测"按钮
4. 查看详细的检测结果

### API使用

```python
from tools.pagination_detector import PaginationDetector
import asyncio

async def detect_website():
    detector = PaginationDetector()
    result = await detector.detect_pagination("https://example.com")
    
    if result['has_pagination']:
        print(f"翻页类型: {result['pagination_type']}")
        print(f"推荐选择器: {result['recommended_selectors']}")
    else:
        print("该网站没有翻页功能")

asyncio.run(detect_website())
```

## 📊 检测结果说明

### 结果结构

```json
{
  "url": "检测的网站URL",
  "has_pagination": true/false,
  "pagination_type": "翻页类型",
  "recommended_selectors": ["推荐的选择器列表"],
  "page_count_info": "页码信息",
  "suggestions": ["使用建议列表"],
  "details": {
    "buttons": "按钮检测详情",
    "links": "链接检测详情",
    "page_info": "页码信息详情",
    "forms": "表单检测详情",
    "scripts": "JavaScript检测详情",
    "infinite_scroll": "无限滚动检测详情"
  }
}
```

### 翻页类型说明

| 类型 | 说明 | 推荐爬取方式 |
|------|------|-------------|
| `button` | 按钮翻页 | 动态翻页模式 |
| `link` | 链接翻页 | 动态翻页或传统翻页 |
| `form` | 表单翻页 | 动态翻页模式 |
| `javascript` | JS翻页 | 动态翻页模式 |
| `infinite_scroll` | 无限滚动 | 滚动翻页模式 |
| `none` | 无翻页 | 单页面爬取 |

## 🔍 检测原理

### 1. 按钮检测
检测以下类型的翻页按钮：
- `input[value*='下一页']` - input按钮
- `button:has-text('下一页')` - button按钮
- `a:has-text('下一页')` - 链接按钮
- `.next`, `.page-next` - CSS类选择器

### 2. 页码信息检测
使用正则表达式匹配：
- `第\s*(\d+)\s*/\s*(\d+)\s*页` - 中文页码
- `Page\s*(\d+)\s*of\s*(\d+)` - 英文页码
- `(\d+)\s*/\s*(\d+)` - 数字页码

### 3. 链接模式检测
检测URL中的翻页模式：
- `page=`, `p=` - 页码参数
- `index_2.html` - 文件名模式
- `pagenum=`, `pn=` - 页码变量

### 4. JavaScript检测
分析页面源码中的JS模式：
- 翻页函数定义
- onclick事件处理
- location.href跳转

### 5. 无限滚动检测
通过滚动测试检测：
- 记录初始页面高度
- 滚动到底部
- 检查页面高度变化

## 💡 使用建议

### 针对不同网站类型

#### 政府网站
- 通常使用input按钮翻页
- 推荐选择器：`input[value='下一页']`
- 建议使用动态翻页模式

#### 新闻网站
- 多使用链接翻页或无限滚动
- 检查URL模式，可能支持传统翻页
- 注意反爬虫机制

#### 论坛网站
- 通常有明确的页码链接
- 推荐使用传统翻页模式
- URL模式：`page=2`, `p=2`等

#### 电商网站
- 多使用AJAX翻页或无限滚动
- 建议使用动态翻页或滚动翻页
- 注意加载等待时间

### 常见问题解决

#### 1. 检测不到翻页按钮
- 检查页面是否完全加载
- 尝试滚动到页面底部
- 检查是否在iframe中

#### 2. 翻页按钮不可点击
- 检查按钮是否被CSS隐藏
- 验证按钮的enabled状态
- 尝试不同的选择器

#### 3. 页面内容不变化
- 增加点击后等待时间
- 检查是否为AJAX加载
- 验证URL是否改变

## 🛠️ 扩展开发

### 添加新的检测模式

```python
async def _detect_custom_pagination(self, page) -> Dict:
    """自定义翻页检测"""
    # 实现自定义检测逻辑
    pass

# 在analyze_results中添加新的判断逻辑
```

### 自定义选择器

```python
# 在button_selectors中添加新的选择器
button_selectors = [
    "input[value*='下一页']",
    "your_custom_selector",  # 添加自定义选择器
    # ...
]
```

## 📝 更新日志

### v1.0.0 (2025-07-18)
- ✅ 初始版本发布
- ✅ 支持5种翻页类型检测
- ✅ 提供命令行和GUI两种使用方式
- ✅ 智能选择器推荐
- ✅ 详细的使用建议

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个工具：

1. **Bug报告**: 请提供详细的错误信息和复现步骤
2. **功能建议**: 描述新功能的用途和实现思路
3. **代码贡献**: 遵循现有代码风格，添加必要的测试

## 📄 许可证

本工具遵循MIT许可证，可自由使用和修改。

---

**注意**: 使用本工具时请遵守网站的robots.txt和使用条款，合理控制访问频率，避免对目标网站造成过大负担。
