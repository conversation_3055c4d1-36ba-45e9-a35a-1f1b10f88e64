#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel集成功能
验证重构后的Excel功能是否正常工作
"""

import os
import sys
import asyncio
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_excel_writer_import():
    """测试Excel写入器导入"""
    try:
        from core.excel_writer import ExcelWriter, ExcelWriteMode, get_excel_writer
        print("✅ Excel写入器导入成功")
        return True
    except ImportError as e:
        print(f"❌ Excel写入器导入失败: {e}")
        return False

def test_crawler_excel_integration():
    """测试crawler.py中的Excel集成"""
    try:
        from core.crawler import write_article_data_async, EXCEL_WRITER_AVAILABLE
        print(f"✅ Crawler Excel集成导入成功 (EXCEL_WRITER_AVAILABLE: {EXCEL_WRITER_AVAILABLE})")
        return True
    except ImportError as e:
        print(f"❌ Crawler Excel集成导入失败: {e}")
        return False

async def test_excel_write_functionality():
    """测试Excel写入功能"""
    try:
        from core.excel_writer import get_excel_writer, ExcelWriteMode
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        test_file = os.path.join(temp_dir, "test.xlsx")
        
        try:
            # 获取Excel写入器
            excel_writer = get_excel_writer()
            
            # 测试数据
            headers = ["标题", "链接", "日期", "来源"]
            test_data = [
                ["测试文章1", "http://example.com/1", "2024-01-01", "测试来源"],
                ["测试文章2", "http://example.com/2", "2024-01-02", "测试来源"],
            ]
            
            # 测试直接写入模式
            success1 = excel_writer.write(test_file, test_data[0], headers, ExcelWriteMode.DIRECT)
            success2 = excel_writer.write(test_file, test_data[1], headers, ExcelWriteMode.DIRECT)
            
            if success1 and success2:
                print("✅ Excel直接写入模式测试成功")
                
                # 检查文件是否存在
                if os.path.exists(test_file):
                    print(f"✅ Excel文件创建成功: {test_file}")
                    file_size = os.path.getsize(test_file)
                    print(f"   文件大小: {file_size} 字节")
                    return True
                else:
                    print("❌ Excel文件未创建")
                    return False
            else:
                print("❌ Excel写入失败")
                return False
                
        finally:
            # 清理临时文件
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                
    except Exception as e:
        print(f"❌ Excel写入功能测试失败: {e}")
        return False

async def test_crawler_write_function():
    """测试crawler中的写入函数"""
    try:
        from core.crawler import write_article_data_async
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        test_file = os.path.join(temp_dir, "test_crawler.xlsx")
        
        try:
            # 测试数据
            headers = ["标题", "链接", "日期", "来源"]
            test_data = ["测试文章", "http://example.com", "2024-01-01", "测试来源"]
            
            # 测试异步写入
            success = await write_article_data_async(
                test_file, test_data, headers, 
                file_format="EXCEL", strategy="direct"
            )
            
            if success:
                print("✅ Crawler异步Excel写入测试成功")
                
                # 检查文件是否存在
                if os.path.exists(test_file):
                    print(f"✅ Crawler Excel文件创建成功: {test_file}")
                    return True
                else:
                    print("❌ Crawler Excel文件未创建")
                    return False
            else:
                print("❌ Crawler异步Excel写入失败")
                return False
                
        finally:
            # 清理临时文件
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                
    except Exception as e:
        print(f"❌ Crawler写入函数测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 开始Excel集成测试...")
    print("=" * 50)
    
    # 测试导入
    test1 = test_excel_writer_import()
    test2 = test_crawler_excel_integration()
    
    if not (test1 and test2):
        print("❌ 导入测试失败，跳过功能测试")
        return False
    
    # 测试功能
    test3 = await test_excel_write_functionality()
    test4 = await test_crawler_write_function()
    
    print("=" * 50)
    
    all_passed = test1 and test2 and test3 and test4
    
    if all_passed:
        print("🎉 所有测试通过！Excel重构成功！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main())
