#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新模块测试文件
测试更新模块的各项功能是否正常工作
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_config_manager():
    """测试配置管理器的新功能"""
    print("=" * 50)
    print("🧪 测试配置管理器")
    print("=" * 50)
    
    try:
        from config.manager import ConfigManager
        
        # 创建测试配置管理器
        config_manager = ConfigManager("testing/test_config.json")
        
        # 测试分类管理
        print("\n📁 测试分类管理...")
        
        # 添加分类
        success = config_manager.add_category("政府网站", "政府相关网站配置")
        print(f"添加分类 '政府网站': {'✅' if success else '❌'}")
        
        success = config_manager.add_category("新闻媒体", "新闻媒体网站配置")
        print(f"添加分类 '新闻媒体': {'✅' if success else '❌'}")
        
        # 获取分类
        categories = config_manager.get_categories()
        print(f"当前分类数量: {len(categories)}")
        for name, info in categories.items():
            print(f"  - {name}: {info.get('description', '无描述')}")
        
        # 测试配置组管理
        print("\n📋 测试配置组管理...")
        
        # 添加配置组到指定分类
        test_config = {
            "input_url": "https://test.gov.cn",
            "title_selectors": [".title"],
            "content_selectors": [".content"]
        }
        
        success = config_manager.add_group("测试政府网站", test_config, "政府网站")
        print(f"添加配置组到政府网站分类: {'✅' if success else '❌'}")
        
        # 测试缓存管理
        print("\n💾 测试缓存管理...")
        
        # 添加URL到缓存
        success = config_manager.add_url_to_cache(
            "测试政府网站",
            "https://test.gov.cn/article1",
            "测试文章标题1"
        )
        print(f"添加URL到缓存: {'✅' if success else '❌'}")
        
        success = config_manager.add_url_to_cache(
            "测试政府网站",
            "https://test.gov.cn/article2",
            "测试文章标题2"
        )
        print(f"添加第二个URL到缓存: {'✅' if success else '❌'}")
        
        # 获取缓存信息
        cache = config_manager.get_config_cache("测试政府网站")
        if cache:
            print(f"缓存信息获取成功，URL记录数: {len(cache.get('last_urls', []))}")
            for i, url_info in enumerate(cache.get('last_urls', []), 1):
                print(f"  {i}. {url_info.get('title', '无标题')}")
        
        # 测试移动配置组
        print("\n🔄 测试移动配置组...")
        success = config_manager.move_config_to_category("测试政府网站", "新闻媒体")
        print(f"移动配置组到新闻媒体分类: {'✅' if success else '❌'}")
        
        # 验证移动结果
        configs_in_news = config_manager.get_configs_by_category("新闻媒体")
        print(f"新闻媒体分类下的配置组: {configs_in_news}")
        
        print("\n✅ 配置管理器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_update_manager():
    """测试更新管理器"""
    print("\n" + "=" * 50)
    print("🧪 测试更新管理器")
    print("=" * 50)
    
    try:
        from core.update_manager import UpdateManager
        
        # 创建更新管理器
        update_manager = UpdateManager()
        
        # 测试获取分类信息
        print("\n📊 测试获取分类信息...")
        categories = update_manager.get_all_categories()
        print(f"获取到 {len(categories)} 个分类")
        
        for category_name, category_data in categories.items():
            configs = category_data.get("configs", [])
            print(f"  📁 {category_name}: {len(configs)} 个配置组")
            for config_info in configs[:3]:  # 只显示前3个
                name = config_info["name"]
                last_update = config_info.get("last_update")
                total_articles = config_info.get("total_articles", 0)
                print(f"    - {name}: {total_articles} 篇文章, 最后更新: {last_update or '从未'}")
        
        # 测试获取配置组更新信息
        print("\n🔍 测试获取配置组更新信息...")
        if categories:
            # 获取第一个配置组进行测试
            first_category = next(iter(categories.values()))
            if first_category["configs"]:
                test_config_name = first_category["configs"][0]["name"]
                update_info = update_manager.get_config_update_info(test_config_name)
                print(f"配置组 '{test_config_name}' 更新信息:")
                print(f"  - 可以更新: {update_info['can_update']}")
                print(f"  - 更新原因: {update_info['update_reason']}")
                print(f"  - 文章总数: {update_info['total_articles']}")
                print(f"  - 成功率: {update_info['success_rate']:.1%}")
        
        print("\n✅ 更新管理器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 更新管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_integration():
    """测试GUI集成"""
    print("\n" + "=" * 50)
    print("🧪 测试GUI集成")
    print("=" * 50)
    
    try:
        # 测试更新标签页导入
        print("\n📱 测试更新标签页导入...")
        from gui.update_tab import UpdateTab, UpdateThread
        print("✅ 更新标签页导入成功")
        
        # 测试主窗口集成
        print("\n🖥️ 测试主窗口集成...")
        # 这里只测试导入，不创建实际的GUI实例
        from gui.main_window import CrawlerGUI
        print("✅ 主窗口导入成功")
        
        print("\n✅ GUI集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_migration():
    """测试配置迁移功能"""
    print("\n" + "=" * 50)
    print("🧪 测试配置迁移功能")
    print("=" * 50)
    
    try:
        # 创建旧格式配置文件
        old_config = {
            "last_used": "test_config",
            "groups": {
                "test_config": {
                    "input_url": "https://example.com",
                    "title_selectors": [".title"],
                    "content_selectors": [".content"]
                },
                "another_config": {
                    "input_url": "https://another.com",
                    "title_selectors": [".h1"],
                    "content_selectors": [".main"]
                }
            }
        }
        
        # 保存旧格式配置
        test_config_file = "testing/test_migration_config.json"
        os.makedirs("testing", exist_ok=True)
        with open(test_config_file, 'w', encoding='utf-8') as f:
            json.dump(old_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 创建旧格式配置文件: {test_config_file}")
        
        # 加载配置（应该触发迁移）
        from config.manager import ConfigManager
        config_manager = ConfigManager(test_config_file)
        
        # 验证迁移结果
        categories = config_manager.get_categories()
        print(f"迁移后分类数量: {len(categories)}")
        
        groups = config_manager.config.get("groups", {})
        print(f"迁移后配置组数量: {len(groups)}")
        
        # 检查是否添加了缓存结构
        for group_name, group_config in groups.items():
            if "cache" in group_config:
                print(f"✅ 配置组 '{group_name}' 已添加缓存结构")
            else:
                print(f"❌ 配置组 '{group_name}' 缺少缓存结构")
            
            if "category" in group_config:
                print(f"✅ 配置组 '{group_name}' 已添加分类信息: {group_config['category']}")
            else:
                print(f"❌ 配置组 '{group_name}' 缺少分类信息")
        
        # 清理测试文件
        if os.path.exists(test_config_file):
            os.remove(test_config_file)
            print(f"🗑️ 清理测试文件: {test_config_file}")
        
        print("\n✅ 配置迁移测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置迁移测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始更新模块功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 确保测试目录存在
    os.makedirs("testing", exist_ok=True)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("配置管理器", test_config_manager()))
    test_results.append(("更新管理器", test_update_manager()))
    test_results.append(("GUI集成", test_gui_integration()))
    test_results.append(("配置迁移", test_config_migration()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！更新模块功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
