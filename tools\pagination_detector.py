#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网站翻页功能检测工具
用于检测网站是否有翻页功能，并提供合适的建议
"""

import asyncio
import sys
import os
import re
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from playwright.async_api import async_playwright
from core.crawler import launch_browser

class PaginationDetector:
    """网站翻页功能检测器"""
    
    def __init__(self):
        self.pagination_indicators = {
            'buttons': [],
            'links': [],
            'page_info': [],
            'forms': [],
            'scripts': []
        }
        
    async def detect_pagination(self, url: str, headless: bool = False) -> Dict:
        """
        检测网站的翻页功能
        
        Args:
            url: 要检测的网站URL
            headless: 是否使用无头模式
            
        Returns:
            检测结果字典
        """
        print(f"🔍 检测网站翻页功能: {url}")
        print("=" * 60)
        
        result = {
            'url': url,
            'has_pagination': False,
            'pagination_type': 'none',
            'recommended_selectors': [],
            'page_count_info': None,
            'suggestions': [],
            'details': {}
        }
        
        async with async_playwright() as p:
            browser, context, page = await launch_browser(p, headless=headless)
            
            try:
                # 访问页面
                await page.goto(url, timeout=60000)
                await page.wait_for_load_state('networkidle', timeout=30000)
                await page.wait_for_timeout(5000)
                
                print("✅ 页面加载完成")
                
                # 滚动到页面底部，确保所有内容加载
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await page.wait_for_timeout(3000)
                
                # 获取页面内容
                html_content = await page.content()
                page_text = await page.text_content("body")
                
                # 1. 检测翻页按钮
                buttons_result = await self._detect_pagination_buttons(page)
                result['details']['buttons'] = buttons_result
                
                # 2. 检测页码信息
                page_info_result = await self._detect_page_info(page, page_text)
                result['details']['page_info'] = page_info_result
                
                # 3. 检测翻页链接
                links_result = await self._detect_pagination_links(page)
                result['details']['links'] = links_result
                
                # 4. 检测表单翻页
                forms_result = await self._detect_pagination_forms(page)
                result['details']['forms'] = forms_result
                
                # 5. 检测JavaScript翻页
                scripts_result = await self._detect_javascript_pagination(html_content)
                result['details']['scripts'] = scripts_result
                
                # 6. 检测无限滚动
                scroll_result = await self._detect_infinite_scroll(page)
                result['details']['infinite_scroll'] = scroll_result
                
                # 综合分析结果
                result = await self._analyze_results(result)
                
                # 生成建议
                result['suggestions'] = self._generate_suggestions(result)
                
                print("\n📊 检测结果:")
                print(f"  翻页功能: {'✅ 有' if result['has_pagination'] else '❌ 无'}")
                print(f"  翻页类型: {result['pagination_type']}")
                
                if result['recommended_selectors']:
                    print("  推荐选择器:")
                    for selector in result['recommended_selectors']:
                        print(f"    - {selector}")
                
                if result['suggestions']:
                    print("  建议:")
                    for suggestion in result['suggestions']:
                        print(f"    💡 {suggestion}")
                
                return result
                
            except Exception as e:
                print(f"❌ 检测过程中出错: {e}")
                result['error'] = str(e)
                return result
                
            finally:
                await context.close()
                await browser.close()
    
    async def _detect_pagination_buttons(self, page) -> Dict:
        """检测翻页按钮"""
        print("🔍 检测翻页按钮...")
        
        button_selectors = [
            "input[value*='下一页']",
            "input[value*='next']",
            "input[value*='Next']",
            "input[value*='>']",
            "button:has-text('下一页')",
            "button:has-text('next')",
            "button:has-text('Next')",
            "button:has-text('>')",
            "a:has-text('下一页')",
            "a:has-text('next')",
            "a:has-text('Next')",
            "a:has-text('>')",
            ".next",
            ".page-next",
            ".pagination-next"
        ]
        
        found_buttons = []
        
        for selector in button_selectors:
            try:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    is_visible = await element.is_visible()
                    is_enabled = await element.is_enabled()
                    
                    if is_visible and is_enabled:
                        tag_name = await element.evaluate("el => el.tagName.toLowerCase()")
                        
                        button_info = {
                            'selector': selector,
                            'tag': tag_name,
                            'visible': is_visible,
                            'enabled': is_enabled
                        }
                        
                        if tag_name == 'input':
                            button_info['value'] = await element.get_attribute('value')
                            button_info['type'] = await element.get_attribute('type')
                        else:
                            button_info['text'] = await element.text_content()
                            button_info['href'] = await element.get_attribute('href')
                        
                        found_buttons.append(button_info)
                        print(f"  ✅ 找到按钮: {selector}")
                        
            except Exception:
                continue
        
        return {
            'count': len(found_buttons),
            'buttons': found_buttons
        }
    
    async def _detect_page_info(self, page, page_text: str) -> Dict:
        """检测页码信息"""
        print("🔍 检测页码信息...")
        
        # 页码信息的正则模式
        page_patterns = [
            r'第\s*(\d+)\s*/\s*(\d+)\s*页',
            r'第\s*(\d+)\s*页',
            r'(\d+)\s*/\s*(\d+)\s*页',
            r'Page\s*(\d+)\s*of\s*(\d+)',
            r'(\d+)\s*/\s*(\d+)',
            r'共\s*(\d+)\s*页',
            r'总共\s*(\d+)\s*页'
        ]
        
        page_info = []
        
        for pattern in page_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                page_info.extend(matches)
                print(f"  ✅ 找到页码信息: {matches}")
        
        return {
            'found': len(page_info) > 0,
            'patterns': page_info
        }
    
    async def _detect_pagination_links(self, page) -> Dict:
        """检测翻页链接"""
        print("🔍 检测翻页链接...")
        
        link_patterns = [
            "a[href*='page']",
            "a[href*='index_']",
            "a[href*='p=']",
            "a[href*='pagenum']",
            "a[href*='pn=']"
        ]
        
        found_links = []
        
        for pattern in link_patterns:
            try:
                elements = await page.query_selector_all(pattern)
                for element in elements:
                    is_visible = await element.is_visible()
                    if is_visible:
                        href = await element.get_attribute('href')
                        text = await element.text_content()
                        
                        found_links.append({
                            'selector': pattern,
                            'href': href,
                            'text': text.strip() if text else ''
                        })
                        
                if elements:
                    print(f"  ✅ 找到翻页链接: {pattern} ({len(elements)} 个)")
                    
            except Exception:
                continue
        
        return {
            'count': len(found_links),
            'links': found_links
        }
    
    async def _detect_pagination_forms(self, page) -> Dict:
        """检测表单翻页"""
        print("🔍 检测表单翻页...")
        
        forms = await page.query_selector_all("form")
        pagination_forms = []
        
        for form in forms:
            try:
                # 检查表单中是否有页码相关的输入
                inputs = await form.query_selector_all("input")
                has_page_input = False
                
                for input_elem in inputs:
                    name = await input_elem.get_attribute('name')
                    value = await input_elem.get_attribute('value')
                    
                    if name and any(keyword in name.lower() for keyword in ['page', 'pn', 'pagenum']):
                        has_page_input = True
                        break
                    
                    if value and any(keyword in value for keyword in ['下一页', 'next', '确定']):
                        has_page_input = True
                        break
                
                if has_page_input:
                    action = await form.get_attribute('action')
                    method = await form.get_attribute('method')
                    
                    pagination_forms.append({
                        'action': action,
                        'method': method,
                        'inputs': len(inputs)
                    })
                    
            except Exception:
                continue
        
        if pagination_forms:
            print(f"  ✅ 找到表单翻页: {len(pagination_forms)} 个")
        
        return {
            'count': len(pagination_forms),
            'forms': pagination_forms
        }
    
    async def _detect_javascript_pagination(self, html_content: str) -> Dict:
        """检测JavaScript翻页"""
        print("🔍 检测JavaScript翻页...")
        
        js_patterns = [
            r'function\s+\w*[Pp]age\w*\s*\(',
            r'function\s+\w*[Nn]ext\w*\s*\(',
            r'onclick\s*=\s*["\'][^"\']*[Pp]age[^"\']*["\']',
            r'onclick\s*=\s*["\'][^"\']*[Nn]ext[^"\']*["\']',
            r'location\.href\s*=.*page',
            r'window\.location.*page'
        ]
        
        js_matches = []
        
        for pattern in js_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                js_matches.extend(matches)
                print(f"  ✅ 找到JS翻页模式: {len(matches)} 个")
        
        return {
            'found': len(js_matches) > 0,
            'patterns': js_matches[:5]  # 只保留前5个
        }
    
    async def _detect_infinite_scroll(self, page) -> Dict:
        """检测无限滚动"""
        print("🔍 检测无限滚动...")
        
        # 获取初始页面高度
        initial_height = await page.evaluate("document.body.scrollHeight")
        
        # 滚动到底部
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await page.wait_for_timeout(3000)
        
        # 检查页面高度是否改变
        new_height = await page.evaluate("document.body.scrollHeight")
        
        has_infinite_scroll = new_height > initial_height
        
        if has_infinite_scroll:
            print("  ✅ 检测到无限滚动")
        
        return {
            'detected': has_infinite_scroll,
            'initial_height': initial_height,
            'final_height': new_height
        }
    
    async def _analyze_results(self, result: Dict) -> Dict:
        """分析检测结果"""
        details = result['details']
        
        # 判断是否有翻页功能
        has_buttons = details['buttons']['count'] > 0
        has_page_info = details['page_info']['found']
        has_links = details['links']['count'] > 0
        has_forms = details['forms']['count'] > 0
        has_js = details['scripts']['found']
        has_scroll = details['infinite_scroll']['detected']
        
        if has_buttons or has_links or has_forms:
            result['has_pagination'] = True
            if has_buttons:
                result['pagination_type'] = 'button'
                # 生成推荐选择器
                for button in details['buttons']['buttons']:
                    result['recommended_selectors'].append(button['selector'])
            elif has_links:
                result['pagination_type'] = 'link'
            elif has_forms:
                result['pagination_type'] = 'form'
        elif has_scroll:
            result['has_pagination'] = True
            result['pagination_type'] = 'infinite_scroll'
        elif has_js:
            result['has_pagination'] = True
            result['pagination_type'] = 'javascript'
        else:
            result['has_pagination'] = False
            result['pagination_type'] = 'none'
        
        # 提取页码信息
        if has_page_info:
            patterns = details['page_info']['patterns']
            if patterns:
                result['page_count_info'] = patterns[0]
        
        return result
    
    def _generate_suggestions(self, result: Dict) -> List[str]:
        """生成使用建议"""
        suggestions = []
        
        if not result['has_pagination']:
            suggestions.extend([
                "该页面可能是单页面，没有翻页功能",
                "建议检查是否有其他页面包含更多内容",
                "可以尝试使用传统翻页模式，检查是否有index_2.html等URL"
            ])
        else:
            pagination_type = result['pagination_type']
            
            if pagination_type == 'button':
                suggestions.extend([
                    "建议使用动态翻页模式",
                    f"推荐选择器: {result['recommended_selectors'][0] if result['recommended_selectors'] else 'input[value=\"下一页\"]'}",
                    "设置适当的等待时间: wait_after_click=3000"
                ])
            elif pagination_type == 'link':
                suggestions.extend([
                    "建议使用动态翻页模式或传统翻页模式",
                    "检查链接URL模式，可能支持传统翻页"
                ])
            elif pagination_type == 'form':
                suggestions.extend([
                    "该网站使用表单翻页，建议使用动态翻页模式",
                    "可能需要填写页码并提交表单"
                ])
            elif pagination_type == 'infinite_scroll':
                suggestions.extend([
                    "该网站使用无限滚动，建议使用滚动翻页模式",
                    "设置合适的滚动间隔和等待时间"
                ])
            elif pagination_type == 'javascript':
                suggestions.extend([
                    "该网站使用JavaScript翻页，建议使用动态翻页模式",
                    "可能需要特殊的选择器或等待策略"
                ])
        
        return suggestions

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python pagination_detector.py <URL>")
        print("示例: python pagination_detector.py https://www.tjszx.gov.cn/tagz/taxd/index.shtml")
        return
    
    url = sys.argv[1]
    detector = PaginationDetector()
    
    result = await detector.detect_pagination(url, headless=False)
    
    # 保存检测报告
    report_filename = f"pagination_detection_report_{url.replace('://', '_').replace('/', '_')}.json"
    
    import json
    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 检测报告已保存: {report_filename}")

if __name__ == "__main__":
    asyncio.run(main())
