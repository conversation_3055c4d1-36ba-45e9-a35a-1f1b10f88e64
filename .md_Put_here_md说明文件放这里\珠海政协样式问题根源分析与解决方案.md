# 珠海政协网站样式内容误识别问题 - 根源分析与解决方案

## 🔍 问题描述

在爬取珠海政协网站 `http://www.zhzx.gov.cn/zxyw/202306/t20230609_58927130.html` 时，正文内容中经常包含CSS样式代码：

```css
.TRS_Editor P{line-height:1.75;font-family:宋体;font-size:12pt;}
.TRS_Editor DIV{line-height:1.75;font-family:宋体;font-size:12pt;}
.TRS_Editor TD{line-height:1.75;font-family:宋体;font-size:12pt;}
...
```

## 🎯 根源分析

### 1. 配置问题（主要原因）

**问题配置：**
```json
{
    "content_selector": "div.article_con p",
    "content_type": "CSS"
}
```

**问题分析：**
- 使用了过于宽泛的选择器 `div.article_con p`
- 该选择器会匹配到包含样式定义的 `<p>` 标签
- 没有使用专门针对TRS编辑器的选择器

### 2. 网页结构分析

珠海政协网站使用TRS内容管理系统，典型结构：
```html
<div class="article_con">
    <style type="text/css">
        .TRS_Editor P{...}
        .TRS_Editor DIV{...}
        ...
    </style>
    
    <div class="TRS_Editor">
        <p>实际正文内容...</p>
        <p>更多正文内容...</p>
    </div>
</div>
```

### 3. 选择器匹配问题

- `div.article_con p` 会匹配到：
  - ✅ 正文中的 `<p>` 标签（正确）
  - ❌ 样式定义中的文本内容（错误）

## 🛠️ 解决方案

### 1. 更新配置文件

**修复前：**
```json
{
    "content_selector": "div.article_con p",
    "content_type": "CSS"
}
```

**修复后：**
```json
{
    "content_selectors": [
        ".TRS_Editor",
        "div.view.TRS_UEDITOR.trs_paper_default.trs_web",
        ".article_cont",
        "div[class*='content']",
        "div.article_con",
        "div.zhengwen"
    ],
    "content_type": "CSS"
}
```

### 2. 选择器优先级说明

1. **`.TRS_Editor`** - 最精确，直接定位TRS编辑器内容区域
2. **`div.view.TRS_UEDITOR.trs_paper_default.trs_web`** - TRS系统的标准容器
3. **`.article_cont`** - 通用文章内容容器
4. **`div[class*='content']`** - 包含content的div元素
5. **`div.article_con`** - 原始容器（但不包含p标签）
6. **`div.zhengwen`** - 正文容器的备选

### 3. 代码层面的改进

#### A. HTML预清理函数
```python
def clean_html_before_text_extraction(html_content):
    """在提取文本内容之前清理HTML，移除样式标签"""
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 移除所有style标签及其内容
    for style_tag in soup.find_all('style'):
        style_tag.decompose()
    
    # 移除所有script标签及其内容  
    for script_tag in soup.find_all('script'):
        script_tag.decompose()
        
    return str(soup)
```

#### B. 爬虫代码改进
```python
# 修复前：直接提取文本
content_text = await content_elem.text_content()

# 修复后：先清理HTML再提取文本
content_html = await content_elem.inner_html()
cleaned_html = clean_html_before_text_extraction(content_html)
temp_soup = BeautifulSoup(cleaned_html, "html.parser")
content_text = temp_soup.get_text(separator='\n', strip=True)
```

#### C. 增强的CSS清理
```python
def clean_html_tags(text):
    """改进的HTML标签清理函数"""
    # 原有的style标签清理...
    
    # 新增：清理残留的CSS规则
    css_rule_pattern = re.compile(r'\.TRS_Editor\s+[A-Z]+\{[^}]*\}', re.IGNORECASE)
    text = css_rule_pattern.sub('', text)
    
    # 更通用的CSS规则清理
    general_css_pattern = re.compile(r'\.[A-Za-z_][A-Za-z0-9_-]*\s+[A-Z]+\{[^}]*\}', re.IGNORECASE)
    text = general_css_pattern.sub('', text)
```

## 🎯 解决方案的优势

### 1. 根本性解决
- **精确定位**：使用 `.TRS_Editor` 直接定位正文区域
- **避免误匹配**：不再选择包含样式的元素
- **多重保障**：提供多个备选选择器

### 2. 鲁棒性强
- **兼容性**：支持新旧配置格式
- **容错性**：多个选择器按优先级尝试
- **通用性**：适用于其他TRS系统网站

### 3. 可维护性
- **配置驱动**：通过配置文件调整，无需修改代码
- **模块化**：清理函数独立，可复用
- **可扩展**：易于添加新的选择器

## 📊 测试验证

### 测试脚本
```bash
python test_zhzx_fix.py
```

### 预期结果
- ✅ `.TRS_Editor` 选择器成功提取正文
- ✅ 提取的内容不包含CSS样式代码
- ✅ 内容长度合理（>500字符）
- ❌ `div.article_con p` 确认包含样式内容

## 🔄 向后兼容

- ✅ 保持原有功能不变
- ✅ 支持单选择器和多选择器配置
- ✅ 自动兼容新旧配置格式
- ✅ 不影响其他网站的爬取

## 📝 总结

这个问题的根源在于**选择器配置不够精确**，而不是文本清理算法的问题。通过：

1. **更新配置**：使用更精确的选择器
2. **代码改进**：增强HTML预清理
3. **多重保障**：提供备选方案

可以从根本上解决珠海政协网站样式内容误识别的问题，同时保持系统的鲁棒性和可维护性。
