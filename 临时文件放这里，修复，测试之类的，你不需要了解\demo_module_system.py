#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模组配置系统演示脚本
展示模组配置系统的主要功能
"""

import os
import json

def demo_module_matching():
    """演示模组匹配功能"""
    print("="*60)
    print("模组匹配功能演示")
    print("="*60)
    
    # 测试URL列表
    test_urls = [
        "https://mp.weixin.qq.com/s/2EGonPvQhmqoEKszLUxZCg",
        "https://mp.weixin.qq.com/s/XYRoerLntbPc16EjcBwERg", 
        "https://www.shrd.gov.cn/n8347/n8378/u1ai270696.html",
        "https://www.shrd.gov.cn/n8347/n8861/n9893/n9894/u1ai270910.html",
        "https://www.example.gov.cn/news/article.html",
        "https://news.example.com/article.html"
    ]
    
    try:
        from module_manager import match_module_for_url, get_config_for_url
        
        print("测试URL匹配结果:")
        print("-"*60)
        
        for i, url in enumerate(test_urls, 1):
            print(f"{i}. URL: {url}")
            
            # 匹配模组
            module_name = match_module_for_url(url)
            print(f"   匹配模组: {module_name}")
            
            # 获取配置
            config = get_config_for_url(url)
            if config:
                print(f"   内容选择器: {config.get('content_selectors', [])}")
                print(f"   标题选择器: {config.get('title_selectors', [])}")
                print(f"   爬取模式: {config.get('mode', 'balance')}")
                print(f"   重试次数: {config.get('retry', 2)}")
            else:
                print("   未获取到配置")
            print()
            
    except ImportError as e:
        print(f"无法导入模组管理器: {e}")
        print("请确保 module_manager.py 文件存在且正确")

def demo_config_structure():
    """演示配置文件结构"""
    print("="*60)
    print("配置文件结构演示")
    print("="*60)
    
    config_file = "module_configs.json"
    
    if not os.path.exists(config_file):
        print(f"配置文件不存在: {config_file}")
        return
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        print(f"配置文件: {config_file}")
        print(f"默认模组: {config_data.get('default_module', '未设置')}")
        print(f"匹配优先级: {config_data.get('match_priority', [])}")
        print()
        
        # 显示各个模组的基本信息
        print("模组列表:")
        print("-"*40)
        
        for module_name, module_config in config_data.items():
            if module_name in ['default_module', 'match_priority']:
                continue
            
            print(f"模组名称: {module_name}")
            print(f"描述: {module_config.get('description', '无描述')}")
            print(f"域名模式: {module_config.get('domain_patterns', [])}")
            print(f"URL模式: {module_config.get('url_patterns', [])}")
            
            config = module_config.get('config', {})
            if config:
                print(f"内容选择器数量: {len(config.get('content_selectors', []))}")
                print(f"爬取模式: {config.get('mode', 'balance')}")
            print("-"*40)
            
    except Exception as e:
        print(f"读取配置文件失败: {e}")

def demo_failed_url_info():
    """演示失败URL信息"""
    print("="*60)
    print("失败URL文件信息")
    print("="*60)
    
    articles_dir = "articles"
    if not os.path.exists(articles_dir):
        print(f"文章目录不存在: {articles_dir}")
        return
    
    # 查找失败文件
    failed_files = []
    for file in os.listdir(articles_dir):
        if file.endswith("_failed.csv"):
            failed_files.append(os.path.join(articles_dir, file))
    
    if not failed_files:
        print("没有找到失败URL文件")
        return
    
    print(f"找到 {len(failed_files)} 个失败URL文件:")
    print("-"*40)
    
    for failed_file in failed_files:
        print(f"文件: {failed_file}")
        
        try:
            import csv
            with open(failed_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
            
            total_count = len(rows)
            retry_count = sum(1 for row in rows if row.get('status') == '待重试')
            
            print(f"  总失败数: {total_count}")
            print(f"  待重试数: {retry_count}")
            
            # 分析失败原因
            reasons = {}
            for row in rows:
                reason = row.get('reason', '未知')
                reasons[reason] = reasons.get(reason, 0) + 1
            
            print("  失败原因统计:")
            for reason, count in reasons.items():
                print(f"    {reason}: {count} 次")
            
            # 分析URL类型
            url_types = {}
            for row in rows:
                url = row.get('failed_url', '')
                if 'mp.weixin.qq.com' in url:
                    url_type = '微信公众号'
                elif 'shrd.gov.cn' in url:
                    url_type = '上海人大'
                elif '.gov.cn' in url:
                    url_type = '政府网站'
                else:
                    url_type = '其他'
                url_types[url_type] = url_types.get(url_type, 0) + 1
            
            print("  URL类型统计:")
            for url_type, count in url_types.items():
                print(f"    {url_type}: {count} 个")
            
        except Exception as e:
            print(f"  读取文件失败: {e}")
        
        print("-"*40)

def demo_usage_examples():
    """演示使用示例"""
    print("="*60)
    print("使用示例")
    print("="*60)
    
    print("1. 启动配置管理界面:")
    print("   python module_config_manager.py")
    print()
    
    print("2. 处理失败URL文件:")
    print("   from failed_url_processor import process_failed_csv")
    print("   result = process_failed_csv(")
    print("       failed_csv_path='articles/上海人大_代表风采_failed.csv',")
    print("       save_dir='articles',")
    print("       export_filename='重试结果',")
    print("       file_format='CSV',")
    print("       classid='3802',")
    print("       retry=3,")
    print("       max_workers=3")
    print("   )")
    print()
    
    print("3. 测试URL匹配:")
    print("   from module_manager import match_module_for_url")
    print("   url = 'https://mp.weixin.qq.com/s/2EGonPvQhmqoEKszLUxZCg'")
    print("   module_name = match_module_for_url(url)")
    print("   print(f'URL匹配到模组: {module_name}')")
    print()
    
    print("4. 获取URL配置:")
    print("   from module_manager import get_config_for_url")
    print("   config = get_config_for_url(url)")
    print("   print(f'配置: {config}')")
    print()
    
    print("5. 在爬虫中使用模组配置:")
    print("   from crawler import save_article")
    print("   success = save_article(")
    print("       link=url,")
    print("       save_dir='articles',")
    print("       page_title='测试文章',")
    print("       content_selectors=[],  # 将被模组配置覆盖")
    print("       use_module_config=True  # 启用模组配置")
    print("   )")

def main():
    """主函数"""
    print("模组配置系统功能演示")
    print("="*60)
    print("这个演示将展示模组配置系统的主要功能")
    print()
    
    # 演示各个功能
    demo_config_structure()
    print()
    
    demo_module_matching()
    print()
    
    demo_failed_url_info()
    print()
    
    demo_usage_examples()
    
    print("\n" + "="*60)
    print("演示完成")
    print("="*60)
    print("模组配置系统已经成功集成到爬虫中！")
    print()
    print("主要特性:")
    print("✓ 根据URL自动选择合适的爬虫配置")
    print("✓ 支持微信公众号、政府网站等特定配置")
    print("✓ 失败URL自动重试功能")
    print("✓ 可视化配置管理界面")
    print("✓ 完全向后兼容现有代码")
    print()
    print("现在您可以:")
    print("1. 运行 python module_config_manager.py 管理配置")
    print("2. 使用现有爬虫，系统会自动应用合适的配置")
    print("3. 处理失败的URL文件，提高爬取成功率")

if __name__ == "__main__":
    main()
