#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健壮的等待策略工具
实现渐进式等待策略，提高网站访问的成功率
"""

import asyncio
import logging
from typing import Optional, Dict, Any, List
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

class RobustWaitStrategy:
    """健壮的等待策略类"""
    
    # 等待策略优先级（从最理想到最基础）
    WAIT_STRATEGIES = [
        "networkidle",      # 网络空闲（最理想，但可能超时）
        "domcontentloaded", # DOM内容加载完成（平衡选择）
        "load",             # 基本加载完成（较快但可能不完整）
        "commit"            # 页面提交（最基础，几乎总是成功）
    ]
    
    # 网站特定配置
    SITE_CONFIGS = {
        "mp.weixin.qq.com": {
            "timeout": 60000,
            "preferred_strategy": "domcontentloaded",
            "extra_wait": 5.0,
            "max_retries": 3,
            "wait_for_selector": "#js_content",
            "wait_for_selector_timeout": 10000
        },
        "shrd.gov.cn": {
            "timeout": 45000,
            "preferred_strategy": "networkidle",
            "extra_wait": 2.0,
            "max_retries": 2,
            "wait_for_selector": None,
            "wait_for_selector_timeout": 5000
        },
        "default": {
            "timeout": 90000,
            "preferred_strategy": "networkidle",
            "extra_wait": 2.0,
            "max_retries": 3,
            "wait_for_selector": None,
            "wait_for_selector_timeout": 5000
        }
    }
    
    def __init__(self):
        self.success_stats = {}  # 记录各策略的成功率
    
    def get_site_config(self, url: str) -> Dict[str, Any]:
        """根据URL获取网站特定配置"""
        domain = urlparse(url).netloc.lower()
        
        for site_pattern, config in self.SITE_CONFIGS.items():
            if site_pattern != "default" and site_pattern in domain:
                return config.copy()
        
        return self.SITE_CONFIGS["default"].copy()
    
    async def robust_goto(self, page: Page, url: str, **kwargs) -> bool:
        """
        健壮的页面访问函数

        Args:
            page: Playwright页面对象
            url: 目标URL
            **kwargs: 额外参数，可覆盖默认配置

        Returns:
            bool: 是否访问成功
        """
        # 首先检查页面是否有效
        if page.is_closed():
            logger.error(f"页面已关闭，无法访问: {url}")
            return False

        site_config = self.get_site_config(url)

        # 合并用户提供的参数
        config = {**site_config, **kwargs}

        logger.info(f"开始访问页面: {url}")
        logger.debug(f"使用配置: {config}")

        # 确定要尝试的等待策略列表
        strategies = self._get_strategies_to_try(config.get("preferred_strategy"))

        last_error = None

        for attempt, strategy in enumerate(strategies):
            try:
                # 在每次尝试前检查页面状态
                if page.is_closed():
                    logger.error(f"页面在策略 {strategy} 尝试前被关闭")
                    last_error = Exception("页面被关闭")
                    break

                logger.info(f"尝试策略 {attempt + 1}/{len(strategies)}: {strategy}")

                # 执行页面访问
                success = await self._try_goto_with_strategy(
                    page, url, strategy, config
                )

                if success:
                    logger.info(f"✅ 页面访问成功，使用策略: {strategy}")
                    self._record_success(url, strategy)
                    return True

            except Exception as e:
                last_error = e
                error_msg = str(e)

                # 特殊处理页面关闭错误
                if "closed" in error_msg.lower():
                    logger.error(f"❌ 页面或浏览器被关闭: {error_msg}")
                    break  # 页面关闭时不再尝试其他策略
                else:
                    logger.warning(f"❌ 策略 {strategy} 失败: {e}")

                # 如果不是最后一个策略，继续尝试
                if attempt < len(strategies) - 1:
                    logger.info(f"🔄 尝试下一个策略...")
                    await asyncio.sleep(1)  # 短暂等待后重试

        # 所有策略都失败了
        logger.error(f"🔴 所有等待策略都失败，最后错误: {last_error}")
        self._record_failure(url)
        return False
    
    def _get_strategies_to_try(self, preferred_strategy: Optional[str] = None) -> List[str]:
        """获取要尝试的策略列表"""
        strategies = self.WAIT_STRATEGIES.copy()
        
        # 如果指定了首选策略，将其移到最前面
        if preferred_strategy and preferred_strategy in strategies:
            strategies.remove(preferred_strategy)
            strategies.insert(0, preferred_strategy)
        
        return strategies
    
    async def _try_goto_with_strategy(self, page: Page, url: str,
                                    strategy: str, config: Dict[str, Any]) -> bool:
        """使用指定策略尝试访问页面"""
        timeout = config.get("timeout", 90000)
        extra_wait = config.get("extra_wait", 2.0)
        wait_for_selector = config.get("wait_for_selector")
        wait_for_selector_timeout = config.get("wait_for_selector_timeout", 5000)

        try:
            # 检查页面是否仍然有效
            if page.is_closed():
                raise Exception("页面已关闭")

            # 执行页面跳转
            logger.debug(f"执行 page.goto，策略: {strategy}, 超时: {timeout}ms")
            await page.goto(url, timeout=timeout, wait_until=strategy)

            # 检查页面是否在跳转后仍然有效
            if page.is_closed():
                raise Exception("页面在跳转后被关闭")

            # 额外的等待状态检查
            await self._additional_wait_checks(page, strategy, timeout)

            # 如果指定了特定选择器，等待其出现
            if wait_for_selector and not page.is_closed():
                try:
                    logger.debug(f"等待选择器: {wait_for_selector}")
                    await page.wait_for_selector(
                        wait_for_selector,
                        timeout=wait_for_selector_timeout
                    )
                    logger.debug(f"选择器已出现: {wait_for_selector}")
                except PlaywrightTimeoutError:
                    logger.warning(f"选择器等待超时: {wait_for_selector}")
                    # 不抛出异常，继续处理

            # 额外等待时间
            if extra_wait > 0 and not page.is_closed():
                logger.debug(f"额外等待 {extra_wait} 秒")
                await asyncio.sleep(extra_wait)

            # 验证页面是否正常加载
            if not page.is_closed() and await self._validate_page_load(page):
                return True
            else:
                raise Exception("页面验证失败或页面已关闭")

        except PlaywrightTimeoutError as e:
            raise Exception(f"页面访问超时 ({strategy}): {e}")
        except Exception as e:
            # 检查是否是页面关闭错误
            if "closed" in str(e).lower():
                raise Exception(f"页面或浏览器已关闭 ({strategy}): {e}")
            raise Exception(f"页面访问失败 ({strategy}): {e}")
    
    async def _additional_wait_checks(self, page: Page, strategy: str, timeout: int):
        """根据策略执行额外的等待检查"""
        if page.is_closed():
            logger.warning("页面已关闭，跳过额外等待检查")
            return

        reduced_timeout = min(timeout // 2, 30000)  # 使用较短的超时时间

        try:
            if strategy == "commit" and not page.is_closed():
                # 对于commit策略，尝试等待DOM加载
                await page.wait_for_load_state("domcontentloaded", timeout=reduced_timeout)
            elif strategy == "load" and not page.is_closed():
                # 对于load策略，尝试等待网络空闲
                await page.wait_for_load_state("networkidle", timeout=reduced_timeout)
            elif strategy == "domcontentloaded" and not page.is_closed():
                # 对于domcontentloaded策略，尝试等待网络空闲
                await page.wait_for_load_state("networkidle", timeout=reduced_timeout)
        except PlaywrightTimeoutError:
            # 额外等待失败不影响主流程
            logger.debug(f"额外等待检查超时，但不影响主流程")
        except Exception as e:
            if "closed" in str(e).lower():
                logger.warning("页面在额外等待检查中被关闭")
            else:
                logger.debug(f"额外等待检查异常: {e}")
    
    async def _validate_page_load(self, page: Page) -> bool:
        """验证页面是否正常加载（放宽验证条件）"""
        try:
            # 检查页面标题
            title = await page.title()

            # 检查页面内容长度
            content = await page.content()
            if len(content) < 100:  # 进一步降低最小内容长度要求
                logger.warning(f"页面内容过短: {len(content)} 字符")
                return False

            # 检查是否是明显的错误页面（只检查标题中的明显错误）
            if title:
                obvious_errors = ["404", "403", "500", "not found", "page not found"]
                if any(error in title.lower() for error in obvious_errors):
                    logger.warning(f"页面显示明显错误: {title}")
                    return False

            # 检查是否包含明显的阻止内容（放宽检查）
            content_lower = content.lower()
            obvious_blocks = ["access denied", "forbidden", "blocked by administrator"]
            if any(block in content_lower for block in obvious_blocks):
                logger.warning("页面被明显阻止")
                return False

            # 如果有标题，记录成功信息
            if title:
                logger.debug(f"页面验证通过: {title[:50]}...")
            else:
                logger.debug(f"页面验证通过（无标题）: 内容长度 {len(content)} 字符")

            return True

        except Exception as e:
            logger.warning(f"页面验证失败: {e}")
            # 验证失败时也返回True，让上层决定是否继续
            return True
    
    def _record_success(self, url: str, strategy: str):
        """记录成功的策略"""
        domain = urlparse(url).netloc.lower()
        if domain not in self.success_stats:
            self.success_stats[domain] = {}
        if strategy not in self.success_stats[domain]:
            self.success_stats[domain][strategy] = {"success": 0, "total": 0}
        
        self.success_stats[domain][strategy]["success"] += 1
        self.success_stats[domain][strategy]["total"] += 1
    
    def _record_failure(self, url: str):
        """记录失败"""
        domain = urlparse(url).netloc.lower()
        if domain not in self.success_stats:
            self.success_stats[domain] = {}
        
        # 为所有尝试过的策略记录失败
        for strategy in self.WAIT_STRATEGIES:
            if strategy not in self.success_stats[domain]:
                self.success_stats[domain][strategy] = {"success": 0, "total": 0}
            self.success_stats[domain][strategy]["total"] += 1
    
    def get_success_stats(self) -> Dict[str, Any]:
        """获取成功率统计"""
        stats = {}
        for domain, strategies in self.success_stats.items():
            stats[domain] = {}
            for strategy, data in strategies.items():
                if data["total"] > 0:
                    success_rate = data["success"] / data["total"] * 100
                    stats[domain][strategy] = {
                        "success_rate": f"{success_rate:.1f}%",
                        "success": data["success"],
                        "total": data["total"]
                    }
        return stats


# 全局实例
robust_wait_strategy = RobustWaitStrategy()


async def robust_goto(page: Page, url: str, **kwargs) -> bool:
    """
    便捷函数：健壮的页面访问
    
    Args:
        page: Playwright页面对象
        url: 目标URL
        **kwargs: 额外配置参数
        
    Returns:
        bool: 是否访问成功
    """
    return await robust_wait_strategy.robust_goto(page, url, **kwargs)


def get_success_stats() -> Dict[str, Any]:
    """获取成功率统计"""
    return robust_wait_strategy.get_success_stats()


if __name__ == "__main__":
    # 测试代码
    print("健壮等待策略工具已加载")
    print("主要功能:")
    print("- robust_goto(): 健壮的页面访问函数")
    print("- 渐进式等待策略: networkidle -> domcontentloaded -> load -> commit")
    print("- 网站特定配置支持")
    print("- 成功率统计")
