#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选择器测试配置管理模块
用于管理和维护各种网站的测试配置
"""

import json
import os
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger('SelectorsTestConfig')


class TestConfigManager:
    """测试配置管理器"""
    
    def __init__(self, config_file: str = "selectors_test_configs.json"):
        """
        初始化测试配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.configs = self.load_configs()
    
    def load_configs(self) -> Dict[str, Any]:
        """加载测试配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                return self.create_default_configs()
        else:
            return self.create_default_configs()
    
    def save_configs(self) -> bool:
        """保存测试配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.configs, f, ensure_ascii=False, indent=2)
            logger.info(f"配置已保存到: {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return False
    
    def create_default_configs(self) -> Dict[str, Any]:
        """创建默认测试配置"""
        return {
            "微信公众号测试": {
                "description": "微信公众号文章选择器测试",
                "urls": [
                    "https://mp.weixin.qq.com/s/VILTK648NrlWPaMpi8OVnw",
                    "https://mp.weixin.qq.com/s/qxpFGYiO-fH5KQuPC2HC8g"
                ],
                "expected_module": "微信公众号",
                "selectors_config": {
                    "content_selectors": ["#js_content"],
                    "title_selectors": [
                        "#activity-name",
                        ".rich_media_title",
                        "h1.rich_media_title"
                    ],
                    "date_selectors": [
                        "#publish_time",
                        ".rich_media_meta_text"
                    ],
                    "source_selectors": [
                        ".rich_media_meta_nickname",
                        "#js_name"
                    ],
                    "mode": "safe",
                    "use_module_config": True
                }
            },
            "上海人大测试": {
                "description": "上海人大网站选择器测试",
                "urls": [
                    "https://www.shrd.gov.cn/n8347/n8378/u1ai270696.html"
                ],
                "expected_module": None,
                "selectors_config": {
                    "content_selectors": [".gjzw.gray16"],
                    "title_selectors": ["h1.blue30"],
                    "date_selectors": [".gray14"],
                    "source_selectors": [".gray14"],
                    "mode": "safe",
                    "use_module_config": False
                }
            },
            "北京人大测试": {
                "description": "北京人大网站选择器测试",
                "urls": [
                    "https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/202412/t20241206_3815513.html"
                ],
                "expected_module": None,
                "selectors_config": {
                    "content_selectors": [".zhengwen"],
                    "title_selectors": [".xl_title.clearfix h1"],
                    "date_selectors": [".xl_ly span:first-child"],
                    "source_selectors": [".xl_ly span:last-child"],
                    "mode": "balance",
                    "use_module_config": False
                }
            },
            "通用政府网站测试": {
                "description": "通用政府网站选择器测试",
                "urls": [],
                "expected_module": None,
                "selectors_config": {
                    "content_selectors": [
                        ".article_cont",
                        ".article-content",
                        ".content",
                        ".zhengwen",
                        ".TRS_Editor",
                        "div[class*='content']"
                    ],
                    "title_selectors": [
                        "h1",
                        ".title",
                        ".article-title",
                        ".xl_title h1"
                    ],
                    "date_selectors": [
                        ".date",
                        ".time",
                        ".publish-time",
                        ".xl_ly span:first-child"
                    ],
                    "source_selectors": [
                        ".source",
                        ".author",
                        ".xl_ly span:last-child"
                    ],
                    "mode": "balance",
                    "use_module_config": False
                }
            }
        }
    
    def get_test_config(self, config_name: str) -> Optional[Dict[str, Any]]:
        """获取指定的测试配置"""
        return self.configs.get(config_name)
    
    def add_test_config(self, config_name: str, config_data: Dict[str, Any]) -> bool:
        """添加新的测试配置"""
        try:
            self.configs[config_name] = config_data
            return self.save_configs()
        except Exception as e:
            logger.error(f"添加测试配置失败: {e}")
            return False
    
    def update_test_config(self, config_name: str, config_data: Dict[str, Any]) -> bool:
        """更新测试配置"""
        if config_name not in self.configs:
            logger.warning(f"测试配置不存在: {config_name}")
            return False
        
        try:
            self.configs[config_name].update(config_data)
            return self.save_configs()
        except Exception as e:
            logger.error(f"更新测试配置失败: {e}")
            return False
    
    def delete_test_config(self, config_name: str) -> bool:
        """删除测试配置"""
        if config_name not in self.configs:
            logger.warning(f"测试配置不存在: {config_name}")
            return False
        
        try:
            del self.configs[config_name]
            return self.save_configs()
        except Exception as e:
            logger.error(f"删除测试配置失败: {e}")
            return False
    
    def list_test_configs(self) -> List[str]:
        """列出所有测试配置名称"""
        return list(self.configs.keys())
    
    def validate_config(self, config_data: Dict[str, Any]) -> List[str]:
        """验证测试配置"""
        errors = []
        
        # 检查必要字段
        required_fields = ['description', 'urls', 'selectors_config']
        for field in required_fields:
            if field not in config_data:
                errors.append(f"缺少必要字段: {field}")
        
        # 检查URLs
        urls = config_data.get('urls', [])
        if not isinstance(urls, list):
            errors.append("urls字段必须是列表")
        elif len(urls) == 0:
            errors.append("至少需要一个测试URL")
        
        # 检查选择器配置
        selectors_config = config_data.get('selectors_config', {})
        if not isinstance(selectors_config, dict):
            errors.append("selectors_config字段必须是字典")
        else:
            # 检查必要的选择器
            required_selectors = ['content_selectors']
            for selector in required_selectors:
                if selector not in selectors_config:
                    errors.append(f"缺少必要的选择器: {selector}")
        
        return errors
    
    def export_config(self, config_name: str, export_file: str) -> bool:
        """导出单个测试配置"""
        config = self.get_test_config(config_name)
        if not config:
            logger.error(f"测试配置不存在: {config_name}")
            return False
        
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump({config_name: config}, f, ensure_ascii=False, indent=2)
            logger.info(f"配置已导出到: {export_file}")
            return True
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, import_file: str) -> bool:
        """导入测试配置"""
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                imported_configs = json.load(f)
            
            for config_name, config_data in imported_configs.items():
                errors = self.validate_config(config_data)
                if errors:
                    logger.error(f"配置验证失败 {config_name}: {errors}")
                    continue
                
                self.configs[config_name] = config_data
                logger.info(f"已导入配置: {config_name}")
            
            return self.save_configs()
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False


# 选择器测试工具函数
def create_selector_test_suite():
    """创建选择器测试套件"""
    config_manager = TestConfigManager()
    
    # 确保默认配置存在
    if not config_manager.configs:
        config_manager.configs = config_manager.create_default_configs()
        config_manager.save_configs()
    
    print("选择器测试套件已创建")
    print(f"配置文件: {config_manager.config_file}")
    print(f"可用测试配置: {', '.join(config_manager.list_test_configs())}")
    
    return config_manager


def generate_test_report_template():
    """生成测试报告模板"""
    template = {
        "test_session": {
            "start_time": "2024-01-01 00:00:00",
            "end_time": "2024-01-01 01:00:00",
            "total_duration": 3600,
            "tester": "测试人员姓名"
        },
        "test_summary": {
            "total_tests": 0,
            "successful_tests": 0,
            "failed_tests": 0,
            "success_rate": 0.0
        },
        "test_results": [
            {
                "test_name": "示例测试",
                "url": "https://example.com",
                "success": True,
                "duration": 2.5,
                "module_name": "示例模组",
                "error": None,
                "timestamp": "2024-01-01 00:30:00"
            }
        ],
        "recommendations": [
            "建议优化的选择器",
            "需要添加的模组配置",
            "性能改进建议"
        ]
    }
    
    template_file = "test_report_template.json"
    try:
        with open(template_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, ensure_ascii=False, indent=2)
        print(f"✅ 测试报告模板已创建: {template_file}")
    except Exception as e:
        print(f"❌ 创建模板失败: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "create":
            create_selector_test_suite()
        elif command == "template":
            generate_test_report_template()
        else:
            print("可用命令:")
            print("  python selectors_test_config.py create    - 创建测试套件")
            print("  python selectors_test_config.py template  - 生成报告模板")
    else:
        create_selector_test_suite()
