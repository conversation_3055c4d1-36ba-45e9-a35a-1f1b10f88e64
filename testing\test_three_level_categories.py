#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试三级分类功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_three_level_category_structure():
    """测试三级分类结构"""
    print("🧪 测试三级分类结构")
    
    try:
        from config.manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 测试获取父级分类
        parent_categories = config_manager.get_parent_categories()
        print(f"父级分类: {parent_categories}")
        
        if parent_categories:
            # 测试获取次级分类
            first_parent = parent_categories[0]
            sub_categories = config_manager.get_sub_categories(first_parent)
            print(f"'{first_parent}' 的次级分类: {sub_categories}")
            
            if sub_categories:
                # 测试获取子级分类
                first_sub = sub_categories[0]
                child_categories = config_manager.get_child_categories(first_parent, first_sub)
                print(f"'{first_parent}/{first_sub}' 的子级分类: {child_categories}")
                
                if child_categories:
                    # 测试获取配置组
                    first_child = child_categories[0]
                    category_path = f"{first_parent}/{first_sub}/{first_child}"
                    configs = config_manager.get_configs_by_category_path(category_path)
                    print(f"'{category_path}' 的配置组: {configs}")
        
        print("✅ 三级分类结构测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 三级分类结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_add_three_level_category():
    """测试添加三级分类"""
    print("\n🧪 测试添加三级分类")
    
    try:
        from config.manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 添加新的三级分类
        success = config_manager.add_three_level_category(
            "新闻媒体", "中央媒体", "人民日报", "人民日报相关配置"
        )
        print(f"添加三级分类 '新闻媒体/中央媒体/人民日报': {'✅' if success else '❌'}")
        
        if success:
            # 验证分类是否添加成功
            configs = config_manager.get_configs_by_category_path("新闻媒体/中央媒体/人民日报")
            print(f"新分类的配置组: {configs}")
        
        print("✅ 添加三级分类测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 添加三级分类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_move_config_to_category():
    """测试移动配置组到分类"""
    print("\n🧪 测试移动配置组到分类")
    
    try:
        from config.manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 首先确保有配置组存在
        groups = config_manager.get_groups()
        if not groups:
            print("⚠️ 没有配置组可供测试")
            return True
        
        test_config = groups[0]
        print(f"测试配置组: {test_config}")
        
        # 获取当前分类路径
        config_data = config_manager.get_group(test_config)
        current_path = config_data.get("category_path", "未知")
        print(f"当前分类路径: {current_path}")
        
        # 确保目标分类存在
        config_manager.add_three_level_category(
            "测试分类", "测试子分类", "测试子子分类", "测试用分类"
        )
        
        # 移动配置组
        target_path = "测试分类/测试子分类/测试子子分类"
        success = config_manager.move_config_to_category_path(test_config, target_path)
        print(f"移动配置组到 '{target_path}': {'✅' if success else '❌'}")
        
        if success:
            # 验证移动结果
            updated_config = config_manager.get_group(test_config)
            new_path = updated_config.get("category_path", "未知")
            print(f"移动后的分类路径: {new_path}")
            
            # 验证新分类中是否包含配置组
            configs_in_new_category = config_manager.get_configs_by_category_path(target_path)
            print(f"新分类中的配置组: {configs_in_new_category}")
        
        print("✅ 移动配置组测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 移动配置组测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_three_level_categories():
    """测试GUI三级分类功能"""
    print("\n🧪 测试GUI三级分类功能")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建GUI实例
        gui = CrawlerGUI()
        
        # 检查三级分类控件是否存在
        has_parent_combo = hasattr(gui, 'parent_category_combo')
        has_sub_combo = hasattr(gui, 'sub_category_combo')
        has_child_combo = hasattr(gui, 'child_category_combo')
        
        print(f"父级分类下拉框: {'✅' if has_parent_combo else '❌'}")
        print(f"次级分类下拉框: {'✅' if has_sub_combo else '❌'}")
        print(f"子级分类下拉框: {'✅' if has_child_combo else '❌'}")
        
        if has_parent_combo and has_sub_combo and has_child_combo:
            # 测试分类加载
            gui.load_category_combos()
            
            parent_count = gui.parent_category_combo.count()
            print(f"父级分类数量: {parent_count}")
            
            if parent_count > 0:
                # 测试分类切换
                first_parent = gui.parent_category_combo.itemText(0)
                gui.on_parent_category_changed(first_parent)
                
                sub_count = gui.sub_category_combo.count()
                print(f"次级分类数量: {sub_count}")
                
                if sub_count > 0:
                    first_sub = gui.sub_category_combo.itemText(0)
                    gui.on_sub_category_changed(first_sub)
                    
                    child_count = gui.child_category_combo.count()
                    print(f"子级分类数量: {child_count}")
        
        print("✅ GUI三级分类功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI三级分类功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_migration():
    """测试配置迁移到三级分类"""
    print("\n🧪 测试配置迁移到三级分类")
    
    try:
        import json
        from config.manager import ConfigManager
        
        # 创建旧格式配置
        old_config = {
            "last_used": "test_config",
            "groups": {
                "test_config": {
                    "input_url": "https://example.com",
                    "title_selectors": [".title"],
                    "content_selectors": [".content"]
                }
            }
        }
        
        # 保存旧格式配置
        test_config_file = "testing/test_migration_three_level.json"
        os.makedirs("testing", exist_ok=True)
        with open(test_config_file, 'w', encoding='utf-8') as f:
            json.dump(old_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 创建旧格式配置文件: {test_config_file}")
        
        # 加载配置（应该触发迁移）
        config_manager = ConfigManager(test_config_file)
        
        # 验证迁移结果
        parent_categories = config_manager.get_parent_categories()
        print(f"迁移后父级分类: {parent_categories}")
        
        if parent_categories:
            first_parent = parent_categories[0]
            sub_categories = config_manager.get_sub_categories(first_parent)
            print(f"迁移后次级分类: {sub_categories}")
            
            if sub_categories:
                first_sub = sub_categories[0]
                child_categories = config_manager.get_child_categories(first_parent, first_sub)
                print(f"迁移后子级分类: {child_categories}")
                
                if child_categories:
                    first_child = child_categories[0]
                    category_path = f"{first_parent}/{first_sub}/{first_child}"
                    configs = config_manager.get_configs_by_category_path(category_path)
                    print(f"迁移后配置组: {configs}")
        
        # 检查配置组是否有正确的category_path
        groups = config_manager.config.get("groups", {})
        for group_name, group_config in groups.items():
            category_path = group_config.get("category_path")
            print(f"配置组 '{group_name}' 的分类路径: {category_path}")
        
        # 清理测试文件
        if os.path.exists(test_config_file):
            os.remove(test_config_file)
            print(f"🗑️ 清理测试文件: {test_config_file}")
        
        print("✅ 配置迁移到三级分类测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置迁移测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试三级分类功能")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("三级分类结构", test_three_level_category_structure()))
    test_results.append(("添加三级分类", test_add_three_level_category()))
    test_results.append(("移动配置组", test_move_config_to_category()))
    test_results.append(("GUI三级分类", test_gui_three_level_categories()))
    test_results.append(("配置迁移", test_config_migration()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有三级分类功能测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，但主要功能应该可以使用")
        return passed > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
