#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的动态翻页功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from core.crawler import launch_browser
from core.PaginationHandler import PaginationHandler

async def test_fixed_pagination():
    """测试修复后的动态翻页功能"""
    print("🧪 测试修复后的动态翻页功能")
    print("=" * 60)
    
    # 测试多个网站
    test_urls = [
        {
            "name": "天津市政协-提案选登",
            "url": "https://www.tjszx.gov.cn/tagz/taxd/index.shtml",
            "article_selector": "a[href*='/tagz/system/']"
        },
        {
            "name": "天津市政协-全体会议发言", 
            "url": "http://www.tjszx.gov.cn/yzjy/qthyfy/index.shtml",
            "article_selector": "a[href*='/yzjy/system/']"
        }
    ]
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            for test_case in test_urls:
                print(f"\n🎯 测试网站: {test_case['name']}")
                print(f"📋 URL: {test_case['url']}")
                
                try:
                    # 访问页面
                    await page.goto(test_case['url'], timeout=30000)
                    await page.wait_for_load_state('networkidle', timeout=15000)
                    
                    # 创建PaginationHandler实例
                    handler = PaginationHandler(page)
                    
                    # 配置文章提取参数
                    extract_config = {
                        'list_container_selector': 'body',
                        'article_item_selector': test_case['article_selector'],
                        'url_mode': 'absolute'
                    }
                    
                    print("🔍 开始测试修复后的动态翻页...")
                    
                    # 使用修复后的点击翻页功能
                    pages_processed = await handler.click_pagination(
                        next_button_selector="a.next:not(.lose)",  # 使用常见的选择器
                        max_pages=3,  # 测试3页
                        timeout=5000,
                        wait_after_click=2000,
                        disabled_check=True,
                        extract_articles_config=extract_config,
                        auto_detect_pagination=True  # 启用自动检测
                    )
                    
                    print(f"✅ 处理了 {pages_processed} 页")
                    
                    # 获取收集的文章
                    articles = handler.get_all_articles()
                    print(f"📊 总共收集了 {len(articles)} 篇文章")
                    
                    if len(articles) > 0:
                        print("📋 前几篇文章:")
                        for i, article in enumerate(articles[:3]):
                            title = article[0] if len(article) > 0 else "无标题"
                            url = article[1] if len(article) > 1 else "无URL"
                            print(f"    [{i+1}] {title[:50]}...")
                            print(f"        URL: {url}")
                    
                    # 清空文章列表，准备下一个测试
                    handler.clear_articles()
                    
                except Exception as e:
                    print(f"❌ 测试 {test_case['name']} 失败: {e}")
                    continue
            
            print("\n📊 所有测试完成")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            
        finally:
            await context.close()
            await browser.close()

async def test_alternative_button_detection():
    """专门测试替代按钮检测功能"""
    print("\n🧪 测试替代按钮检测功能")
    print("=" * 60)
    
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            await page.goto(url, timeout=30000)
            await page.wait_for_load_state('networkidle', timeout=15000)
            
            handler = PaginationHandler(page)
            
            print("🔍 测试替代按钮检测...")
            alternative_buttons = await handler._find_alternative_pagination_buttons()
            
            if alternative_buttons:
                print(f"✅ 找到 {len(alternative_buttons)} 个替代翻页按钮:")
                for i, button in enumerate(alternative_buttons):
                    text = await button.text_content()
                    href = await button.get_attribute('href')
                    onclick = await button.get_attribute('onclick')
                    print(f"  [{i+1}] 文本: '{text}' | href: '{href}' | onclick: '{onclick}'")
            else:
                print("❌ 未找到任何替代翻页按钮")
                print("💡 这可能意味着页面确实没有翻页功能")
            
        except Exception as e:
            print(f"❌ 替代按钮检测测试失败: {e}")
            
        finally:
            await context.close()
            await browser.close()

if __name__ == "__main__":
    print("🚀 开始测试修复后的动态翻页功能")
    asyncio.run(test_fixed_pagination())
    asyncio.run(test_alternative_button_detection())
