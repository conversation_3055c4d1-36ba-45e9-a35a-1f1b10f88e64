#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一的翻页工具类，减少重复代码
"""

import logging
from typing import List, Dict, Optional, Any
from playwright.async_api import Page

logger = logging.getLogger(__name__)

class PaginationUtils:
    """统一的翻页工具类"""
    
    # 通用的下一页选择器
    COMMON_NEXT_SELECTORS = [
        'a:has-text("下一页")',
        'a:has-text("下页")', 
        'a:has-text("下一頁")',
        'a:has-text(">")',
        'a[title*="下一页"]',
        'a[title*="下页"]',
        '.next',
        'a.next-page',
        'button:has-text("下一页")',
        'button:has-text("下页")'
    ]
    
    # JSP网站专用选择器
    JSP_SPECIFIC_SELECTORS = [
        'a[href*="javascript:goto"]',
        'a[onclick*="goto"]',
        'a[href*="javascript:nextPage"]',
        'a[onclick*="nextPage"]'
    ]
    
    @staticmethod
    async def get_pagination_context(page: Page) -> Page:
        """
        获取翻页上下文（主页面或iframe）

        Args:
            page: Playwright页面对象

        Returns:
            Page: 包含翻页元素的页面对象（主页面或iframe）
        """
        # 首先检查主页面是否有翻页元素
        main_page_elements = await page.query_selector_all('a:has-text("下一页"), a:has-text("下页"), a[href*="javascript:goto"]')
        if main_page_elements:
            logger.debug("翻页元素在主页面中")
            return page

        # 检查iframe中是否有翻页元素
        iframes = await page.query_selector_all('iframe')
        for iframe in iframes:
            try:
                frame = await iframe.content_frame()
                if frame:
                    await frame.wait_for_load_state('networkidle', timeout=5000)
                    frame_elements = await frame.query_selector_all('a:has-text("下一页"), a:has-text("下页"), a[href*="javascript:goto"]')
                    if frame_elements:
                        logger.debug(f"翻页元素在iframe中: {await iframe.get_attribute('src')}")
                        return frame
            except Exception as e:
                logger.debug(f"检查iframe失败: {e}")

        logger.debug("未找到翻页元素，返回主页面")
        return page

    @staticmethod
    async def analyze_pagination_structure(page: Page, current_page: int = 1) -> Dict[str, Any]:
        """
        分析页面的翻页结构
        
        Args:
            page: Playwright页面对象
            current_page: 当前页码
            
        Returns:
            dict: 翻页结构信息
        """
        try:
            # 获取正确的翻页上下文（主页面或iframe）
            pagination_context = await PaginationUtils.get_pagination_context(page)

            pagination_info = await pagination_context.evaluate(f"""
                () => {{
                    const info = {{
                        nextPageLinks: [],
                        pageNumbers: [],
                        jsFunction: null,
                        currentPage: {current_page},
                        totalPages: null
                    }};
                    
                    // 查找下一页链接
                    const nextLinks = document.querySelectorAll('a, button');
                    nextLinks.forEach(link => {{
                        const text = link.textContent.trim();
                        const href = link.getAttribute('href') || '';
                        const onclick = link.getAttribute('onclick') || '';
                        const className = link.className || '';
                        
                        // 下一页链接
                        if (text.includes('下一页') || text.includes('下页') || text === '>' || text === 'Next') {{
                            info.nextPageLinks.push({{
                                text: text,
                                href: href,
                                onclick: onclick,
                                className: className,
                                disabled: link.hasAttribute('disabled') || 
                                         className.includes('disabled') || 
                                         className.includes('gray') ||
                                         className.includes('inactive')
                            }});
                        }}
                        
                        // 页码链接
                        if (/^\\d+$/.test(text)) {{
                            const pageNum = parseInt(text);
                            if (pageNum === {current_page + 1}) {{
                                info.pageNumbers.push({{
                                    pageNum: pageNum,
                                    href: href,
                                    onclick: onclick,
                                    className: className
                                }});
                            }}
                        }}
                    }});
                    
                    // 检查JavaScript翻页函数
                    if (typeof goto === 'function') info.jsFunction = 'goto';
                    else if (typeof nextPage === 'function') info.jsFunction = 'nextPage';
                    else if (typeof goPage === 'function') info.jsFunction = 'goPage';
                    else if (typeof turnPage === 'function') info.jsFunction = 'turnPage';
                    else if (typeof page === 'function') info.jsFunction = 'page';
                    
                    // 尝试检测总页数
                    const totalPagesText = document.body.textContent;
                    const totalMatch = totalPagesText.match(/共\\s*(\\d+)\\s*页|总共\\s*(\\d+)\\s*页|total\\s*(\\d+)\\s*pages?/i);
                    if (totalMatch) {{
                        info.totalPages = parseInt(totalMatch[1] || totalMatch[2] || totalMatch[3]);
                    }}
                    
                    return info;
                }}
            """)
            
            logger.debug(f"翻页结构分析结果: {pagination_info}")
            return pagination_info
            
        except Exception as e:
            logger.error(f"翻页结构分析失败: {e}")
            return {}
    
    @staticmethod
    async def find_next_page_element(page: Page, custom_selectors: List[str] = None, 
                                   include_jsp_selectors: bool = False) -> Optional[Dict[str, Any]]:
        """
        查找下一页元素
        
        Args:
            page: Playwright页面对象
            custom_selectors: 自定义选择器列表
            include_jsp_selectors: 是否包含JSP专用选择器
            
        Returns:
            dict: 找到的元素信息，包含element, selector, info等
        """
        # 构建选择器列表
        selectors = PaginationUtils.COMMON_NEXT_SELECTORS.copy()
        
        if include_jsp_selectors:
            selectors.extend(PaginationUtils.JSP_SPECIFIC_SELECTORS)
        
        if custom_selectors:
            selectors.extend(custom_selectors)
        
        # 获取正确的翻页上下文
        pagination_context = await PaginationUtils.get_pagination_context(page)

        # 按优先级查找
        for selector in selectors:
            try:
                element = await pagination_context.query_selector(selector)
                if element:
                    # 检查元素状态
                    is_visible = await element.is_visible()
                    is_enabled = await element.is_enabled()
                    
                    # 获取元素属性
                    href = await element.get_attribute('href')
                    onclick = await element.get_attribute('onclick')
                    class_name = await element.get_attribute('class') or ''
                    text = await element.text_content()
                    
                    # 检查是否禁用
                    is_disabled = (
                        not is_enabled or
                        'disabled' in class_name.lower() or
                        'gray' in class_name.lower() or
                        'inactive' in class_name.lower()
                    )
                    
                    if is_visible and not is_disabled:
                        return {
                            'element': element,
                            'selector': selector,
                            'info': {
                                'text': text,
                                'href': href,
                                'onclick': onclick,
                                'className': class_name,
                                'isVisible': is_visible,
                                'isEnabled': is_enabled,
                                'isDisabled': is_disabled
                            }
                        }
                    else:
                        logger.debug(f"跳过不可用元素 {selector}: visible={is_visible}, disabled={is_disabled}")
                        
            except Exception as e:
                logger.debug(f"选择器 {selector} 查找失败: {e}")
        
        return None
    
    @staticmethod
    async def click_next_page(page: Page, custom_selectors: List[str] = None,
                            include_jsp_selectors: bool = False, 
                            wait_after_click: int = 1000) -> bool:
        """
        点击下一页
        
        Args:
            page: Playwright页面对象
            custom_selectors: 自定义选择器列表
            include_jsp_selectors: 是否包含JSP专用选择器
            wait_after_click: 点击后等待时间(毫秒)
            
        Returns:
            bool: 是否成功点击
        """
        # 查找下一页元素
        next_element_info = await PaginationUtils.find_next_page_element(
            page, custom_selectors, include_jsp_selectors
        )
        
        if not next_element_info:
            logger.warning("未找到可用的下一页元素")
            return False
        
        try:
            element = next_element_info['element']
            selector = next_element_info['selector']
            info = next_element_info['info']
            
            # 点击元素
            await element.click()
            
            # 等待页面响应
            if wait_after_click > 0:
                await page.wait_for_timeout(wait_after_click)
            
            logger.info(f"翻页成功: {selector} (文本: '{info['text']}', href: '{info['href']}')")
            return True
            
        except Exception as e:
            logger.error(f"点击翻页失败: {e}")
            return False
    
    @staticmethod
    async def javascript_pagination(page: Page, current_page: int, 
                                  js_function: str = None) -> bool:
        """
        JavaScript翻页
        
        Args:
            page: Playwright页面对象
            current_page: 当前页码
            js_function: 指定的JavaScript函数名
            
        Returns:
            bool: 是否成功翻页
        """
        try:
            # 获取正确的翻页上下文
            pagination_context = await PaginationUtils.get_pagination_context(page)

            # 如果指定了函数，直接使用
            if js_function:
                if js_function == 'goto':
                    next_page_num = current_page + 1
                    await pagination_context.evaluate(f"goto({next_page_num})")
                    logger.info(f"JavaScript翻页成功: goto({next_page_num})")
                    return True
                elif js_function == 'nextPage':
                    # 尝试两种调用方式
                    try:
                        await pagination_context.evaluate("nextPage()")
                        logger.info("JavaScript翻页成功: nextPage()")
                        return True
                    except:
                        await pagination_context.evaluate(f"nextPage({current_page + 1})")
                        logger.info(f"JavaScript翻页成功: nextPage({current_page + 1})")
                        return True
                else:
                    next_page_num = current_page + 1
                    await pagination_context.evaluate(f"{js_function}({next_page_num})")
                    logger.info(f"JavaScript翻页成功: {js_function}({next_page_num})")
                    return True
            
            # 自动检测可用函数
            available_functions = await pagination_context.evaluate("""
                () => {
                    const functions = [];
                    if (typeof goto === 'function') functions.push('goto');
                    if (typeof nextPage === 'function') functions.push('nextPage');
                    if (typeof goPage === 'function') functions.push('goPage');
                    if (typeof turnPage === 'function') functions.push('turnPage');
                    if (typeof page === 'function') functions.push('page');
                    return functions;
                }
            """)
            
            # 按优先级尝试函数
            for func_name in available_functions:
                try:
                    return await PaginationUtils.javascript_pagination(page, current_page, func_name)
                except:
                    continue
            
            logger.warning("未找到可用的JavaScript翻页函数")
            return False
            
        except Exception as e:
            logger.error(f"JavaScript翻页失败: {e}")
            return False
    
    @staticmethod
    async def smart_pagination(page: Page, current_page: int, 
                             custom_selectors: List[str] = None,
                             include_jsp_selectors: bool = False,
                             wait_after_click: int = 1000) -> bool:
        """
        智能翻页：综合使用多种策略
        
        Args:
            page: Playwright页面对象
            current_page: 当前页码
            custom_selectors: 自定义选择器列表
            include_jsp_selectors: 是否包含JSP专用选择器
            wait_after_click: 点击后等待时间(毫秒)
            
        Returns:
            bool: 是否成功翻页
        """
        logger.info(f"开始智能翻页: 第{current_page}页 → 第{current_page + 1}页")
        
        # 策略1: 分析页面结构，使用最佳策略
        pagination_info = await PaginationUtils.analyze_pagination_structure(page, current_page)
        
        # 策略1.1: 使用分析结果中的下一页链接
        if pagination_info.get('nextPageLinks'):
            for link_info in pagination_info['nextPageLinks']:
                if not link_info.get('disabled'):
                    try:
                        selector = f'a:has-text("{link_info["text"]}")'
                        element = await page.query_selector(selector)
                        if element:
                            await element.click()
                            await page.wait_for_timeout(wait_after_click)
                            logger.info(f"智能翻页成功(分析结果): {link_info['text']}")
                            return True
                    except Exception as e:
                        logger.debug(f"分析结果翻页失败: {e}")
        
        # 策略1.2: 使用JavaScript函数
        if pagination_info.get('jsFunction'):
            if await PaginationUtils.javascript_pagination(page, current_page, pagination_info['jsFunction']):
                return True
        
        # 策略2: 传统点击翻页
        if await PaginationUtils.click_next_page(page, custom_selectors, include_jsp_selectors, wait_after_click):
            return True
        
        # 策略3: JavaScript自动检测翻页
        if await PaginationUtils.javascript_pagination(page, current_page):
            return True
        
        logger.warning("所有智能翻页策略都失败了")
        return False
