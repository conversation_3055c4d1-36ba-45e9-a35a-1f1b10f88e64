#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证手动翻页功能集成
"""

import os
import sys

def verify_manual_pagination_integration():
    """验证手动翻页功能集成"""
    print("🔍 验证手动翻页功能集成")
    print("=" * 50)
    
    success_count = 0
    total_checks = 0
    
    # 1. 检查文件夹结构
    print("1. 检查文件夹结构...")
    total_checks += 1
    
    required_files = [
        "manual_pagination/README.md",
        "manual_pagination/manual_pagination_handler.py",
        "manual_pagination/url_templates.xlsx",
        "manual_pagination/examples/template_formats.md",
        "manual_pagination/examples/example_urls.xlsx"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"   ❌ 缺少文件: {missing_files}")
    else:
        print("   ✅ 所有必需文件都存在")
        success_count += 1
    
    # 2. 检查手动翻页处理器
    print("\n2. 检查手动翻页处理器...")
    total_checks += 1
    
    try:
        from manual_pagination.manual_pagination_handler import ManualPaginationHandler
        
        # 测试创建实例
        handler = ManualPaginationHandler(None)
        
        # 检查关键方法
        required_methods = [
            'load_urls_from_excel',
            'save_urls_to_excel', 
            'create_template_excel',
            'process_manual_pagination',
            'get_all_articles'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(handler, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"   ❌ 缺少方法: {missing_methods}")
        else:
            print("   ✅ 手动翻页处理器功能完整")
            success_count += 1
            
    except ImportError as e:
        print(f"   ❌ 无法导入手动翻页处理器: {e}")
    except Exception as e:
        print(f"   ❌ 手动翻页处理器测试失败: {e}")
    
    # 3. 检查GUI集成
    print("\n3. 检查GUI集成...")
    total_checks += 1
    
    try:
        # 检查GUI文件中是否包含手动翻页相关代码
        gui_file = "gui/main_window.py"
        if os.path.exists(gui_file):
            with open(gui_file, 'r', encoding='utf-8') as f:
                gui_content = f.read()
            
            required_gui_elements = [
                "手动翻页",
                "manual_pagination_group",
                "excel_file_edit",
                "browse_excel_file",
                "create_excel_template"
            ]
            
            missing_elements = []
            for element in required_gui_elements:
                if element not in gui_content:
                    missing_elements.append(element)
            
            if missing_elements:
                print(f"   ❌ GUI缺少元素: {missing_elements}")
            else:
                print("   ✅ GUI集成完整")
                success_count += 1
        else:
            print(f"   ❌ GUI文件不存在: {gui_file}")
            
    except Exception as e:
        print(f"   ❌ GUI集成检查失败: {e}")
    
    # 4. 检查爬虫线程集成
    print("\n4. 检查爬虫线程集成...")
    total_checks += 1
    
    try:
        # 检查爬虫线程文件
        thread_file = "gui/crawler_thread.py"
        if os.path.exists(thread_file):
            with open(thread_file, 'r', encoding='utf-8') as f:
                thread_content = f.read()
            
            required_thread_elements = [
                "run_manual_pagination",
                "_async_manual_pagination",
                "ManualPaginationHandler"
            ]
            
            missing_elements = []
            for element in required_thread_elements:
                if element not in thread_content:
                    missing_elements.append(element)
            
            if missing_elements:
                print(f"   ❌ 爬虫线程缺少元素: {missing_elements}")
            else:
                print("   ✅ 爬虫线程集成完整")
                success_count += 1
        else:
            print(f"   ❌ 爬虫线程文件不存在: {thread_file}")
            
    except Exception as e:
        print(f"   ❌ 爬虫线程集成检查失败: {e}")
    
    # 5. 检查Excel文件
    print("\n5. 检查Excel文件...")
    total_checks += 1
    
    try:
        import pandas as pd
        
        excel_file = "manual_pagination/url_templates.xlsx"
        if os.path.exists(excel_file):
            df = pd.read_excel(excel_file)
            
            required_columns = ['URL', '页面名称', '状态', '文章数量', '备注']
            missing_columns = []
            
            for col in required_columns:
                if col not in df.columns:
                    missing_columns.append(col)
            
            if missing_columns:
                print(f"   ❌ Excel缺少列: {missing_columns}")
            else:
                print(f"   ✅ Excel文件格式正确，包含 {len(df)} 行数据")
                success_count += 1
        else:
            print(f"   ❌ Excel文件不存在: {excel_file}")
            
    except ImportError:
        print("   ❌ 缺少pandas库，无法检查Excel文件")
    except Exception as e:
        print(f"   ❌ Excel文件检查失败: {e}")
    
    # 总结
    print(f"\n📊 验证结果:")
    print(f"   通过: {success_count}/{total_checks}")
    print(f"   成功率: {success_count/total_checks*100:.1f}%")
    
    if success_count == total_checks:
        print("\n🎉 手动翻页功能集成完整！")
        print("💡 您现在可以在GUI中使用手动翻页功能了")
        return True
    else:
        print(f"\n⚠️ 发现 {total_checks - success_count} 个问题需要修复")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 手动翻页功能使用说明:")
    print("=" * 50)
    print("1. 启动GUI: python main.py")
    print("2. 选择'动态翻页'标签页")
    print("3. 在翻页类型中选择'手动翻页'")
    print("4. 点击'创建Excel模板'创建URL列表文件")
    print("5. 点击'编辑Excel文件'填写要爬取的URL")
    print("6. 配置其他参数（文章选择器等）")
    print("7. 点击'开始爬取'")
    print("\n📋 Excel文件格式:")
    print("   - URL: 要爬取的页面地址")
    print("   - 页面名称: 页面描述")
    print("   - 状态: 处理状态（自动更新）")
    print("   - 文章数量: 提取的文章数（自动更新）")
    print("   - 备注: 其他说明")
    print("\n📁 相关文件:")
    print("   - manual_pagination/url_templates.xlsx: 主要的URL列表文件")
    print("   - manual_pagination/README.md: 详细使用说明")
    print("   - manual_pagination/examples/: 示例文件")

if __name__ == "__main__":
    print("🚀 手动翻页功能验证")
    
    success = verify_manual_pagination_integration()
    
    if success:
        show_usage_instructions()
    else:
        print("\n🔧 请检查上述问题并修复后再试")
    
    print(f"\n📄 详细文档: manual_pagination/README.md")
