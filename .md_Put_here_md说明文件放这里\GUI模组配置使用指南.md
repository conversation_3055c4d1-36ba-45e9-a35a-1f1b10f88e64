# GUI模组配置使用指南

## 🎉 问题已修复

之前遇到的 `'CrawlerGUI' object has no attribute 'save_dir'` 错误已经完全修复！

## ✅ 修复内容

1. **添加了保存目录选择功能** - 在失败URL处理组中新增保存目录选择
2. **修复了属性引用错误** - 正确引用GUI控件属性
3. **完善了界面布局** - 优化了失败URL处理组的布局

## 🚀 完整功能列表

### 模组配置标签页包含：

#### 1. 模组库管理组
- ✅ **模组开关** - "启用模组配置系统"复选框
- ✅ **模组列表** - 下拉选择可用模组
- ✅ **模组信息** - 显示选中模组的详细信息
- ✅ **管理按钮**：
  - 刷新模组 - 重新加载模组配置
  - 添加模组 - 创建新的模组配置
  - 编辑模组 - 修改现有模组
  - 删除模组 - 移除模组配置
  - 测试URL匹配 - 验证匹配规则

#### 2. 模组设置组
- ✅ **默认模组** - 设置默认使用的模组
- ✅ **匹配优先级** - 显示匹配优先级信息
- ✅ **配置文件** - 显示和打开配置文件

#### 3. 失败URL处理组（已修复）
- ✅ **失败文件选择** - 浏览并选择失败的CSV文件
- ✅ **保存目录选择** - 选择重试结果的保存目录
- ✅ **重试文件命名** - 自定义重试结果文件名
- ✅ **重试参数设置**：
  - 重试次数 (1-10次)
  - 重试间隔 (0.1-10.0秒)
  - 并发数 (1-20个线程)
- ✅ **处理按钮** - 一键处理失败URL

## 📖 详细使用方法

### 1. 启动GUI
```bash
python crawler_gui_new.py
```

### 2. 启用模组配置
1. 点击"模组配置"标签页
2. 勾选"启用模组配置系统"
3. 在模组列表中查看可用模组

### 3. 管理模组配置

#### 添加新模组
1. 点击"添加模组"按钮
2. 填写模组信息：
   - **模组名称**: 如"知乎文章"
   - **描述**: 如"处理知乎文章的模组配置"
   - **域名模式**: 如"zhihu.com, *.zhihu.com"
   - **URL模式**: 如".*zhihu\\.com/p/.*"
   - **选择器配置**: 各种CSS选择器
   - **爬取设置**: 模式、重试次数等
3. 点击"保存"

#### 编辑现有模组
1. 在模组列表中选择要编辑的模组
2. 点击"编辑模组"按钮
3. 修改配置参数
4. 点击"保存"

#### 测试URL匹配
1. 点击"测试URL匹配"按钮
2. 输入要测试的URL
3. 点击"测试匹配"
4. 查看匹配结果和配置详情

### 4. 处理失败URL

#### 选择失败文件
1. 在"失败文件"行点击"浏览"按钮
2. 选择失败的CSV文件（如 `上海人大_代表风采_failed.csv`）

#### 设置保存目录
1. 在"保存目录"行点击"浏览"按钮
2. 选择重试结果的保存目录
3. 或者直接输入目录路径（默认为"articles"）

#### 配置重试参数
1. **重试文件名**: 输入自定义文件名（可选）
2. **重试次数**: 设置重试次数（建议3次）
3. **重试间隔**: 设置重试间隔（建议1.0秒）
4. **并发数**: 设置并发线程数（建议5个）

#### 开始处理
1. 点击"处理失败URL"按钮
2. 系统会在后台处理失败的URL
3. 查看日志了解处理进度
4. 处理完成后会显示结果统计

## 🎯 使用场景示例

### 场景1: 处理微信公众号失败URL
1. 启用模组配置系统
2. 选择失败文件: `articles/微信文章_failed.csv`
3. 设置保存目录: `articles/微信重试`
4. 设置重试文件名: `微信公众号重试结果`
5. 点击处理，系统会自动使用微信模组配置

### 场景2: 添加新网站支持
1. 点击"添加模组"
2. 创建"新浪新闻"模组：
   - 域名模式: `news.sina.com.cn`
   - URL模式: `.*news\\.sina\\.com\\.cn.*`
   - 内容选择器: `.article-content, .content`
   - 标题选择器: `h1, .title`
3. 保存后即可自动处理新浪新闻URL

### 场景3: 测试配置是否正确
1. 点击"测试URL匹配"
2. 输入测试URL: `https://mp.weixin.qq.com/s/test123`
3. 查看匹配结果: 应该匹配到"微信公众号"模组
4. 验证配置参数是否正确

## 🔧 故障排除

### 常见问题

**Q: 模组列表为空**
A: 点击"刷新模组"按钮，或检查 `module_configs.json` 文件

**Q: 失败URL处理没有效果**
A: 确认模组配置系统已启用，检查失败文件格式

**Q: URL匹配不正确**
A: 使用"测试URL匹配"功能验证，调整域名模式或URL模式

**Q: 保存目录无法选择**
A: 确保有足够的文件系统权限，或手动输入目录路径

### 日志信息说明

- `✅ 模组配置系统已启用` - 系统正常启用
- `🔄 开始处理失败URL文件` - 开始处理
- `📁 保存目录: xxx` - 使用的保存目录
- `📝 重试文件名: xxx` - 使用的文件名
- `✅ 失败URL处理完成!` - 处理完成

## 🎊 总结

现在您拥有了一个完整、稳定的GUI模组配置系统，可以：

✅ **可视化管理模组** - 通过GUI轻松管理所有模组配置
✅ **自定义保存目录** - 灵活选择重试结果保存位置
✅ **批量处理失败URL** - 一键处理所有失败的URL
✅ **实时测试验证** - 确保配置正确有效
✅ **完全向后兼容** - 不影响现有功能

立即体验新功能：`python crawler_gui_new.py` 🚀
