#!/usr/bin/env python3
"""
Test script for Tianjin Tax Bureau pagination analysis
"""

import asyncio
from playwright.async_api import async_playwright
import json
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_tjszx_pagination():
    """Test pagination for Tianjin Tax Bureau website"""
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=1000)  # Visible for debugging
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            locale='zh-CN'
        )
        page = await context.new_page()
        
        try:
            print("Navigating to:", url)
            await page.goto(url, wait_until='networkidle')
            
            # Take initial screenshot
            await page.screenshot(path='tjszx_initial.png', full_page=True)
            
            # Analyze page structure
            print("\n=== Page Structure Analysis ===")
            
            # Check for common pagination elements
            pagination_selectors = [
                '.pagination',
                '.page-nav',
                '.pager',
                '.fenye',
                '.pageination',
                '.page',
                '.pages',
                '.turn_page',
                '.page_turn',
                'div[class*="page"]',
                'div[class*="Page"]'
            ]
            
            found_elements = []
            for selector in pagination_selectors:
                elements = await page.query_selector_all(selector)
                if elements:
                    found_elements.append((selector, len(elements)))
                    print(f"Found {len(elements)} elements with selector: {selector}")
            
            # Find next/previous buttons
            print("\n=== Pagination Controls ===")
            
            next_selectors = [
                'a:has-text("下一页")',
                'a:has-text("下页")',
                'a:has-text("下一頁")',
                'a.next',
                'a.next-page',
                '.pagination .next',
                'a[title="下一页"]',
                'button:has-text("下一页")'
            ]
            
            prev_selectors = [
                'a:has-text("上一页")',
                'a:has-text("上页")',
                'a:has-text("上一頁")',
                'a.prev',
                'a.prev-page',
                '.pagination .prev',
                'a[title="上一页"]',
                'button:has-text("上一页")'
            ]
            
            next_btn = None
            for selector in next_selectors:
                btn = await page.query_selector(selector)
                if btn:
                    next_btn = btn
                    print(f"Next button found: {selector}")
                    break
            
            prev_btn = None
            for selector in prev_selectors:
                btn = await page.query_selector(selector)
                if btn:
                    prev_btn = btn
                    print(f"Previous button found: {selector}")
                    break
            
            # Extract page numbers
            print("\n=== Page Numbers ===")
            page_links = await page.query_selector_all('.pagination a, .page-nav a, .fenye a')
            page_numbers = []
            
            for link in page_links:
                text = await link.text_content()
                if text and text.strip().isdigit():
                    page_numbers.append(int(text.strip()))
            
            if page_numbers:
                print(f"Found page numbers: {page_numbers}")
                print(f"Total pages detected: {max(page_numbers)}")
            
            # Find current page
            current_selectors = [
                '.pagination .current',
                '.page-nav .active',
                '.fenye .current',
                '.current a',
                '.active a'
            ]
            
            current_page = None
            for selector in current_selectors:
                current = await page.query_selector(selector)
                if current:
                    current_text = await current.text_content()
                    if current_text and current_text.strip().isdigit():
                        current_page = int(current_text.strip())
                        print(f"Current page: {current_page}")
                        break
            
            # 循环点击“下一页”按钮，最多5次
            max_clicks = 5
            click_count = 0
            while click_count < max_clicks:
                # 查找当前可用的下一页按钮
                next_btn = None
                for selector in next_selectors:
                    btn = await page.query_selector(selector)
                    if btn:
                        is_disabled = await btn.evaluate('(el) => el.hasAttribute("disabled") || el.classList.contains("disabled")')
                        is_visible = await btn.is_visible()
                        if not is_disabled and is_visible:
                            next_btn = btn
                            print(f"Next button found: {selector}")
                            break
                if not next_btn:
                    print("No more next button found or all are disabled.")
                    break

                print(f"\n=== Testing Pagination: Click {click_count+1} ===")
                initial_url = page.url
                print(f"Current URL: {initial_url}")

                await next_btn.click()
                await page.wait_for_load_state('networkidle')
                await page.wait_for_timeout(2000)

                new_url = page.url
                print(f"New URL after pagination: {new_url}")
                await page.screenshot(path=f'tjszx_paginated_{click_count+1}.png', full_page=True)

                if initial_url == new_url:
                    print("URL did not change after click, may be last page or AJAX load.")
                else:
                    print("URL changed - using URL-based pagination")
                    from urllib.parse import urlparse, parse_qs
                    parsed = urlparse(new_url)
                    params = parse_qs(parsed.query)
                    print("Query parameters found:", params)
                    page_params = ['page', 'p', 'pn', 'currentPage', 'pageNo', 'pageNum', 'curPage']
                    for param in page_params:
                        if param in params:
                            print(f"Pagination parameter detected: {param} = {params[param][0]}")

                # 可选：采集当前页数据
                # Find list items
                list_selectors = [
                    '.list-content li',
                    '.list-box li',
                    '.news-list li',
                    '.content-list li',
                    'ul li'
                ]
                items = []
                for selector in list_selectors:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        items = elements
                        print(f"Found {len(elements)} items with selector: {selector}")
                        break
                if items:
                    print("\nSample data from first 3 items:")
                    for i, item in enumerate(items[:3]):
                        title_elem = await item.query_selector('a')
                        date_elem = await item.query_selector('span')
                        if title_elem:
                            title = await title_elem.text_content()
                            link = await title_elem.get_attribute('href')
                            print(f"{i+1}. Title: {title.strip()}")
                            print(f"   Link: {link}")
                        if date_elem:
                            date = await date_elem.text_content()
                            print(f"   Date: {date.strip()}")
                        print()

                click_count += 1
            
            # Extract data from current page
            print("\n=== Data Extraction Test ===")
            
            # Find list items
            list_selectors = [
                '.list-content li',
                '.list-box li',
                '.news-list li',
                '.content-list li',
                'ul li'
            ]
            
            items = []
            for selector in list_selectors:
                elements = await page.query_selector_all(selector)
                if elements:
                    items = elements
                    print(f"Found {len(elements)} items with selector: {selector}")
                    break
            
            if items:
                print("\nSample data from first 3 items:")
                for i, item in enumerate(items[:3]):
                    title_elem = await item.query_selector('a')
                    date_elem = await item.query_selector('span')
                    
                    if title_elem:
                        title = await title_elem.text_content()
                        link = await title_elem.get_attribute('href')
                        print(f"{i+1}. Title: {title.strip()}")
                        print(f"   Link: {link}")
                    
                    if date_elem:
                        date = await date_elem.text_content()
                        print(f"   Date: {date.strip()}")
                    
                    print()
            
            await browser.close()
            
            return {
                'url': url,
                'pagination_found': bool(next_btn),
                'current_page': current_page,
                'total_pages': max(page_numbers) if page_numbers else None,
                'next_selector': next_selectors[[i for i, s in enumerate(next_selectors) if await page.query_selector(s)][0]] if next_btn else None,
                'items_found': len(items)
            }
            
        except Exception as e:
            print(f"Error: {e}")
            await page.screenshot(path='tjszx_error.png')
            await browser.close()
            raise e

if __name__ == "__main__":
    result = asyncio.run(test_tjszx_pagination())
    print("\n=== Summary ===")
    print(json.dumps(result, indent=2, ensure_ascii=False))