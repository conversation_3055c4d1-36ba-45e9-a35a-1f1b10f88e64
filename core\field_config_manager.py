#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段配置管理器
提供字段配置的加载、保存、预设管理等功能
"""

import json
import os
import logging
from typing import Dict, List, Any, Optional
from .field_extractor import get_field_extractor, configure_fields, add_extended_fields

logger = logging.getLogger(__name__)

class FieldConfigManager:
    """字段配置管理器"""
    
    def __init__(self, config_file: str = "configs/fields/field_configs.json"):
        """初始化配置管理器"""
        self.config_file = config_file
        self.config_data = {}
        self.load_config()
    
    def load_config(self):
        """加载字段配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                logger.info(f"已加载字段配置: {self.config_file}")
            else:
                logger.warning(f"字段配置文件不存在: {self.config_file}")
                self.config_data = self._get_default_config()
        except Exception as e:
            logger.error(f"加载字段配置失败: {e}")
            self.config_data = self._get_default_config()
    
    def save_config(self):
        """保存字段配置文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存字段配置: {self.config_file}")
        except Exception as e:
            logger.error(f"保存字段配置失败: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "default_fields": {
                "dateget": {
                    "name": "发布日期",
                    "type": "text",
                    "selectors": [".date", ".publish-date", ".article-date", "time", ".time"],
                    "required": False,
                    "default": "",
                    "extractor": "date_extractor"
                },
                "source": {
                    "name": "来源",
                    "type": "text",
                    "selectors": [".source", ".author", ".publisher", ".from"],
                    "required": False,
                    "default": "本站",
                    "extractor": "text_extractor"
                },
                "title": {
                    "name": "标题",
                    "type": "text",
                    "selectors": ["h1", ".title", ".article-title", "h2", ".news-title"],
                    "required": True,
                    "default": "",
                    "extractor": "text_extractor"
                },
                "articlelink": {
                    "name": "文章链接",
                    "type": "url",
                    "selectors": [],
                    "required": True,
                    "default": "",
                    "extractor": "url_extractor"
                },
                "content": {
                    "name": "内容",
                    "type": "html",
                    "selectors": [".content", ".article-content", ".main-text"],
                    "required": True,
                    "default": "",
                    "extractor": "content_extractor"
                },
                "classid": {
                    "name": "分类ID",
                    "type": "text",
                    "selectors": [],
                    "required": False,
                    "default": "",
                    "extractor": "static_extractor"
                },
                "city": {
                    "name": "城市",
                    "type": "text",
                    "selectors": [],
                    "required": False,
                    "default": "上海",
                    "extractor": "static_extractor"
                },
                "getdate": {
                    "name": "采集时间",
                    "type": "datetime",
                    "selectors": [],
                    "required": False,
                    "default": "",
                    "extractor": "timestamp_extractor"
                }
            },
            "extended_fields": {},
            "field_presets": {
                "basic": ["dateget", "source", "title", "articlelink", "content", "classid", "city", "getdate"]
            }
        }
    
    def get_available_fields(self) -> Dict[str, Dict[str, Any]]:
        """获取所有可用字段"""
        all_fields = {}
        all_fields.update(self.config_data.get("default_fields", {}))
        all_fields.update(self.config_data.get("extended_fields", {}))
        return all_fields
    
    def get_field_presets(self) -> Dict[str, List[str]]:
        """获取字段预设"""
        return self.config_data.get("field_presets", {})
    
    def apply_preset(self, preset_name: str) -> bool:
        """应用字段预设"""
        presets = self.get_field_presets()
        if preset_name not in presets:
            logger.error(f"未找到预设: {preset_name}")
            return False
        
        field_names = presets[preset_name]
        all_fields = self.get_available_fields()
        
        # 构建字段配置
        field_config = {}
        for field_name in field_names:
            if field_name in all_fields:
                field_config[field_name] = all_fields[field_name]
            else:
                logger.warning(f"预设中的字段不存在: {field_name}")
        
        # 应用配置
        configure_fields(field_config)
        logger.info(f"已应用字段预设: {preset_name}, 包含 {len(field_config)} 个字段")
        return True
    
    def apply_custom_fields(self, field_names: List[str]) -> bool:
        """应用自定义字段列表"""
        all_fields = self.get_available_fields()
        
        # 构建字段配置
        field_config = {}
        for field_name in field_names:
            if field_name in all_fields:
                field_config[field_name] = all_fields[field_name]
            else:
                logger.warning(f"字段不存在: {field_name}")
        
        if not field_config:
            logger.error("没有有效的字段配置")
            return False
        
        # 应用配置
        configure_fields(field_config)
        logger.info(f"已应用自定义字段: {field_names}, 包含 {len(field_config)} 个字段")
        return True
    
    def add_custom_field(self, field_name: str, field_config: Dict[str, Any], 
                        save_to_file: bool = True) -> bool:
        """添加自定义字段"""
        try:
            # 添加到扩展字段
            if "extended_fields" not in self.config_data:
                self.config_data["extended_fields"] = {}
            
            self.config_data["extended_fields"][field_name] = field_config
            
            # 保存到文件
            if save_to_file:
                self.save_config()
            
            logger.info(f"已添加自定义字段: {field_name}")
            return True
            
        except Exception as e:
            logger.error(f"添加自定义字段失败: {e}")
            return False
    
    def remove_field(self, field_name: str, save_to_file: bool = True) -> bool:
        """移除字段"""
        try:
            removed = False
            
            # 从扩展字段中移除
            if field_name in self.config_data.get("extended_fields", {}):
                del self.config_data["extended_fields"][field_name]
                removed = True
            
            # 从默认字段中移除（谨慎操作）
            if field_name in self.config_data.get("default_fields", {}):
                del self.config_data["default_fields"][field_name]
                removed = True
            
            if removed:
                if save_to_file:
                    self.save_config()
                logger.info(f"已移除字段: {field_name}")
                return True
            else:
                logger.warning(f"字段不存在: {field_name}")
                return False
                
        except Exception as e:
            logger.error(f"移除字段失败: {e}")
            return False
    
    def create_preset(self, preset_name: str, field_names: List[str], 
                     save_to_file: bool = True) -> bool:
        """创建字段预设"""
        try:
            if "field_presets" not in self.config_data:
                self.config_data["field_presets"] = {}
            
            self.config_data["field_presets"][preset_name] = field_names
            
            if save_to_file:
                self.save_config()
            
            logger.info(f"已创建字段预设: {preset_name}, 包含字段: {field_names}")
            return True
            
        except Exception as e:
            logger.error(f"创建字段预设失败: {e}")
            return False
    
    def get_field_info(self, field_name: str) -> Optional[Dict[str, Any]]:
        """获取字段信息"""
        all_fields = self.get_available_fields()
        return all_fields.get(field_name)
    
    def list_fields(self) -> Dict[str, List[str]]:
        """列出所有字段"""
        return {
            "default_fields": list(self.config_data.get("default_fields", {}).keys()),
            "extended_fields": list(self.config_data.get("extended_fields", {}).keys()),
            "presets": list(self.config_data.get("field_presets", {}).keys())
        }


# 全局字段配置管理器实例
_field_config_manager = None


def get_field_config_manager() -> FieldConfigManager:
    """获取全局字段配置管理器实例"""
    global _field_config_manager
    if _field_config_manager is None:
        _field_config_manager = FieldConfigManager()
    return _field_config_manager


def apply_field_preset(preset_name: str) -> bool:
    """应用字段预设的便捷函数"""
    manager = get_field_config_manager()
    return manager.apply_preset(preset_name)


def apply_custom_field_list(field_names: List[str]) -> bool:
    """应用自定义字段列表的便捷函数"""
    manager = get_field_config_manager()
    return manager.apply_custom_fields(field_names)


def get_available_field_names() -> List[str]:
    """获取所有可用字段名称"""
    manager = get_field_config_manager()
    return list(manager.get_available_fields().keys())


def get_field_presets() -> Dict[str, List[str]]:
    """获取所有字段预设"""
    manager = get_field_config_manager()
    return manager.get_field_presets()
