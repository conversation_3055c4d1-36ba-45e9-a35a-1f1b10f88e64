#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网站翻页功能检测工具 - GUI版本
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import asyncio
import threading
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.pagination_detector import PaginationDetector

class PaginationDetectorGUI:
    """翻页检测工具GUI"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("网站翻页功能检测工具")
        self.root.geometry("800x600")
        
        self.detector = PaginationDetector()
        self.is_detecting = False
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # URL输入
        url_frame = ttk.Frame(main_frame)
        url_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(url_frame, text="网站URL:").grid(row=0, column=0, sticky=tk.W)
        
        self.url_var = tk.StringVar(value="https://www.tjszx.gov.cn/tagz/taxd/index.shtml")
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=60)
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # 选项
        options_frame = ttk.Frame(main_frame)
        options_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.headless_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text="无头模式 (不显示浏览器)", 
                       variable=self.headless_var).grid(row=0, column=0, sticky=tk.W)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.detect_button = ttk.Button(button_frame, text="开始检测", 
                                       command=self.start_detection)
        self.detect_button.grid(row=0, column=0, padx=(0, 10))
        
        self.clear_button = ttk.Button(button_frame, text="清空结果", 
                                      command=self.clear_results)
        self.clear_button.grid(row=0, column=1)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="检测结果", padding="5")
        result_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.result_text = scrolledtext.ScrolledText(result_frame, height=25, width=80)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        url_frame.columnconfigure(1, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
    
    def start_detection(self):
        """开始检测"""
        if self.is_detecting:
            messagebox.showwarning("警告", "检测正在进行中，请等待完成")
            return
        
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("错误", "请输入网站URL")
            return
        
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            self.url_var.set(url)
        
        self.is_detecting = True
        self.detect_button.config(state='disabled')
        self.progress.start()
        
        # 在新线程中运行检测
        thread = threading.Thread(target=self.run_detection, args=(url,))
        thread.daemon = True
        thread.start()
    
    def run_detection(self, url):
        """在后台线程中运行检测"""
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 运行检测
            result = loop.run_until_complete(
                self.detector.detect_pagination(url, self.headless_var.get())
            )
            
            # 在主线程中更新UI
            self.root.after(0, self.update_results, result)
            
        except Exception as e:
            error_msg = f"检测过程中出错: {str(e)}"
            self.root.after(0, self.show_error, error_msg)
        finally:
            self.root.after(0, self.detection_finished)
    
    def update_results(self, result):
        """更新检测结果"""
        self.result_text.delete(1.0, tk.END)
        
        # 格式化结果显示
        output = []
        output.append("=" * 60)
        output.append("网站翻页功能检测结果")
        output.append("=" * 60)
        output.append(f"网站URL: {result['url']}")
        output.append(f"翻页功能: {'✅ 有' if result['has_pagination'] else '❌ 无'}")
        output.append(f"翻页类型: {result['pagination_type']}")
        
        if result.get('page_count_info'):
            output.append(f"页码信息: {result['page_count_info']}")
        
        output.append("")
        
        # 详细信息
        details = result.get('details', {})
        
        # 翻页按钮
        buttons = details.get('buttons', {})
        if buttons.get('count', 0) > 0:
            output.append("🔘 翻页按钮:")
            for i, button in enumerate(buttons['buttons'], 1):
                output.append(f"  [{i}] 选择器: {button['selector']}")
                output.append(f"      标签: {button['tag']}")
                if button['tag'] == 'input':
                    output.append(f"      值: {button.get('value', '')}")
                    output.append(f"      类型: {button.get('type', '')}")
                else:
                    output.append(f"      文本: {button.get('text', '')}")
                    output.append(f"      链接: {button.get('href', '')}")
                output.append("")
        
        # 翻页链接
        links = details.get('links', {})
        if links.get('count', 0) > 0:
            output.append("🔗 翻页链接:")
            for i, link in enumerate(links['links'][:5], 1):  # 只显示前5个
                output.append(f"  [{i}] 选择器: {link['selector']}")
                output.append(f"      链接: {link['href']}")
                output.append(f"      文本: {link['text']}")
                output.append("")
        
        # 页码信息
        page_info = details.get('page_info', {})
        if page_info.get('found'):
            output.append("📄 页码信息:")
            for pattern in page_info['patterns'][:3]:  # 只显示前3个
                output.append(f"  - {pattern}")
            output.append("")
        
        # 表单翻页
        forms = details.get('forms', {})
        if forms.get('count', 0) > 0:
            output.append("📝 表单翻页:")
            output.append(f"  找到 {forms['count']} 个相关表单")
            output.append("")
        
        # JavaScript翻页
        scripts = details.get('scripts', {})
        if scripts.get('found'):
            output.append("📜 JavaScript翻页:")
            output.append("  检测到JavaScript翻页模式")
            output.append("")
        
        # 无限滚动
        scroll = details.get('infinite_scroll', {})
        if scroll.get('detected'):
            output.append("📜 无限滚动:")
            output.append("  检测到无限滚动功能")
            output.append(f"  页面高度变化: {scroll['initial_height']} → {scroll['final_height']}")
            output.append("")
        
        # 推荐选择器
        if result.get('recommended_selectors'):
            output.append("🎯 推荐选择器:")
            for selector in result['recommended_selectors']:
                output.append(f"  - {selector}")
            output.append("")
        
        # 使用建议
        if result.get('suggestions'):
            output.append("💡 使用建议:")
            for suggestion in result['suggestions']:
                output.append(f"  • {suggestion}")
            output.append("")
        
        # 错误信息
        if result.get('error'):
            output.append("❌ 错误信息:")
            output.append(f"  {result['error']}")
        
        self.result_text.insert(tk.END, "\n".join(output))
    
    def show_error(self, error_msg):
        """显示错误信息"""
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, f"❌ {error_msg}")
        messagebox.showerror("检测失败", error_msg)
    
    def detection_finished(self):
        """检测完成"""
        self.is_detecting = False
        self.detect_button.config(state='normal')
        self.progress.stop()
    
    def clear_results(self):
        """清空结果"""
        self.result_text.delete(1.0, tk.END)

def main():
    """主函数"""
    root = tk.Tk()
    app = PaginationDetectorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
