#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的开关测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    print("开始简单开关测试...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        print("创建主窗口...")
        window = CrawlerGUI()
        print("主窗口创建成功")
        
        # 检查字段配置开关
        if hasattr(window, 'enable_field_config_checkbox'):
            print("✅ 字段配置开关存在")
            
            # 检查初始状态
            initial_state = window.enable_field_config_checkbox.isChecked()
            print(f"初始状态: {initial_state}")
            
            # 测试启用
            print("测试启用开关...")
            window.enable_field_config_checkbox.setChecked(True)
            enabled_state = window.enable_field_config_checkbox.isChecked()
            print(f"启用后状态: {enabled_state}")
            
            # 检查受控组件
            components_to_check = [
                'field_config_area',
                'custom_fields_group', 
                'field_preview_group',
                'field_actions_group'
            ]
            
            print("检查受控组件状态:")
            for component_name in components_to_check:
                if hasattr(window, component_name):
                    component = getattr(window, component_name)
                    is_enabled = component.isEnabled()
                    print(f"  {component_name}: {'启用' if is_enabled else '禁用'}")
                else:
                    print(f"  {component_name}: 不存在")
            
            # 测试禁用
            print("测试禁用开关...")
            window.enable_field_config_checkbox.setChecked(False)
            disabled_state = window.enable_field_config_checkbox.isChecked()
            print(f"禁用后状态: {disabled_state}")
            
            print("禁用后组件状态:")
            for component_name in components_to_check:
                if hasattr(window, component_name):
                    component = getattr(window, component_name)
                    is_enabled = component.isEnabled()
                    print(f"  {component_name}: {'启用' if is_enabled else '禁用'}")
                else:
                    print(f"  {component_name}: 不存在")
                    
        else:
            print("❌ 字段配置开关不存在")
            
        print("测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
