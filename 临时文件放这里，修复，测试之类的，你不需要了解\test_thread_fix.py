#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试线程修复
"""

def test_function_signature():
    """测试函数签名是否匹配"""
    print("🔧 测试函数签名...")
    
    try:
        import inspect
        from crawler import crawl_articles_async
        
        # 获取函数签名
        sig = inspect.signature(crawl_articles_async)
        params = list(sig.parameters.keys())
        
        print(f"✅ crawl_articles_async 函数参数:")
        for i, param in enumerate(params, 1):
            print(f"  {i:2d}. {param}")
        
        # 检查关键参数
        required_params = ['progress_callback', 'stop_check_callback']
        for param in required_params:
            if param in params:
                print(f"✅ 参数 {param} 存在")
            else:
                print(f"❌ 参数 {param} 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_gui_thread_config():
    """测试GUI线程配置"""
    print("\n🔧 测试GUI线程配置...")
    
    try:
        from gui_crawler_thread import CrawlerThreadManager
        
        # 创建线程管理器实例
        manager = CrawlerThreadManager()
        
        # 测试配置准备
        test_config = {
            'input_url': 'https://example.com',
            'content_selectors': ['.content'],
            'title_selectors': ['h1'],
            'progress_callback': lambda x, y: None,
            'stop_check_callback': lambda: False,
            'unsupported_param': 'should_be_filtered'
        }
        
        manager.config = test_config
        
        # 调用配置准备方法
        filtered_config = manager._prepare_crawler_config()
        
        print(f"✅ 原始配置参数: {len(test_config)}")
        print(f"✅ 过滤后参数: {len(filtered_config)}")
        
        # 检查关键参数
        if 'progress_callback' in filtered_config:
            print("✅ progress_callback 参数保留")
        else:
            print("❌ progress_callback 参数被过滤")
        
        if 'unsupported_param' not in filtered_config:
            print("✅ 不支持的参数被正确过滤")
        else:
            print("❌ 不支持的参数未被过滤")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_compatibility():
    """测试参数兼容性"""
    print("\n🔧 测试参数兼容性...")
    
    try:
        import inspect
        from crawler import crawl_articles_async
        from gui_crawler_thread import CrawlerThreadManager
        
        # 获取函数支持的参数
        sig = inspect.signature(crawl_articles_async)
        function_params = set(sig.parameters.keys())
        
        # 获取GUI线程支持的参数
        manager = CrawlerThreadManager()
        # 通过查看源码获取支持的参数列表
        supported_params = {
            'all_articles', 'input_url', 'base_url', 'max_pages',
            'list_container_selector', 'list_container_type',
            'article_item_selector', 'article_item_type',
            'title_selectors', 'date_selectors', 'source_selectors',
            'title_selector', 'date_selector', 'source_selector',
            'title_selector_type', 'date_selector_type', 'source_selector_type',
            'content_selectors', 'content_type', 'log_callback',
            'page_suffix', 'url_mode', 'browser_type', 'headless',
            'collect_links', 'mode', 'filters', 'export_filename',
            'classid', 'file_format', 'dynamic_pagination_type',
            'max_workers', 'retry', 'interval', 'stop_check_callback',
            'progress_callback'
        }
        
        print(f"✅ 函数支持参数: {len(function_params)}")
        print(f"✅ GUI线程支持参数: {len(supported_params)}")
        
        # 检查不匹配的参数
        gui_only = supported_params - function_params
        function_only = function_params - supported_params
        
        if gui_only:
            print(f"⚠️ GUI支持但函数不支持的参数: {gui_only}")
        
        if function_only:
            print(f"⚠️ 函数支持但GUI不支持的参数: {function_only}")
        
        if not gui_only and not function_only:
            print("✅ 参数完全匹配")
        
        return len(gui_only) == 0  # GUI不应该有函数不支持的参数
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_simple_call():
    """测试简单调用"""
    print("\n🔧 测试简单调用...")
    
    try:
        import asyncio
        from crawler import crawl_articles_async
        
        async def test_call():
            # 测试最简单的调用
            result = await crawl_articles_async(
                all_articles=[],  # 空文章列表
                progress_callback=lambda x, y: None,
                stop_check_callback=lambda: False
            )
            return result
        
        # 运行测试
        result = asyncio.run(test_call())
        print(f"✅ 简单调用成功: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 简单调用失败: {e}")
        return False

def main():
    """主测试函数"""
    print("线程修复测试")
    print("=" * 40)
    
    tests = [
        ("函数签名测试", test_function_signature),
        ("GUI线程配置测试", test_gui_thread_config),
        ("参数兼容性测试", test_parameter_compatibility),
        ("简单调用测试", test_simple_call),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 40)
    print("测试结果:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！线程问题已修复。")
        print("\n📋 修复内容:")
        print("✅ 添加了 progress_callback 参数到 crawl_articles_async")
        print("✅ 更新了 GUI 线程的支持参数列表")
        print("✅ 确保了参数兼容性")
        
        print("\n🎯 现在可以重新运行GUI:")
        print("1. 重新启动GUI程序")
        print("2. 尝试处理失败URL")
        print("3. 应该不再出现参数错误")
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件")

if __name__ == "__main__":
    main()
