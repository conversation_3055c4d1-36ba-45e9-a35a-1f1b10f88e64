#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建包结构并重新组织代码
"""

import os
import shutil
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_package_structure():
    """创建包结构"""
    packages = [
        'core',      # 核心爬虫功能
        'gui',       # GUI相关
        'ai',        # AI分析功能
        'modules',   # 模组管理
        'testing',   # 测试相关
        'config',    # 配置管理
        'utils'      # 通用工具
    ]
    
    for package in packages:
        if not os.path.exists(package):
            os.makedirs(package)
            logger.info(f"创建包目录: {package}")
        
        # 创建 __init__.py
        init_file = os.path.join(package, '__init__.py')
        if not os.path.exists(init_file):
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write(f'# {package} package\n')
            logger.info(f"创建 __init__.py: {init_file}")

def move_files():
    """移动文件到对应的包"""
    
    file_mappings = {
        # 核心爬虫功能
        'core': [
            'crawler.py',
            'failed_url_processor.py',
            'PaginationHandler.py'
        ],
        
        # GUI相关
        'gui': [
            ('crawler_gui_new.py', 'main_window.py'),
            ('gui_config_manager.py', 'config_manager.py'),
            ('gui_crawler_thread.py', 'crawler_thread.py'),
            ('gui_utils.py', 'utils.py')
        ],
        
        # AI分析功能
        'ai': [
            ('ai_selector_analyzer_enhanced.py', 'analyzer.py'),
            ('gui_ai_helper_enhanced.py', 'helper.py'),
            ('interactive_ai_analyzer.py', 'interactive.py')
        ],
        
        # 模组管理
        'modules': [
            ('module_manager.py', 'manager.py'),
            ('module_config_manager.py', 'config_manager.py')
        ],
        
        # 测试相关
        'testing': [
            'selectors_test.py',
            ('selectors_test_config.py', 'config.py')
        ],
        
        # 配置管理
        'config': [
            ('myconfig.py', 'manager.py')
        ],
        
        # 通用工具
        'utils': [
            ('txt_clear.py', 'text_cleaner.py'),
            ('playwright_optimized_config.py', 'playwright_config.py')
        ]
    }
    
    for package, files in file_mappings.items():
        for file_info in files:
            if isinstance(file_info, tuple):
                old_name, new_name = file_info
                old_path = old_name
                new_path = os.path.join(package, new_name)
            else:
                old_path = file_info
                new_path = os.path.join(package, file_info)
            
            if os.path.exists(old_path):
                try:
                    shutil.move(old_path, new_path)
                    logger.info(f"移动文件: {old_path} -> {new_path}")
                except Exception as e:
                    logger.error(f"移动文件失败 {old_path}: {e}")
            else:
                logger.warning(f"文件不存在: {old_path}")

def update_imports_in_file(file_path, import_mappings):
    """更新文件中的导入语句"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        for old_import, new_import in import_mappings.items():
            content = content.replace(old_import, new_import)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"更新导入语句: {file_path}")
        
    except Exception as e:
        logger.error(f"更新导入语句失败 {file_path}: {e}")

def update_all_imports():
    """更新所有文件的导入语句"""
    
    # 导入映射表
    import_mappings = {
        # 核心模块
        'import crawler': 'from core import crawler',
        'from crawler import': 'from core.crawler import',
        'import failed_url_processor': 'from core import failed_url_processor',
        'from failed_url_processor import': 'from core.failed_url_processor import',
        'import PaginationHandler': 'from core import PaginationHandler',
        'from PaginationHandler import': 'from core.PaginationHandler import',
        
        # GUI模块
        'import crawler_gui_new': 'from gui import main_window',
        'from crawler_gui_new import': 'from gui.main_window import',
        'import gui_config_manager': 'from gui import config_manager',
        'from gui_config_manager import': 'from gui.config_manager import',
        'import gui_crawler_thread': 'from gui import crawler_thread',
        'from gui_crawler_thread import': 'from gui.crawler_thread import',
        'import gui_utils': 'from gui import utils',
        'from gui_utils import': 'from gui.utils import',
        
        # AI模块
        'import ai_selector_analyzer_enhanced': 'from ai import analyzer',
        'from ai_selector_analyzer_enhanced import': 'from ai.analyzer import',
        'import gui_ai_helper_enhanced': 'from ai import helper',
        'from gui_ai_helper_enhanced import': 'from ai.helper import',
        'import interactive_ai_analyzer': 'from ai import interactive',
        'from interactive_ai_analyzer import': 'from ai.interactive import',
        
        # 模组模块
        'import module_manager': 'from modules import manager',
        'from module_manager import': 'from modules.manager import',
        'import module_config_manager': 'from modules import config_manager',
        'from module_config_manager import': 'from modules.config_manager import',
        
        # 测试模块
        'import selectors_test': 'from testing import selectors_test',
        'from selectors_test import': 'from testing.selectors_test import',
        'import selectors_test_config': 'from testing import config',
        'from selectors_test_config import': 'from testing.config import',
        
        # 配置模块
        'import myconfig': 'from config import manager',
        'from myconfig import': 'from config.manager import',
        
        # 工具模块
        'import txt_clear': 'from utils import text_cleaner',
        'from txt_clear import': 'from utils.text_cleaner import',
        'import playwright_optimized_config': 'from utils import playwright_config',
        'from playwright_optimized_config import': 'from utils.playwright_config import',
    }
    
    # 遍历所有包目录
    packages = ['core', 'gui', 'ai', 'modules', 'testing', 'config', 'utils']
    
    for package in packages:
        if os.path.exists(package):
            for root, dirs, files in os.walk(package):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        update_imports_in_file(file_path, import_mappings)

def create_main_entry():
    """创建主入口文件"""
    main_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫项目主入口
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    from gui.main_window import main as gui_main
    gui_main()

if __name__ == "__main__":
    main()
'''
    
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(main_content)
    logger.info("创建主入口文件: main.py")

def main():
    """主函数"""
    logger.info("开始重新组织代码结构...")
    
    # 1. 创建包结构
    create_package_structure()
    
    # 2. 移动文件
    move_files()
    
    # 3. 更新导入语句
    update_all_imports()
    
    # 4. 创建主入口
    create_main_entry()
    
    logger.info("代码结构重新组织完成！")
    
    print("\n📦 新的包结构:")
    print("├── core/                    # 核心爬虫功能")
    print("│   ├── crawler.py")
    print("│   ├── failed_url_processor.py")
    print("│   └── PaginationHandler.py")
    print("├── gui/                     # GUI相关")
    print("│   ├── main_window.py       # 主窗口")
    print("│   ├── config_manager.py    # 配置管理")
    print("│   ├── crawler_thread.py    # 爬虫线程")
    print("│   └── utils.py             # GUI工具")
    print("├── ai/                      # AI分析功能")
    print("│   ├── analyzer.py          # AI分析器")
    print("│   ├── helper.py            # AI助手")
    print("│   └── interactive.py       # 交互式工具")
    print("├── modules/                 # 模组管理")
    print("│   ├── manager.py           # 模组管理器")
    print("│   └── config_manager.py    # 模组配置管理")
    print("├── testing/                 # 测试相关")
    print("│   ├── selectors_test.py    # 选择器测试")
    print("│   └── config.py            # 测试配置")
    print("├── config/                  # 配置管理")
    print("│   └── manager.py           # 配置管理")
    print("├── utils/                   # 通用工具")
    print("│   ├── text_cleaner.py      # 文本清理")
    print("│   └── playwright_config.py # Playwright配置")
    print("└── main.py                  # 主入口文件")

if __name__ == "__main__":
    main()
