#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选择器测试模块
使用Playwright实际访问网站，测试基本配置组的选择器是否能正确提取预期内容
"""

import asyncio
import logging
import json
import os
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import urlparse
import time
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext

# 导入相关模块
try:
    from config.manager import ConfigManager
    CONFIG_MANAGER_AVAILABLE = True
except ImportError:
    CONFIG_MANAGER_AVAILABLE = False

try:
    from modules.manager import get_config_for_url, match_module_for_url, module_manager
    MODULE_MANAGER_AVAILABLE = True
except ImportError:
    MODULE_MANAGER_AVAILABLE = False

try:
    from core.crawler import launch_browser
    CRAWLER_AVAILABLE = True
except ImportError:
    CRAWLER_AVAILABLE = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('SelectorsTest')


class SelectorsTestManager:
    """选择器测试管理器 - 使用Playwright实际访问网站测试选择器"""

    def __init__(self, test_results_dir: str = "selector_test_results"):
        """
        初始化选择器测试管理器

        Args:
            test_results_dir: 测试结果保存目录
        """
        self.test_results_dir = test_results_dir
        self.ensure_test_dir()
        self.config_manager = ConfigManager() if CONFIG_MANAGER_AVAILABLE else None

    def ensure_test_dir(self):
        """确保测试目录存在"""
        if not os.path.exists(self.test_results_dir):
            os.makedirs(self.test_results_dir)

    async def test_selectors_on_page(self,
                                   page: Page,
                                   url: str,
                                   selectors_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        在页面上测试选择器配置

        Args:
            page: Playwright页面对象
            url: 测试URL
            selectors_config: 选择器配置

        Returns:
            测试结果字典
        """
        result = {
            'url': url,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'extractions': {},
            'errors': [],
            'success': False
        }

        try:
            # 首先检查页面是否有效
            if page.is_closed():
                raise Exception("页面已关闭，无法进行测试")

            # 访问页面
            logger.info(f"访问页面: {url}")

            # 使用健壮等待策略
            try:
                from utils.robust_wait_strategy import robust_goto
                success = await robust_goto(page, url, timeout=30000, preferred_strategy="domcontentloaded")
                if not success:
                    raise Exception("页面访问失败")
            except ImportError:
                # 回退到原有方式
                if not page.is_closed():
                    await page.goto(url, timeout=30000, wait_until="domcontentloaded")
                    # 等待页面加载
                    await page.wait_for_load_state('domcontentloaded', timeout=10000)
                else:
                    raise Exception("页面已关闭，无法使用回退方式")

            # 再次检查页面状态
            if page.is_closed():
                raise Exception("页面在访问后被关闭")

            # 特殊处理微信公众号
            if "mp.weixin.qq.com" in url and not page.is_closed():
                try:
                    await page.wait_for_selector("#js_content", timeout=10000)
                    await asyncio.sleep(3)  # 额外等待动态内容
                except:
                    logger.warning("微信公众号内容元素等待超时")

            # 测试各种选择器（只在页面有效时）
            if not page.is_closed():
                await self._test_content_selectors(page, selectors_config, result)
                await self._test_title_selectors(page, selectors_config, result)
                await self._test_date_selectors(page, selectors_config, result)
                await self._test_source_selectors(page, selectors_config, result)

                # 判断测试是否成功
                result['success'] = bool(result['extractions'].get('content'))
            else:
                raise Exception("页面在测试过程中被关闭")

        except Exception as e:
            error_msg = f"选择器测试失败: {str(e)}"
            result['errors'].append(error_msg)
            logger.error(error_msg)

            # 特殊处理页面关闭错误
            if "closed" in str(e).lower():
                result['errors'].append("页面或浏览器被意外关闭，这可能是由于并发访问或资源管理问题导致的")

        return result
    
    async def _test_content_selectors(self, page: Page, config: Dict, result: Dict):
        """测试内容选择器"""
        if page.is_closed():
            logger.warning("页面已关闭，跳过内容选择器测试")
            return

        content_selectors = config.get('content_selectors', [])
        if isinstance(content_selectors, str):
            content_selectors = [content_selectors]

        for selector in content_selectors:
            try:
                if page.is_closed():
                    logger.warning("页面在测试过程中被关闭")
                    break

                if config.get('content_type', 'CSS').upper() == 'XPATH':
                    elements = await page.query_selector_all(f"xpath={selector}")
                else:
                    elements = await page.query_selector_all(selector)

                if elements:
                    # 获取第一个元素的文本内容
                    content = await elements[0].text_content()
                    if content and content.strip():
                        result['extractions']['content'] = {
                            'selector': selector,
                            'text': content.strip()[:200] + "..." if len(content.strip()) > 200 else content.strip(),
                            'length': len(content.strip()),
                            'elements_found': len(elements)
                        }
                        logger.info(f"✅ 内容选择器 '{selector}' 找到 {len(elements)} 个元素，内容长度: {len(content.strip())}")
                        break
                    else:
                        logger.warning(f"⚠️ 内容选择器 '{selector}' 找到元素但内容为空")
                else:
                    logger.warning(f"❌ 内容选择器 '{selector}' 未找到元素")
            except Exception as e:
                error_msg = f"内容选择器 '{selector}' 测试失败: {str(e)}"
                result['errors'].append(error_msg)
                logger.error(error_msg)

    async def _test_title_selectors(self, page: Page, config: Dict, result: Dict):
        """测试标题选择器"""
        if page.is_closed():
            logger.warning("页面已关闭，跳过标题选择器测试")
            return

        title_selectors = config.get('title_selectors', [])
        if isinstance(title_selectors, str):
            title_selectors = [title_selectors]

        for selector in title_selectors:
            try:
                if page.is_closed():
                    logger.warning("页面在测试过程中被关闭")
                    break

                # 统一使用Playwright的自动检测，支持CSS和XPath混合
                element = await page.query_selector(selector)

                if element:
                    title = await element.text_content()
                    if title and title.strip():
                        result['extractions']['title'] = {
                            'selector': selector,
                            'text': title.strip(),
                            'length': len(title.strip())
                        }
                        logger.info(f"✅ 标题选择器 '{selector}' 提取到: {title.strip()}")
                        break
                    else:
                        logger.warning(f"⚠️ 标题选择器 '{selector}' 找到元素但内容为空")
                else:
                    logger.warning(f"❌ 标题选择器 '{selector}' 未找到元素")
            except Exception as e:
                error_msg = f"标题选择器 '{selector}' 测试失败: {str(e)}"
                result['errors'].append(error_msg)
                logger.error(error_msg)

    async def _test_date_selectors(self, page: Page, config: Dict, result: Dict):
        """测试日期选择器"""
        date_selectors = config.get('date_selectors', [])
        if isinstance(date_selectors, str):
            date_selectors = [date_selectors]

        for selector in date_selectors:
            try:
                # 统一使用Playwright的自动检测，支持CSS和XPath混合
                element = await page.query_selector(selector)

                if element:
                    date_text = await element.text_content()
                    if date_text and date_text.strip():
                        result['extractions']['date'] = {
                            'selector': selector,
                            'text': date_text.strip(),
                            'length': len(date_text.strip())
                        }
                        logger.info(f"✅ 日期选择器 '{selector}' 提取到: {date_text.strip()}")
                        break
                    else:
                        logger.warning(f"⚠️ 日期选择器 '{selector}' 找到元素但内容为空")
                else:
                    logger.warning(f"❌ 日期选择器 '{selector}' 未找到元素")
            except Exception as e:
                error_msg = f"日期选择器 '{selector}' 测试失败: {str(e)}"
                result['errors'].append(error_msg)
                logger.error(error_msg)

    async def _test_source_selectors(self, page: Page, config: Dict, result: Dict):
        """测试来源选择器"""
        source_selectors = config.get('source_selectors', [])
        if isinstance(source_selectors, str):
            source_selectors = [source_selectors]

        for selector in source_selectors:
            try:
                # 统一使用Playwright的自动检测，支持CSS和XPath混合
                element = await page.query_selector(selector)

                if element:
                    source_text = await element.text_content()
                    if source_text and source_text.strip():
                        result['extractions']['source'] = {
                            'selector': selector,
                            'text': source_text.strip(),
                            'length': len(source_text.strip())
                        }
                        logger.info(f"✅ 来源选择器 '{selector}' 提取到: {source_text.strip()}")
                        break
                    else:
                        logger.warning(f"⚠️ 来源选择器 '{selector}' 找到元素但内容为空")
                else:
                    logger.warning(f"❌ 来源选择器 '{selector}' 未找到元素")
            except Exception as e:
                error_msg = f"来源选择器 '{selector}' 测试失败: {str(e)}"
                result['errors'].append(error_msg)
                logger.error(error_msg)
    
    async def test_config_group(self, config_group_name: str, test_url: str = None) -> Dict[str, Any]:
        """
        测试基本配置组的选择器

        Args:
            config_group_name: 配置组名称
            test_url: 测试URL（如果不提供，使用配置组中的input_url）

        Returns:
            测试结果字典
        """
        if not self.config_manager:
            return {"error": "配置管理器不可用"}

        # 获取配置组
        config_group = self.config_manager.get_group(config_group_name)
        if not config_group:
            return {"error": f"配置组 '{config_group_name}' 不存在"}

        # 确定测试URL
        url = test_url or config_group.get('input_url')
        if not url:
            return {"error": "未提供测试URL且配置组中无input_url"}

        # 准备选择器配置
        selectors_config = {
            'content_selectors': config_group.get('content_selectors', []),
            'title_selectors': config_group.get('title_selectors', []),
            'date_selectors': config_group.get('date_selectors', []),
            'source_selectors': config_group.get('source_selectors', []),
            'content_type': config_group.get('content_type', 'CSS')
        }

        # 过滤空选择器
        for key in list(selectors_config.keys()):
            if isinstance(selectors_config[key], list):
                selectors_config[key] = [s for s in selectors_config[key] if s.strip()]

        logger.info(f"测试配置组 '{config_group_name}' 的选择器配置")

        # 使用Playwright测试
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            page = await context.new_page()

            try:
                result = await self.test_selectors_on_page(page, url, selectors_config)
                result['config_group'] = config_group_name
                result['config'] = selectors_config

                return result
            finally:
                await browser.close()

    async def test_module_config(self, url: str) -> Dict[str, Any]:
        """
        测试URL的模组配置

        Args:
            url: 要测试的URL

        Returns:
            测试结果字典
        """
        if not MODULE_MANAGER_AVAILABLE:
            return {"error": "模组管理器不可用"}

        # 获取模组配置
        module_name = match_module_for_url(url)
        module_config = get_config_for_url(url)

        logger.info(f"URL {url} 匹配到模组: {module_name}")

        if not module_config:
            return {
                'url': url,
                'success': False,
                'error': '未找到匹配的模组配置',
                'module_name': module_name,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }

        # 使用Playwright测试
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            page = await context.new_page()

            try:
                result = await self.test_selectors_on_page(page, url, module_config)
                result['module_name'] = module_name
                result['module_config'] = module_config

                return result
            finally:
                await browser.close()
    
    async def test_all_config_groups(self) -> List[Dict[str, Any]]:
        """
        测试所有基本配置组

        Returns:
            测试结果列表
        """
        if not self.config_manager:
            return [{"error": "配置管理器不可用"}]

        results = []
        config_groups = self.config_manager.get_groups()

        logger.info(f"开始测试 {len(config_groups)} 个配置组")

        for i, config_name in enumerate(config_groups):
            logger.info(f"测试进度: {i+1}/{len(config_groups)} - {config_name}")

            try:
                result = await self.test_config_group(config_name)
                results.append(result)
            except Exception as e:
                error_result = {
                    'config_group': config_name,
                    'success': False,
                    'error': f"测试异常: {str(e)}",
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                results.append(error_result)
                logger.error(f"测试配置组 '{config_name}' 时出错: {e}")

            # 添加延迟避免过于频繁的请求
            await asyncio.sleep(2)

        return results

    async def quick_test_url(self, url: str, show_preview: bool = True) -> Dict[str, Any]:
        """
        快速测试单个URL，显示页面内容预览

        Args:
            url: 测试URL
            show_preview: 是否显示内容预览

        Returns:
            测试结果字典
        """
        logger.info(f"快速测试URL: {url}")

        # 首先尝试模组配置
        if MODULE_MANAGER_AVAILABLE:
            module_result = await self.test_module_config(url)
            if module_result.get('success'):
                if show_preview:
                    self._show_extraction_preview(module_result)
                return module_result

        # 如果模组配置失败，尝试通用选择器
        generic_config = {
            'content_selectors': [
                '#js_content',  # 微信公众号
                '.article_cont', '.article-content', '.content', '.zhengwen',
                '.TRS_Editor', 'div[class*="content"]', '.post-content'
            ],
            'title_selectors': [
                'h1', '.title', '.article-title', '.post-title',
                '#activity-name', '.rich_media_title'
            ],
            'date_selectors': [
                '.date', '.time', '.publish-time', '.post-date',
                '#publish_time', '.rich_media_meta_text'
            ],
            'source_selectors': [
                '.source', '.author', '.rich_media_meta_nickname'
            ]
        }

        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            page = await context.new_page()

            try:
                result = await self.test_selectors_on_page(page, url, generic_config)
                result['test_type'] = 'generic_selectors'

                if show_preview:
                    self._show_extraction_preview(result)

                return result
            finally:
                await browser.close()
    
    def _show_extraction_preview(self, result: Dict[str, Any]):
        """显示提取内容预览"""
        print(f"\n{'='*60}")
        print(f"URL: {result.get('url', 'Unknown')}")
        print(f"测试时间: {result.get('timestamp', 'Unknown')}")
        print(f"测试结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")

        if result.get('module_name'):
            print(f"匹配模组: {result['module_name']}")

        extractions = result.get('extractions', {})

        if extractions.get('title'):
            print(f"\n📝 标题:")
            print(f"   选择器: {extractions['title']['selector']}")
            print(f"   内容: {extractions['title']['text']}")

        if extractions.get('date'):
            print(f"\n📅 日期:")
            print(f"   选择器: {extractions['date']['selector']}")
            print(f"   内容: {extractions['date']['text']}")

        if extractions.get('source'):
            print(f"\n📰 来源:")
            print(f"   选择器: {extractions['source']['selector']}")
            print(f"   内容: {extractions['source']['text']}")

        if extractions.get('content'):
            print(f"\n📄 内容:")
            print(f"   选择器: {extractions['content']['selector']}")
            print(f"   长度: {extractions['content']['length']} 字符")
            print(f"   元素数: {extractions['content']['elements_found']}")
            print(f"   预览: {extractions['content']['text']}")

        errors = result.get('errors', [])
        if errors:
            print(f"\n❌ 错误信息:")
            for error in errors:
                print(f"   - {error}")

        print(f"{'='*60}")

    def save_test_results(self, results: List[Dict[str, Any]], filename: str = None):
        """保存测试结果到JSON文件"""
        filename = filename or f"selector_test_results_{int(time.time())}.json"
        filepath = os.path.join(self.test_results_dir, filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"测试结果已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存测试结果失败: {e}")

    def generate_test_report(self, results: List[Dict[str, Any]]) -> str:
        """生成测试报告"""
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.get('success', False))
        failed_tests = total_tests - successful_tests

        report = f"""
选择器测试报告
{'='*60}
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
总测试数: {total_tests}
成功数: {successful_tests}
失败数: {failed_tests}
成功率: {(successful_tests/total_tests*100):.1f}%

详细结果:
{'-'*60}
"""

        for i, result in enumerate(results, 1):
            status = "✅ 成功" if result.get('success', False) else "❌ 失败"
            config_group = result.get('config_group', result.get('module_name', 'Unknown'))
            url = result.get('url', 'Unknown')

            report += f"{i}. {status} - {config_group}\n"
            report += f"   URL: {url}\n"

            extractions = result.get('extractions', {})
            if extractions.get('content'):
                report += f"   内容: ✅ ({extractions['content']['length']} 字符)\n"
            else:
                report += f"   内容: ❌\n"

            if extractions.get('title'):
                report += f"   标题: ✅ {extractions['title']['text'][:50]}...\n"
            else:
                report += f"   标题: ❌\n"

            errors = result.get('errors', [])
            if errors:
                report += f"   错误: {len(errors)} 个\n"

            report += "\n"

        return report


# 快速测试用例
QUICK_TEST_URLS = {
    "微信公众号": "https://mp.weixin.qq.com/s/VILTK648NrlWPaMpi8OVnw",
    "上海人大": "https://www.shrd.gov.cn/n8347/n8378/u1ai270696.html",
    "北京人大": "https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/202412/t20241206_3815513.html"
}


async def test_all_configs():
    """测试所有基本配置组"""
    test_manager = SelectorsTestManager()

    print("🚀 开始测试所有基本配置组...")
    print("="*60)

    results = await test_manager.test_all_config_groups()

    # 显示测试报告
    report = test_manager.generate_test_report(results)
    print(report)

    # 保存测试结果
    test_manager.save_test_results(results, "all_config_groups_test.json")

    return results

async def quick_test():
    """快速测试常见网站"""
    test_manager = SelectorsTestManager()

    print("🚀 快速测试常见网站...")
    print("="*60)

    for site_name, url in QUICK_TEST_URLS.items():
        print(f"\n测试 {site_name}:")
        print("-" * 40)

        result = await test_manager.quick_test_url(url, show_preview=True)

        await asyncio.sleep(1)  # 避免请求过于频繁

    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证配置是否有效

        Args:
            config: 要验证的配置

        Returns:
            配置是否有效
        """
        required_fields = ['name', 'url', 'selectors']

        # 检查必需字段
        for field in required_fields:
            if field not in config:
                return False

        # 检查选择器配置
        selectors = config.get('selectors', {})
        if not isinstance(selectors, dict):
            return False

        # 检查基本选择器
        if 'container' not in selectors or 'item' not in selectors:
            return False

        return True


async def interactive_test():
    """交互式测试模式"""
    test_manager = SelectorsTestManager()

    print("🔧 选择器交互式测试工具")
    print("="*60)

    while True:
        print("\n请选择测试模式:")
        print("1. 快速测试单个URL")
        print("2. 测试指定配置组")
        print("3. 测试所有配置组")
        print("4. 快速测试常见网站")
        print("5. 验证模组配置")
        print("6. 退出")

        choice = input("\n请输入选择 (1-6): ").strip()

        if choice == "1":
            url = input("请输入要测试的URL: ").strip()
            if url:
                print("\n开始测试...")
                result = await test_manager.quick_test_url(url, show_preview=True)

        elif choice == "2":
            if not test_manager.config_manager:
                print("❌ 配置管理器不可用")
                continue

            configs = test_manager.config_manager.get_groups()
            print(f"\n可用配置组: {', '.join(configs)}")

            config_name = input("请输入配置组名称: ").strip()
            if config_name in configs:
                test_url = input("请输入测试URL（留空使用配置组默认URL）: ").strip()
                test_url = test_url if test_url else None

                print("\n开始测试...")
                result = await test_manager.test_config_group(config_name, test_url)
                test_manager._show_extraction_preview(result)
            else:
                print("❌ 配置组不存在")

        elif choice == "3":
            print("\n开始测试所有配置组...")
            await test_all_configs()

        elif choice == "4":
            await quick_test()

        elif choice == "5":
            await verify_module_configs()

        elif choice == "6":
            print("退出测试工具")
            break

        else:
            print("❌ 无效选择，请重新输入")


async def verify_module_configs():
    """验证所有模组配置"""
    if not MODULE_MANAGER_AVAILABLE:
        print("❌ 模组管理器不可用")
        return

    print("\n验证模组配置...")
    print("="*50)

    # 获取所有模组
    modules = module_manager.list_modules()

    for module_name in modules:
        print(f"\n模组: {module_name}")
        print("-" * 30)

        module_info = module_manager.get_module_info(module_name)
        if not module_info:
            print("❌ 无法获取模组信息")
            continue

        # 检查域名模式
        domain_patterns = module_info.get('domain_patterns', [])
        url_patterns = module_info.get('url_patterns', [])
        config = module_info.get('config', {})

        print(f"域名模式: {domain_patterns}")
        print(f"URL模式: {url_patterns}")
        print(f"配置项: {list(config.keys())}")

        # 验证必要的配置项
        required_fields = ['content_selectors', 'title_selectors']
        missing_fields = [field for field in required_fields if field not in config]

        if missing_fields:
            print(f"⚠️  缺少必要配置项: {missing_fields}")
        else:
            print("✅ 配置完整")


def create_test_config_template():
    """创建测试配置模板"""
    template = {
        "test_name": "示例测试",
        "description": "这是一个测试配置模板",
        "urls": [
            "https://example.com/article1",
            "https://example.com/article2"
        ],
        "selectors_config": {
            "content_selectors": [
                ".article-content",
                ".post-content",
                "#main-content"
            ],
            "title_selectors": [
                "h1.title",
                ".article-title",
                "#post-title"
            ],
            "date_selectors": [
                ".publish-date",
                ".post-date"
            ],
            "source_selectors": [
                ".author",
                ".source"
            ],
            "content_type": "CSS",
            "mode": "safe",
            "collect_links": True,
            "retry": 2,
            "interval": 1,
            "use_module_config": False
        }
    }

    template_file = "test_config_template.json"
    try:
        with open(template_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, ensure_ascii=False, indent=2)
        print(f"✅ 测试配置模板已创建: {template_file}")
    except Exception as e:
        print(f"❌ 创建模板失败: {e}")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "interactive":
            asyncio.run(interactive_test())
        elif command == "all":
            asyncio.run(test_all_configs())
        elif command == "quick":
            asyncio.run(quick_test())
        elif command == "verify":
            asyncio.run(verify_module_configs())
        else:
            print("🔧 选择器测试工具")
            print("="*40)
            print("可用命令:")
            print("  python selectors_test.py interactive  - 交互式测试")
            print("  python selectors_test.py all         - 测试所有配置组")
            print("  python selectors_test.py quick       - 快速测试常见网站")
            print("  python selectors_test.py verify      - 验证模组配置")
    else:
        # 默认运行交互式测试
        asyncio.run(interactive_test())
