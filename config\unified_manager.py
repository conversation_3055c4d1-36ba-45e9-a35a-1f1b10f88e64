#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置管理器
管理所有配置文件的加载和保存
"""

import os
import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class UnifiedConfigManager:
    """统一配置管理器"""
    
    def __init__(self):
        self.config_base_dir = "configs"
        self.configs = {}
        self.load_all_configs()
    
    def get_config_path(self, config_type: str, config_name: str) -> str:
        """获取配置文件路径"""
        return os.path.join(self.config_base_dir, config_type, config_name)
    
    def load_config(self, config_type: str, config_name: str) -> Dict[str, Any]:
        """加载指定配置文件"""
        config_path = self.get_config_path(config_type, config_name)
        
        if not os.path.exists(config_path):
            logger.warning(f"配置文件不存在: {config_path}")
            return {}
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"加载配置文件: {config_path}")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败 {config_path}: {e}")
            return {}
    
    def save_config(self, config_type: str, config_name: str, config_data: Dict[str, Any]) -> bool:
        """保存配置文件"""
        config_path = self.get_config_path(config_type, config_name)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            logger.info(f"保存配置文件: {config_path}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败 {config_path}: {e}")
            return False
    
    def load_all_configs(self):
        """加载所有配置文件"""
        config_types = ['app', 'modules', 'ai', 'crawler', 'testing']
        
        for config_type in config_types:
            config_dir = os.path.join(self.config_base_dir, config_type)
            if os.path.exists(config_dir):
                for file_name in os.listdir(config_dir):
                    if file_name.endswith('.json'):
                        config_key = f"{config_type}.{file_name}"
                        self.configs[config_key] = self.load_config(config_type, file_name)
    
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用配置"""
        return self.configs.get('app.config.json', {})
    
    def get_module_configs(self) -> Dict[str, Any]:
        """获取模组配置"""
        return self.configs.get('modules.module_configs.json', {})
    
    def get_ai_config(self) -> Dict[str, Any]:
        """获取AI配置"""
        return self.configs.get('ai.llm_config.json', {})
    
    def get_crawler_config(self) -> Dict[str, Any]:
        """获取爬虫配置"""
        return self.configs.get('crawler.crawler_config.json', {})
    
    def backup_config(self, config_type: str, config_name: str) -> bool:
        """备份配置文件"""
        import datetime
        
        source_path = self.get_config_path(config_type, config_name)
        if not os.path.exists(source_path):
            return False
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{config_name}.{timestamp}.backup"
        backup_path = self.get_config_path('backup', backup_name)
        
        try:
            shutil.copy2(source_path, backup_path)
            logger.info(f"备份配置文件: {source_path} -> {backup_path}")
            return True
        except Exception as e:
            logger.error(f"备份配置文件失败: {e}")
            return False

# 全局配置管理器实例
unified_config_manager = UnifiedConfigManager()
