#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字段选择窗口的新布局
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_field_layout():
    """测试字段布局"""
    print("=" * 60)
    print("测试字段选择窗口布局")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        print("🚀 创建主窗口...")
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 检查字段选择相关组件
        print("\n🔍 检查字段选择组件:")
        
        # 检查字段复选框
        if hasattr(window, 'field_checkboxes'):
            checkbox_count = len(window.field_checkboxes)
            print(f"📝 字段复选框数量: {checkbox_count}")
            
            if checkbox_count > 0:
                # 显示字段分类
                basic_fields = []
                extended_fields = []
                
                try:
                    from core.field_config_manager import get_field_config_manager
                    manager = get_field_config_manager()
                    default_fields = manager.config_data.get("default_fields", {})
                    extended_field_configs = manager.config_data.get("extended_fields", {})
                    
                    for field_name in window.field_checkboxes.keys():
                        if field_name in default_fields:
                            basic_fields.append(field_name)
                        elif field_name in extended_field_configs:
                            extended_fields.append(field_name)
                    
                    print(f"   📁 基础字段 ({len(basic_fields)}个): {basic_fields}")
                    print(f"   🔧 扩展字段 ({len(extended_fields)}个): {extended_fields}")
                    
                except Exception as e:
                    print(f"   ⚠️ 无法获取字段分类信息: {e}")
        else:
            print("❌ 字段复选框不存在")
        
        # 检查布局组件
        layout_components = [
            ('fields_scroll_area', '字段滚动区域'),
            ('fields_scroll_layout', '字段网格布局'),
            ('current_fields_text', '当前字段预览'),
            ('field_count_label', '字段数量标签')
        ]
        
        print("\n🔍 检查布局组件:")
        for attr_name, display_name in layout_components:
            if hasattr(window, attr_name):
                component = getattr(window, attr_name)
                print(f"   ✅ {display_name}: 存在")
                
                # 检查特定属性
                if attr_name == 'current_fields_text':
                    max_height = component.maximumHeight()
                    print(f"      最大高度: {max_height}px")
                elif attr_name == 'fields_scroll_layout':
                    spacing = component.spacing()
                    print(f"      间距: {spacing}px")
            else:
                print(f"   ❌ {display_name}: 不存在")
        
        # 测试字段选择功能
        print("\n🧪 测试字段选择功能:")
        
        # 启用字段配置
        if hasattr(window, 'enable_field_config_checkbox'):
            window.enable_field_config_checkbox.setChecked(True)
            print("   ✅ 已启用字段配置")
            
            # 检查字段复选框是否可用
            if hasattr(window, 'field_checkboxes') and window.field_checkboxes:
                first_field = list(window.field_checkboxes.keys())[0]
                first_checkbox = window.field_checkboxes[first_field]
                is_enabled = first_checkbox.isEnabled()
                print(f"   字段复选框状态: {'✅ 可用' if is_enabled else '❌ 禁用'}")
                
                # 测试勾选字段
                first_checkbox.setChecked(True)
                is_checked = first_checkbox.isChecked()
                print(f"   字段勾选测试: {'✅ 成功' if is_checked else '❌ 失败'}")
            else:
                print("   ⚠️ 没有字段复选框可测试")
        else:
            print("   ❌ 字段配置开关不存在")
        
        # 检查窗口尺寸
        print(f"\n📐 窗口信息:")
        window_size = window.size()
        print(f"   窗口尺寸: {window_size.width()} x {window_size.height()}")
        
        if hasattr(window, 'fields_sub_tab_widget'):
            tab_count = window.fields_sub_tab_widget.count()
            print(f"   字段标签页数量: {tab_count}")
            
            for i in range(tab_count):
                tab_text = window.fields_sub_tab_widget.tabText(i)
                print(f"      标签页 {i+1}: {tab_text}")
        
        print("\n✅ 字段布局测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试字段选择窗口布局")
    print()
    
    result = test_field_layout()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if result:
        print("🎉 字段布局测试通过！")
        print("\n💡 布局改进:")
        print("   ✅ 字段复选框从3列增加到4列")
        print("   ✅ 简化字段显示文本，只显示中文名")
        print("   ✅ 详细信息移到工具提示中")
        print("   ✅ 添加滚动区域，限制高度为300px")
        print("   ✅ 减少间距和边距，更紧凑")
        print("   ✅ 字段预览高度从100px减少到80px")
        print("   ✅ 添加图标区分基础字段和扩展字段")
    else:
        print("❌ 字段布局测试失败")

if __name__ == "__main__":
    main()
