#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的翻页问题 - 针对有翻页按钮但点击不了的情况
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright
from core.crawler import launch_browser
from core.PaginationHandler import PaginationHandler

async def test_real_pagination_issue():
    """测试真实的翻页问题"""
    print("🧪 测试真实的翻页问题")
    print("=" * 60)
    
    url = "https://www.tjszx.gov.cn/tagz/taxd/index.shtml"
    
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            print(f"📋 访问网站: {url}")
            await page.goto(url, timeout=30000)
            await page.wait_for_load_state('networkidle', timeout=15000)
            
            print("✅ 页面加载完成")
            
            # 等待页面完全渲染
            await page.wait_for_timeout(3000)
            
            # 查找所有可能的翻页相关元素
            print("\n🔍 查找翻页相关元素...")
            
            # 查找包含"下一页"文本的元素
            next_page_elements = await page.query_selector_all("*")
            found_next_buttons = []
            
            for element in next_page_elements:
                try:
                    text = await element.text_content()
                    if text and "下一页" in text:
                        tag_name = await element.evaluate("el => el.tagName")
                        class_name = await element.get_attribute('class')
                        id_attr = await element.get_attribute('id')
                        onclick = await element.get_attribute('onclick')
                        href = await element.get_attribute('href')
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()
                        
                        found_next_buttons.append({
                            'element': element,
                            'text': text.strip(),
                            'tag': tag_name,
                            'class': class_name,
                            'id': id_attr,
                            'onclick': onclick,
                            'href': href,
                            'visible': is_visible,
                            'enabled': is_enabled
                        })
                        
                        print(f"  ✅ 找到'下一页'元素:")
                        print(f"    标签: {tag_name}")
                        print(f"    文本: '{text.strip()}'")
                        print(f"    class: '{class_name}'")
                        print(f"    id: '{id_attr}'")
                        print(f"    onclick: '{onclick}'")
                        print(f"    href: '{href}'")
                        print(f"    可见: {is_visible}")
                        print(f"    可用: {is_enabled}")
                        print()
                        
                except Exception as e:
                    continue
            
            # 查找页码相关元素
            print("🔍 查找页码相关元素...")
            page_info_elements = await page.query_selector_all("*")
            
            for element in page_info_elements:
                try:
                    text = await element.text_content()
                    if text and ("页" in text and "/" in text):
                        print(f"  📋 页码信息: '{text.strip()}'")
                        
                        # 获取父元素和兄弟元素
                        parent = await element.evaluate("el => el.parentElement")
                        if parent:
                            parent_html = await parent.evaluate("el => el.outerHTML")
                            print(f"  📋 父元素HTML: {parent_html[:200]}...")
                        break
                except Exception:
                    continue
            
            # 尝试不同的翻页按钮选择器
            print("\n🎯 测试不同的翻页按钮选择器...")
            
            test_selectors = [
                "input[value='下一页']",
                "button:has-text('下一页')",
                "a:has-text('下一页')",
                "[onclick*='下一页']",
                "[onclick*='next']",
                "[onclick*='page']",
                "[onclick*='Page']",
                "input[type='button'][value='下一页']",
                "input[type='submit'][value='下一页']",
                ".pagination input[value='下一页']",
                "form input[value='下一页']"
            ]
            
            for selector in test_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"  ✅ 选择器 '{selector}' 找到 {len(elements)} 个元素")
                        
                        for i, elem in enumerate(elements):
                            is_visible = await elem.is_visible()
                            is_enabled = await elem.is_enabled()
                            print(f"    [{i+1}] 可见: {is_visible}, 可用: {is_enabled}")
                            
                            if is_visible and is_enabled:
                                print(f"    🎯 尝试点击元素 {i+1}...")
                                
                                # 获取点击前的URL
                                current_url = page.url
                                print(f"    📋 点击前URL: {current_url}")
                                
                                try:
                                    # 尝试点击
                                    await elem.click()
                                    print("    ✅ 点击成功")
                                    
                                    # 等待页面变化
                                    await page.wait_for_timeout(3000)
                                    
                                    # 检查URL是否改变
                                    new_url = page.url
                                    print(f"    📋 点击后URL: {new_url}")
                                    
                                    if new_url != current_url:
                                        print("    🎉 页面URL发生了变化，翻页成功！")
                                        
                                        # 检查页面内容是否变化
                                        page_text = await page.text_content("body")
                                        if "第2页" in page_text or "2/12" in page_text:
                                            print("    🎉 确认翻页成功，已到达第2页！")
                                        
                                        return True
                                    else:
                                        print("    ⚠️ URL没有变化，检查页面内容是否变化...")
                                        
                                        # 等待可能的AJAX加载
                                        await page.wait_for_timeout(2000)
                                        
                                        # 检查页面内容
                                        page_text = await page.text_content("body")
                                        if "第2页" in page_text or "2/12" in page_text:
                                            print("    🎉 AJAX翻页成功，已到达第2页！")
                                            return True
                                        else:
                                            print("    ❌ 页面内容没有变化")
                                    
                                except Exception as click_error:
                                    print(f"    ❌ 点击失败: {click_error}")
                    else:
                        print(f"  ❌ 选择器 '{selector}' 未找到元素")
                        
                except Exception as e:
                    print(f"  ❌ 测试选择器 '{selector}' 时出错: {e}")
            
            print("\n❌ 所有翻页尝试都失败了")
            return False
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            return False
            
        finally:
            # 保持浏览器打开一段时间，方便观察
            print("\n⏳ 保持浏览器打开10秒，方便观察...")
            await page.wait_for_timeout(10000)
            
            await context.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_real_pagination_issue())
