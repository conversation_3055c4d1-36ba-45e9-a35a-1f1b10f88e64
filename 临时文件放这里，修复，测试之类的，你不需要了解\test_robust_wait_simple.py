#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单稳定的健壮等待策略测试
专门用于测试选择器功能中的等待机制
"""

import asyncio
import sys
import os
import time
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_robust_wait_with_stable_browser():
    """使用稳定的浏览器配置测试健壮等待策略"""
    
    from playwright.async_api import async_playwright
    from utils.robust_wait_strategy import robust_goto, get_success_stats
    
    # 简单的测试URL列表
    test_urls = [
        "https://www.baidu.com",
        "https://httpbin.org/html",
        "https://www.gov.cn/",
    ]
    
    print("🧪 稳定的健壮等待策略测试")
    print("=" * 50)
    
    # 使用更稳定的浏览器配置
    async with async_playwright() as p:
        try:
            # 启动浏览器，使用更稳定的配置
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled'
                ]
            )
            
            print("✅ 浏览器启动成功")
            
            # 创建上下文
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                viewport={'width': 1280, 'height': 720},
                ignore_https_errors=True
            )
            
            print("✅ 浏览器上下文创建成功")
            
            results = []
            
            for i, url in enumerate(test_urls, 1):
                print(f"\n📋 测试 {i}/{len(test_urls)}: {url}")
                print("-" * 30)
                
                # 为每个测试创建新页面，避免页面状态问题
                page = await context.new_page()
                
                try:
                    start_time = time.time()
                    
                    # 使用健壮等待策略
                    logger.info(f"开始测试URL: {url}")
                    success = await robust_goto(page, url, timeout=30000)
                    
                    load_time = time.time() - start_time
                    
                    if success:
                        # 检查页面是否仍然有效
                        if not page.is_closed():
                            title = await page.title()
                            content = await page.content()
                            content_length = len(content)
                            
                            print(f"✅ 成功 - {load_time:.2f}s")
                            print(f"📄 标题: {title[:40]}...")
                            print(f"📊 内容: {content_length:,} 字符")
                            
                            results.append({
                                "url": url,
                                "success": True,
                                "load_time": load_time,
                                "title": title,
                                "content_length": content_length
                            })
                        else:
                            print(f"❌ 页面在成功后被关闭")
                            results.append({
                                "url": url,
                                "success": False,
                                "load_time": load_time,
                                "error": "页面被关闭"
                            })
                    else:
                        print(f"❌ 失败 - {load_time:.2f}s")
                        results.append({
                            "url": url,
                            "success": False,
                            "load_time": load_time,
                            "error": "所有等待策略失败"
                        })
                        
                except Exception as e:
                    load_time = time.time() - start_time
                    error_msg = str(e)
                    print(f"❌ 异常 - {load_time:.2f}s: {error_msg}")
                    results.append({
                        "url": url,
                        "success": False,
                        "load_time": load_time,
                        "error": error_msg
                    })
                
                finally:
                    # 确保页面被正确关闭
                    try:
                        if not page.is_closed():
                            await page.close()
                    except:
                        pass
                
                # 短暂休息
                await asyncio.sleep(1)
            
            # 关闭浏览器
            await context.close()
            await browser.close()
            print("\n✅ 浏览器已正确关闭")
            
            # 显示结果
            print(f"\n📊 测试结果:")
            success_count = sum(1 for r in results if r['success'])
            print(f"成功率: {success_count}/{len(results)}")
            
            for result in results:
                status = "✅" if result['success'] else "❌"
                print(f"{status} {result['url']}: {result.get('load_time', 0):.2f}s")
                if not result['success']:
                    print(f"   错误: {result.get('error', '未知错误')}")
            
            # 显示策略统计
            stats = get_success_stats()
            if stats:
                print(f"\n📈 策略统计:")
                for domain, strategies in stats.items():
                    print(f"🌐 {domain}:")
                    for strategy, data in strategies.items():
                        print(f"  {strategy}: {data['success_rate']}")
            
            return results
            
        except Exception as e:
            print(f"❌ 浏览器操作失败: {e}")
            import traceback
            traceback.print_exc()
            return []


async def test_single_url():
    """测试单个URL的详细过程"""
    from playwright.async_api import async_playwright
    from utils.robust_wait_strategy import robust_goto
    
    test_url = "https://www.baidu.com"
    
    print(f"\n🔍 详细测试单个URL: {test_url}")
    print("=" * 40)
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            print("📋 开始详细测试...")
            start_time = time.time()
            
            success = await robust_goto(page, test_url, timeout=20000)
            
            load_time = time.time() - start_time
            
            if success and not page.is_closed():
                title = await page.title()
                url = page.url
                content_length = len(await page.content())
                
                print(f"✅ 详细测试成功")
                print(f"⏱️  加载时间: {load_time:.2f}s")
                print(f"📄 页面标题: {title}")
                print(f"🔗 最终URL: {url}")
                print(f"📊 内容长度: {content_length:,} 字符")
                
                # 测试一些基本元素
                try:
                    search_box = await page.query_selector("#kw")
                    search_btn = await page.query_selector("#su")
                    print(f"🔍 搜索框: {'✅找到' if search_box else '❌未找到'}")
                    print(f"🔍 搜索按钮: {'✅找到' if search_btn else '❌未找到'}")
                except Exception as e:
                    print(f"🔍 元素测试失败: {e}")
                
            else:
                print(f"❌ 详细测试失败 - {load_time:.2f}s")
                if page.is_closed():
                    print("   原因: 页面被关闭")
                
        except Exception as e:
            print(f"❌ 详细测试异常: {e}")
        
        finally:
            await browser.close()


async def main():
    """主函数"""
    print("🚀 健壮等待策略稳定性测试")
    print("专门用于解决选择器测试中的页面关闭问题")
    print()
    
    try:
        # 主要稳定性测试
        await test_robust_wait_with_stable_browser()
        
        # 单个URL详细测试
        await test_single_url()
        
        print(f"\n🎉 所有测试完成！")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
