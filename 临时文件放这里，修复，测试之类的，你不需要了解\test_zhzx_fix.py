#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试珠海政协网站修复效果
"""

import asyncio
import sys
import os
from playwright.async_api import async_playwright

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_zhzx_content_extraction():
    """测试珠海政协网站内容提取"""
    
    url = "http://www.zhzx.gov.cn/zxyw/202306/t20230609_58927130.html"
    
    # 测试的选择器列表（按优先级排序）
    content_selectors = [
        ".TRS_Editor",
        "div.view.TRS_UEDITOR.trs_paper_default.trs_web", 
        ".article_cont",
        "div[class*='content']",
        "div.article_con",
        "div.zhengwen"
    ]
    
    print(f"🔍 测试URL: {url}")
    print("=" * 80)
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            # 访问页面
            await page.goto(url, timeout=30000)
            await page.wait_for_load_state('networkidle')
            
            print("✅ 页面加载成功")
            
            # 测试每个选择器
            for i, selector in enumerate(content_selectors, 1):
                print(f"\n{i}️⃣ 测试选择器: {selector}")
                
                try:
                    # 查找元素
                    element = await page.query_selector(selector)
                    
                    if element:
                        # 获取HTML内容
                        html_content = await element.inner_html()
                        
                        # 使用我们的清理函数
                        from utils.text_cleaner import clean_html_before_text_extraction
                        cleaned_html = clean_html_before_text_extraction(html_content)
                        
                        # 提取文本
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(cleaned_html, 'html.parser')
                        text_content = soup.get_text(separator='\n', strip=True)
                        
                        print(f"   ✅ 找到元素")
                        print(f"   📄 原始HTML长度: {len(html_content)} 字符")
                        print(f"   🧹 清理后HTML长度: {len(cleaned_html)} 字符")
                        print(f"   📝 提取文本长度: {len(text_content)} 字符")
                        
                        # 检查是否包含样式内容
                        if 'TRS_Editor' in text_content:
                            print(f"   ⚠️ 仍包含样式内容")
                            # 找到样式内容的行
                            style_lines = [line for line in text_content.split('\n') if 'TRS_Editor' in line]
                            print(f"   📊 样式行数: {len(style_lines)}")
                            for line in style_lines[:2]:  # 只显示前2行
                                print(f"      {line[:100]}...")
                        else:
                            print(f"   ✅ 无样式内容")
                        
                        # 显示内容预览
                        if text_content:
                            preview = text_content[:200].replace('\n', ' ')
                            print(f"   📖 内容预览: {preview}...")
                            
                            # 如果内容足够长且无样式内容，这个选择器就是最佳的
                            if len(text_content) > 500 and 'TRS_Editor' not in text_content:
                                print(f"   🎯 推荐使用此选择器")
                                break
                        else:
                            print(f"   ❌ 提取的文本为空")
                    else:
                        print(f"   ❌ 未找到元素")
                        
                except Exception as e:
                    print(f"   ❌ 处理失败: {e}")
            
            # 测试旧的问题选择器
            print(f"\n🔍 测试问题选择器: div.article_con p")
            try:
                elements = await page.query_selector_all("div.article_con p")
                print(f"   📊 找到 {len(elements)} 个p元素")
                
                total_text = ""
                for j, elem in enumerate(elements):
                    text = await elem.text_content()
                    if text:
                        total_text += text + "\n"
                        if 'TRS_Editor' in text:
                            print(f"   ⚠️ 第{j+1}个p元素包含样式内容: {text[:100]}...")
                
                print(f"   📝 总文本长度: {len(total_text)} 字符")
                if 'TRS_Editor' in total_text:
                    print(f"   ❌ 确认包含样式内容（这就是问题所在）")
                else:
                    print(f"   ✅ 不包含样式内容")
                    
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
            
        finally:
            await browser.close()
    
    print("\n" + "=" * 80)
    print("🎯 结论:")
    print("1. 使用 .TRS_Editor 选择器可以准确定位正文内容")
    print("2. 配合 clean_html_before_text_extraction 函数可以完全清除样式内容")
    print("3. 避免使用 div.article_con p 这样的宽泛选择器")
    print("4. 配置文件已更新为使用更精确的选择器列表")

if __name__ == "__main__":
    asyncio.run(test_zhzx_content_extraction())
