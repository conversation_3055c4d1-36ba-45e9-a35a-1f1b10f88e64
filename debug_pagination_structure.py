#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试合肥政协网站的翻页结构
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright

async def debug_pagination_structure():
    """调试翻页结构"""
    print("🔍 调试合肥政协网站翻页结构...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 显示浏览器便于观察
        page = await browser.new_page()
        
        try:
            await page.goto(test_url, wait_until='networkidle')
            print(f"✅ 成功访问页面")
            
            # 1. 检查页面中所有的链接
            print(f"\n📋 页面中所有包含翻页相关文本的链接:")
            all_links = await page.evaluate("""
                () => {
                    const links = [];
                    document.querySelectorAll('a').forEach((link, index) => {
                        const text = link.textContent.trim();
                        const href = link.getAttribute('href') || '';
                        const onclick = link.getAttribute('onclick') || '';
                        const className = link.className || '';
                        
                        // 查找可能的翻页链接
                        if (text.includes('下一页') || text.includes('下页') || text === '>' || 
                            text.includes('Next') || text.includes('next') ||
                            href.includes('javascript:') || onclick.includes('goto') ||
                            /^\\d+$/.test(text) || text.includes('页')) {
                            links.push({
                                index: index,
                                text: text,
                                href: href,
                                onclick: onclick,
                                className: className,
                                outerHTML: link.outerHTML
                            });
                        }
                    });
                    return links;
                }
            """)
            
            for i, link in enumerate(all_links):
                print(f"   [{i+1}] 文本: '{link['text']}'")
                print(f"       href: '{link['href']}'")
                print(f"       onclick: '{link['onclick']}'")
                print(f"       class: '{link['className']}'")
                print(f"       HTML: {link['outerHTML'][:100]}...")
                print()
            
            # 2. 检查JavaScript函数
            print(f"🔧 检查页面中的JavaScript翻页函数:")
            js_functions = await page.evaluate("""
                () => {
                    const functions = {};
                    
                    // 检查常见函数
                    if (typeof goto === 'function') {
                        functions.goto = {
                            available: true,
                            source: goto.toString().substring(0, 200)
                        };
                    }
                    if (typeof nextPage === 'function') {
                        functions.nextPage = {
                            available: true,
                            source: nextPage.toString().substring(0, 200)
                        };
                    }
                    if (typeof goPage === 'function') {
                        functions.goPage = {
                            available: true,
                            source: goPage.toString().substring(0, 200)
                        };
                    }
                    
                    // 检查window对象中的其他函数
                    for (let prop in window) {
                        if (typeof window[prop] === 'function' && 
                            (prop.toLowerCase().includes('page') || 
                             prop.toLowerCase().includes('goto') ||
                             prop.toLowerCase().includes('next'))) {
                            functions[prop] = {
                                available: true,
                                source: window[prop].toString().substring(0, 200)
                            };
                        }
                    }
                    
                    return functions;
                }
            """)
            
            for func_name, info in js_functions.items():
                print(f"   - {func_name}: {info['available']}")
                print(f"     源码: {info['source']}...")
                print()
            
            # 3. 检查页面源码中的翻页相关内容
            print(f"📄 检查页面源码中的翻页相关内容:")
            page_content = await page.content()
            
            # 查找可能的翻页相关代码
            import re
            
            # 查找JavaScript函数定义
            js_patterns = [
                r'function\s+(\w*(?:page|goto|next)\w*)\s*\([^)]*\)',
                r'(\w*(?:page|goto|next)\w*)\s*=\s*function',
                r'javascript:\s*(\w+)\s*\(',
            ]
            
            for pattern in js_patterns:
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                if matches:
                    print(f"   找到函数模式 '{pattern}': {matches}")
            
            # 查找翻页链接模式
            link_patterns = [
                r'<a[^>]*href=["\']javascript:([^"\']*)["\'][^>]*>([^<]*)</a>',
                r'<a[^>]*onclick=["\']([^"\']*)["\'][^>]*>([^<]*)</a>',
            ]
            
            for pattern in link_patterns:
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                for match in matches[:5]:  # 只显示前5个
                    print(f"   找到链接模式: {match}")
            
            # 4. 尝试手动点击可能的翻页元素
            print(f"\n🖱️ 尝试手动点击翻页元素:")
            
            # 尝试不同的选择器
            test_selectors = [
                'a:has-text("下一页")',
                'a:has-text("下页")', 
                'a:has-text(">")',
                'a[href*="javascript:goto"]',
                'a[onclick*="goto"]',
                'a[href*="javascript:nextPage"]',
                'a[onclick*="nextPage"]',
                'a[href*="javascript:"]',
                'a[onclick*="javascript:"]'
            ]
            
            for selector in test_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"   ✅ 找到元素: {selector} ({len(elements)} 个)")
                        for i, elem in enumerate(elements):
                            text = await elem.text_content()
                            href = await elem.get_attribute('href')
                            onclick = await elem.get_attribute('onclick')
                            print(f"      [{i+1}] 文本: '{text}', href: '{href}', onclick: '{onclick}'")
                    else:
                        print(f"   ❌ 未找到: {selector}")
                except Exception as e:
                    print(f"   ❌ 错误: {selector} - {e}")
            
            # 5. 等待用户观察
            print(f"\n⏳ 页面已打开，请手动观察翻页结构...")
            print(f"   页面URL: {page.url}")
            print(f"   按 Enter 继续...")
            input()
            
        except Exception as e:
            print(f"❌ 调试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_pagination_structure())
